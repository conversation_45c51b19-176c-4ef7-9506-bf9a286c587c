{"buildFiles": ["C:\\android\\flutter\\packages\\flutter_tools\\gradle\\src\\main\\groovy\\CMakeLists.txt"], "cleanCommandsComponents": [["C:\\android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\flutter\\flutter_almashal_family\\android\\app\\.cxx\\RelWithDebInfo\\e3h6l2t6\\arm64-v8a", "clean"]], "buildTargetsCommandComponents": ["C:\\android\\sdk\\cmake\\3.22.1\\bin\\ninja.exe", "-C", "C:\\Users\\<USER>\\Desktop\\flutter\\flutter_almashal_family\\android\\app\\.cxx\\RelWithDebInfo\\e3h6l2t6\\arm64-v8a", "{LIST_OF_TARGETS_TO_BUILD}"], "libraries": {}}
PODS:
  - connectivity_plus (0.0.1):
    - FlutterMacOS
    - ReachabilitySwift
  - Firebase/Analytics (10.20.0):
    - Firebase/Core
  - Firebase/Core (10.20.0):
    - Firebase/CoreOnly
    - FirebaseAnalytics (~> 10.20.0)
  - Firebase/CoreOnly (10.20.0):
    - FirebaseCore (= 10.20.0)
  - Firebase/Crashlytics (10.20.0):
    - Firebase/CoreOnly
    - FirebaseCrashlytics (~> 10.20.0)
  - Firebase/Messaging (10.20.0):
    - Firebase/CoreOnly
    - FirebaseMessaging (~> 10.20.0)
  - firebase_analytics (10.8.5):
    - Firebase/Analytics (= 10.20.0)
    - firebase_core
    - FlutterMacOS
  - firebase_core (2.25.4):
    - Firebase/CoreOnly (~> 10.20.0)
    - FlutterMacOS
  - firebase_crashlytics (3.4.14):
    - Firebase/CoreOnly (~> 10.20.0)
    - Firebase/Crashlytics (~> 10.20.0)
    - firebase_core
    - FlutterMacOS
  - firebase_messaging (14.7.15):
    - Firebase/CoreOnly (~> 10.20.0)
    - Firebase/Messaging (~> 10.20.0)
    - firebase_core
    - FlutterMacOS
  - FirebaseAnalytics (10.20.0):
    - FirebaseAnalytics/AdIdSupport (= 10.20.0)
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseAnalytics/AdIdSupport (10.20.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleAppMeasurement (= 10.20.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseCore (10.20.0):
    - FirebaseCoreInternal (~> 10.0)
    - GoogleUtilities/Environment (~> 7.12)
    - GoogleUtilities/Logger (~> 7.12)
  - FirebaseCoreExtension (10.20.0):
    - FirebaseCore (~> 10.0)
  - FirebaseCoreInternal (10.20.0):
    - "GoogleUtilities/NSData+zlib (~> 7.8)"
  - FirebaseCrashlytics (10.20.0):
    - FirebaseCore (~> 10.5)
    - FirebaseInstallations (~> 10.0)
    - FirebaseSessions (~> 10.5)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.8)
    - nanopb (< 2.30910.0, >= 2.30908.0)
    - PromisesObjC (~> 2.1)
  - FirebaseInstallations (10.20.0):
    - FirebaseCore (~> 10.0)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - PromisesObjC (~> 2.1)
  - FirebaseMessaging (10.20.0):
    - FirebaseCore (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.3)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.8)
    - GoogleUtilities/Environment (~> 7.8)
    - GoogleUtilities/Reachability (~> 7.8)
    - GoogleUtilities/UserDefaults (~> 7.8)
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - FirebaseSessions (10.20.0):
    - FirebaseCore (~> 10.5)
    - FirebaseCoreExtension (~> 10.0)
    - FirebaseInstallations (~> 10.0)
    - GoogleDataTransport (~> 9.2)
    - GoogleUtilities/Environment (~> 7.10)
    - nanopb (< 2.30910.0, >= 2.30908.0)
    - PromisesSwift (~> 2.1)
  - flutter_secure_storage_macos (6.1.1):
    - FlutterMacOS
  - FlutterMacOS (1.0.0)
  - GoogleAppMeasurement (10.20.0):
    - GoogleAppMeasurement/AdIdSupport (= 10.20.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleAppMeasurement/AdIdSupport (10.20.0):
    - GoogleAppMeasurement/WithoutAdIdSupport (= 10.20.0)
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleAppMeasurement/WithoutAdIdSupport (10.20.0):
    - GoogleUtilities/AppDelegateSwizzler (~> 7.11)
    - GoogleUtilities/MethodSwizzler (~> 7.11)
    - GoogleUtilities/Network (~> 7.11)
    - "GoogleUtilities/NSData+zlib (~> 7.11)"
    - nanopb (< 2.30910.0, >= 2.30908.0)
  - GoogleDataTransport (9.3.0):
    - GoogleUtilities/Environment (~> 7.7)
    - nanopb (< 2.30910.0, >= 2.30908.0)
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/AppDelegateSwizzler (7.12.0):
    - GoogleUtilities/Environment
    - GoogleUtilities/Logger
    - GoogleUtilities/Network
  - GoogleUtilities/Environment (7.12.0):
    - PromisesObjC (< 3.0, >= 1.2)
  - GoogleUtilities/Logger (7.12.0):
    - GoogleUtilities/Environment
  - GoogleUtilities/MethodSwizzler (7.12.0):
    - GoogleUtilities/Logger
  - GoogleUtilities/Network (7.12.0):
    - GoogleUtilities/Logger
    - "GoogleUtilities/NSData+zlib"
    - GoogleUtilities/Reachability
  - "GoogleUtilities/NSData+zlib (7.12.0)"
  - GoogleUtilities/Reachability (7.12.0):
    - GoogleUtilities/Logger
  - GoogleUtilities/UserDefaults (7.12.0):
    - GoogleUtilities/Logger
  - nanopb (2.30909.1):
    - nanopb/decode (= 2.30909.1)
    - nanopb/encode (= 2.30909.1)
  - nanopb/decode (2.30909.1)
  - nanopb/encode (2.30909.1)
  - package_info_plus (0.0.1):
    - FlutterMacOS
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - platform_device_id (0.0.1):
    - FlutterMacOS
  - platform_device_id_macos (0.0.1):
    - FlutterMacOS
  - PromisesObjC (2.3.1)
  - PromisesSwift (2.3.1):
    - PromisesObjC (= 2.3.1)
  - ReachabilitySwift (5.0.0)
  - share_plus (0.0.1):
    - FlutterMacOS
  - sqflite (0.0.3):
    - Flutter
    - FlutterMacOS
  - url_launcher_macos (0.0.1):
    - FlutterMacOS
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - wakelock_plus (0.0.1):
    - FlutterMacOS

DEPENDENCIES:
  - connectivity_plus (from `Flutter/ephemeral/.symlinks/plugins/connectivity_plus/macos`)
  - firebase_analytics (from `Flutter/ephemeral/.symlinks/plugins/firebase_analytics/macos`)
  - firebase_core (from `Flutter/ephemeral/.symlinks/plugins/firebase_core/macos`)
  - firebase_crashlytics (from `Flutter/ephemeral/.symlinks/plugins/firebase_crashlytics/macos`)
  - firebase_messaging (from `Flutter/ephemeral/.symlinks/plugins/firebase_messaging/macos`)
  - flutter_secure_storage_macos (from `Flutter/ephemeral/.symlinks/plugins/flutter_secure_storage_macos/macos`)
  - FlutterMacOS (from `Flutter/ephemeral`)
  - package_info_plus (from `Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos`)
  - path_provider_foundation (from `Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin`)
  - platform_device_id (from `Flutter/ephemeral/.symlinks/plugins/platform_device_id/macos`)
  - platform_device_id_macos (from `Flutter/ephemeral/.symlinks/plugins/platform_device_id_macos/macos`)
  - share_plus (from `Flutter/ephemeral/.symlinks/plugins/share_plus/macos`)
  - sqflite (from `Flutter/ephemeral/.symlinks/plugins/sqflite/darwin`)
  - url_launcher_macos (from `Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos`)
  - video_player_avfoundation (from `Flutter/ephemeral/.symlinks/plugins/video_player_avfoundation/darwin`)
  - wakelock_plus (from `Flutter/ephemeral/.symlinks/plugins/wakelock_plus/macos`)

SPEC REPOS:
  trunk:
    - Firebase
    - FirebaseAnalytics
    - FirebaseCore
    - FirebaseCoreExtension
    - FirebaseCoreInternal
    - FirebaseCrashlytics
    - FirebaseInstallations
    - FirebaseMessaging
    - FirebaseSessions
    - GoogleAppMeasurement
    - GoogleDataTransport
    - GoogleUtilities
    - nanopb
    - PromisesObjC
    - PromisesSwift
    - ReachabilitySwift

EXTERNAL SOURCES:
  connectivity_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/connectivity_plus/macos
  firebase_analytics:
    :path: Flutter/ephemeral/.symlinks/plugins/firebase_analytics/macos
  firebase_core:
    :path: Flutter/ephemeral/.symlinks/plugins/firebase_core/macos
  firebase_crashlytics:
    :path: Flutter/ephemeral/.symlinks/plugins/firebase_crashlytics/macos
  firebase_messaging:
    :path: Flutter/ephemeral/.symlinks/plugins/firebase_messaging/macos
  flutter_secure_storage_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/flutter_secure_storage_macos/macos
  FlutterMacOS:
    :path: Flutter/ephemeral
  package_info_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/package_info_plus/macos
  path_provider_foundation:
    :path: Flutter/ephemeral/.symlinks/plugins/path_provider_foundation/darwin
  platform_device_id:
    :path: Flutter/ephemeral/.symlinks/plugins/platform_device_id/macos
  platform_device_id_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/platform_device_id_macos/macos
  share_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/share_plus/macos
  sqflite:
    :path: Flutter/ephemeral/.symlinks/plugins/sqflite/darwin
  url_launcher_macos:
    :path: Flutter/ephemeral/.symlinks/plugins/url_launcher_macos/macos
  video_player_avfoundation:
    :path: Flutter/ephemeral/.symlinks/plugins/video_player_avfoundation/darwin
  wakelock_plus:
    :path: Flutter/ephemeral/.symlinks/plugins/wakelock_plus/macos

SPEC CHECKSUMS:
  connectivity_plus: 18d3c32514c886e046de60e9c13895109866c747
  Firebase: 10c8cb12fb7ad2ae0c09ffc86cd9c1ab392a0031
  firebase_analytics: 6637f2e65bef4f7c02dedb2c7c37a440b2a61784
  firebase_core: 2e1a33fd13fb581f0dc809c18be25cdc1a2e10db
  firebase_crashlytics: c8d9eb8c32ad6248e9be271d467d85b96da98fe9
  firebase_messaging: aa7d68aa238b24ee36bfe33f7a73561d3a78b069
  FirebaseAnalytics: a2731bf3670747ce8f65368b118d18aa8e368246
  FirebaseCore: 28045c1560a2600d284b9c45a904fe322dc890b6
  FirebaseCoreExtension: 0659f035b88c5a7a15a9763c48c2e6ca8c0a2977
  FirebaseCoreInternal: efeeb171ac02d623bdaefe121539939821e10811
  FirebaseCrashlytics: 81530595edb6d99f1918f723a6c33766a24a4c86
  FirebaseInstallations: 558b1da7d65afeb996fd5c814332f013234ece4e
  FirebaseMessaging: 06c414a21b122396a26847c523d5c370f8325df5
  FirebaseSessions: 2f348975f6d1c139231c180e12194161da2e0cd6
  flutter_secure_storage_macos: d56e2d218c1130b262bef8b4a7d64f88d7f9c9ea
  FlutterMacOS: 8f6f14fa908a6fb3fba0cd85dbd81ec4b251fb24
  GoogleAppMeasurement: bb3c564c3efb933136af0e94899e0a46167466a8
  GoogleDataTransport: 57c22343ab29bc686febbf7cbb13bad167c2d8fe
  GoogleUtilities: 0759d1a57ebb953965c2dfe0ba4c82e95ccc2e34
  nanopb: d4d75c12cd1316f4a64e3c6963f879ecd4b5e0d5
  package_info_plus: 02d7a575e80f194102bef286361c6c326e4c29ce
  path_provider_foundation: 3784922295ac71e43754bd15e0653ccfd36a147c
  platform_device_id: 3e414428f45df149bbbfb623e2c0ca27c545b763
  platform_device_id_macos: f763bb55f088be804d61b96eb4710b8ab6598e94
  PromisesObjC: c50d2056b5253dadbd6c2bea79b0674bd5a52fa4
  PromisesSwift: 28dca69a9c40779916ac2d6985a0192a5cb4a265
  ReachabilitySwift: 985039c6f7b23a1da463388634119492ff86c825
  share_plus: 76dd39142738f7a68dd57b05093b5e8193f220f7
  sqflite: 673a0e54cc04b7d6dba8d24fb8095b31c3a99eec
  url_launcher_macos: d2691c7dd33ed713bf3544850a623080ec693d95
  video_player_avfoundation: 02011213dab73ae3687df27ce441fbbcc82b5579
  wakelock_plus: 4783562c9a43d209c458cb9b30692134af456269

PODFILE CHECKSUM: 353c8bcc5d5b0994e508d035b5431cfe18c1dea7

COCOAPODS: 1.14.3

import 'package:almashal/src/bindings/albums_binding.dart';
import 'package:almashal/src/bindings/committees_binding.dart';
import 'package:almashal/src/bindings/excellence_award_binding.dart';
import 'package:almashal/src/bindings/news_binding.dart';
import 'package:almashal/src/bindings/splash_binding.dart';
import 'package:almashal/src/view/pages/auth/login_page.dart';
import 'package:almashal/src/view/pages/auth/register_page.dart';
import 'package:almashal/src/view/pages/auth/registration_pending_page.dart';
import 'package:almashal/src/view/pages/committee/committees_page.dart';
import 'package:almashal/src/view/pages/excellence_award/excellence_award_detail_page.dart';
import 'package:almashal/src/view/pages/excellence_award/excellence_awards_page.dart';
import 'package:almashal/src/view/pages/family_tree_page.dart';
import 'package:almashal/src/view/pages/auth/forget_password_page.dart';
import 'package:almashal/src/view/pages/media/image_view_page.dart';
import 'package:almashal/src/view/pages/media/video_view_page.dart';
import 'package:almashal/src/view/pages/news/news_detail_page.dart';
import 'package:almashal/src/view/pages/news/news_page.dart';
import 'package:almashal/src/view/pages/occasion/occasion_detail_page.dart';
import 'package:almashal/src/view/pages/occasion/occasions_page.dart';
import 'package:almashal/src/view/pages/parent/parent_page.dart';
import 'package:almashal/src/view/pages/auth/reset_password_page.dart';
import 'package:almashal/src/view/pages/pdf_view_page.dart';
import 'package:almashal/src/view/pages/profile/profile_page.dart';
import 'package:get/get.dart';
import '../../bindings/about_family_binding.dart';
import '../../bindings/family_tree_binding.dart';
import '../../view/pages/album/album_detail_page.dart';
import '../../view/pages/home_page.dart';
import '../../view/pages/memory/memories_page.dart';
import '../../view/pages/splash_page.dart';

part './app_routes.dart';

abstract class AppPages {
  AppPages._();
  static final pages = [
    // home
    GetPage(
      name: Routes.HOME_PAGE,
      page: () => const HomePage(),
    ),
    // splash
    GetPage(
      name: Routes.SPLASH_PAGE,
      binding: SplashBinding(),
      page: () => const SplashPage(),
    ),
    // login
    GetPage(
      name: Routes.LOGIN_PAGE,
      page: () => const LoginPage(),
    ),
    GetPage(
      name: Routes.REGISTER_PAGE,
      page: () => const RegisterPage(),
    ),
    GetPage(
      name: Routes.REGISTRATION_PENDING_PAGE,
      page: () => const RegistrationPendingPage(),
    ),
    GetPage(
      name: Routes.FORGET_PASSWORD_PAGE,
      page: () => const ForgetPasswordPage(),
    ),
    GetPage(
      name: Routes.RESET_PASSWORD_PAGE,
      page: () => const ResetPasswordPage(),
    ),
    // family tree
    GetPage(
      name: Routes.FAMILY_TREE_PAGE,
      binding: FamilyTreeBinding(),
      page: () => const FamilyTreePage(),
    ),
    // parent
    GetPage(
      name: Routes.PARENT_PAGE,
      binding: AboutFamilyBinding(),
      page: () => const ParentPage(),
    ),
    // occasions
    GetPage(
      name: Routes.OCCASIONS_PAGE,
      page: () => const OccasionsPage(),
    ),
    // news
    GetPage(
      name: Routes.NEWS_PAGE,
      binding: NewsBinding(),
      page: () => const NewsPage(),
    ),
    // memory
    GetPage(
      name: Routes.MEMORY_PAGE,
      binding: AlbumsBinding(),
      page: () => const MemoriesPage(),
    ),
    // committees
    GetPage(
      name: Routes.COMMITTEES_PAGE,
      binding: CommitteesBinding(),
      page: () => const CommitteesPage(),
    ),
    // news detail
    GetPage(
      name: Routes.NEWS_DETAIL_PAGE,
      page: () => const NewsDetailPage(),
    ),
    // album detail
    GetPage(
      name: Routes.ALBUM_DETAIL_PAGE,
      page: () => const AlbumDetailPage(),
    ),
    // image view
    GetPage(
      name: Routes.IMAGE_VIEW_PAGE,
      page: () => const ImageViewPage(),
    ),
    // video view
    GetPage(
      name: Routes.VIDEO_VIEW_PAGE,
      page: () => const VideoViewPage(),
    ),
    // occasion detail
    GetPage(
      name: Routes.OCCASION_DETAIL_PAGE,
      page: () => const OccasionDetailPage(),
    ),
    // excellence awards
    GetPage(
      name: Routes.EXCELLENCE_AWARDS_PAGE,
      binding: ExcellenceAwardBinding(),
      page: () => const ExcellenceAwardsPage(),
    ),
    // excellence awards detail
    GetPage(
      name: Routes.EXCELLENCE_AWARD_DETAIL_PAGE,
      page: () => const ExcellenceAwardDetailPage(),
    ),
    // profile
    GetPage(
      name: Routes.PROFILE_PAGE,
      page: () {
        // الحصول على معرف المستخدم من المعاملات
        final args = Get.arguments as Map<String, dynamic>?;
        final userId = args?['userId'];

        return ProfilePage(userId: userId);
      },
    ),
    // pdf view
    GetPage(
      name: Routes.PDF_VIEW_PAGE,
      page: () => const PdfViewPage(),
    ),
  ];
}

import 'package:flutter/material.dart';

import '../values/colors.dart';
import '../values/app_styles.dart';

final InputDecorationTheme primaryInputDecorationTheme = InputDecorationTheme(
  hintStyle: TextStyle(
    color: Colors.grey.shade500,
    fontSize: 12,
  ),
  prefixStyle: const TextStyle(
    color: Colors.white,
  ),
  suffixStyle: const TextStyle(
    color: Colors.white,
  ),
  hoverColor: AppColors.primaryColor,
  focusColor: AppColors.primaryColor,
  fillColor: Colors.white,
  filled: false,
  errorMaxLines: 2,
  labelStyle: const TextStyle(
    // color: AppColors.secondaryColor,
    fontSize: 16,
    fontWeight: FontWeight.w500,
  ),
  errorStyle: const TextStyle(
    color: Colors.redAccent,
    fontSize: 12,
  ),
  contentPadding: const EdgeInsets.symmetric(
    vertical: 12,
    horizontal: 16,
  ),
  border: OutlineInputBorder(
    borderSide: BorderSide(
      width: 1,
      color: Colors.grey.shade500,
    ),
    borderRadius: AppStyles.formFieldBorderRadius,
  ),
  focusedBorder: OutlineInputBorder(
    borderSide: BorderSide(
      width: 1,
      color: AppColors.primaryColor,
    ),
    borderRadius: AppStyles.formFieldBorderRadius,
  ),
  errorBorder: OutlineInputBorder(
    borderSide: const BorderSide(
      width: 0.3,
      color: Colors.redAccent,
    ),
    borderRadius: AppStyles.formFieldBorderRadius,
  ),
  enabledBorder: OutlineInputBorder(
    borderSide: BorderSide(
      width: 1,
      color: AppColors.secondaryColor,
    ),
    borderRadius: AppStyles.formFieldBorderRadius,
  ),
  // alignLabelWithHint: true,
);
final InputDecorationTheme secondaryInputDecorationTheme = InputDecorationTheme(
  hintStyle: TextStyle(
    color: Colors.grey.shade500,
    fontSize: 12,
  ),
  hoverColor: AppColors.primaryColor,
  focusColor: AppColors.primaryColor,
  filled: false,
  contentPadding: const EdgeInsets.symmetric(
    vertical: 12,
    horizontal: 16,
  ),
  border: OutlineInputBorder(
    borderSide: BorderSide(
      width: 0.1,
      color: Colors.grey.shade500,
    ),
    borderRadius: AppStyles.formFieldBorderRadius,
  ),
  focusedBorder: OutlineInputBorder(
    borderSide: BorderSide(
      width: 0.1,
      color: AppColors.primaryColor,
    ),
    borderRadius: AppStyles.formFieldBorderRadius,
  ),
  errorBorder: OutlineInputBorder(
    borderSide: const BorderSide(
      width: 1,
      color: Colors.redAccent,
    ),
    borderRadius: AppStyles.formFieldBorderRadius,
  ),
  enabledBorder: OutlineInputBorder(
    borderSide: BorderSide(
      width: 0.1,
      color: Colors.grey.shade500,
    ),
    borderRadius: AppStyles.formFieldBorderRadius,
  ),
  alignLabelWithHint: true,
);

import 'package:flutter/material.dart';

class ProfileTheme {
  // الألوان الرئيسية
  static const Color primaryColor = Color(0xFFB6977A);
  static const Color secondaryColor = Color(0xFF4A4A4A);
  static const Color accentColor = Color(0xFF8B6B4E);
  static const Color backgroundColor = Colors.white;
  static const Color textColor = Color(0xFF2D2D2D);

  // أنماط النصوص
  static const TextStyle headerName = TextStyle(
    fontSize: 24,
    fontWeight: FontWeight.bold,
    color: Colors.white,
    shadows: [
      Shadow(
        offset: Offset(0, 2),
        blurRadius: 4,
        color: Color.fromARGB(50, 0, 0, 0),
      ),
    ],
  );

  static const TextStyle subHeaderText = TextStyle(
    fontSize: 16,
    color: Colors.white70,
    fontWeight: FontWeight.w500,
  );

  static const TextStyle tabText = TextStyle(
    fontSize: 16,
    fontWeight: FontWeight.w600,
    color: primaryColor,
  );

  // تأثيرات الظل والانحناءات
  static final BoxDecoration cardDecoration = BoxDecoration(
    color: Colors.white,
    borderRadius: BorderRadius.circular(15),
    boxShadow: [
      BoxShadow(
        color: Colors.black.withOpacity(0.05),
        blurRadius: 10,
        offset: const Offset(0, 5),
      ),
    ],
  );

  // تأثيرات الصور
  static final BoxDecoration profileImageDecoration = BoxDecoration(
    shape: BoxShape.circle,
    border: Border.all(color: Colors.white, width: 4),
    boxShadow: [
      BoxShadow(
        color: Colors.black.withOpacity(0.2),
        blurRadius: 10,
        offset: const Offset(0, 5),
      ),
    ],
  );
}

import 'package:almashal/src/core/theme/input_decoration_themes.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:almashal/src/core/theme/app_colors.dart';
import 'package:almashal/src/core/theme/app_dimensions.dart';

/// ثيم التطبيق الموحد
class AppTheme {
  /// ثيم النظام الفاتح
  static ThemeData get lightTheme {
    return ThemeData(
      // الألوان الأساسية
      primaryColor: AppColors.primary,
      colorScheme: ColorScheme.light(
        primary: AppColors.primary,
        secondary: AppColors.secondary,
        tertiary: AppColors.accent,
        background: AppColors.background,
        surface: AppColors.surfaceLight,
        error: AppColors.error,
      ),
      fontFamily: GoogleFonts.tajawal().fontFamily,
      // ثيم البطاقات
      cardTheme: CardTheme(
        color: Colors.white,
        elevation: AppDimensions.elevationSmall,
        margin: EdgeInsets.all(AppDimensions.small),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.borderRadiusMedium),
        ),
      ),

      // ثيم الزر المرتفع
      elevatedButtonTheme: ElevatedButtonThemeData(
        style: ElevatedButton.styleFrom(
          backgroundColor: AppColors.primary,
          foregroundColor: Colors.white,
          padding: EdgeInsets.symmetric(
            horizontal: AppDimensions.medium,
            vertical: AppDimensions.small,
          ),
          textStyle: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: AppDimensions.fontSizeRegular,
          ),
          shape: RoundedRectangleBorder(
            borderRadius:
                BorderRadius.circular(AppDimensions.borderRadiusMedium),
          ),
        ),
      ),

      // ثيم زر النص
      textButtonTheme: TextButtonThemeData(
        style: TextButton.styleFrom(
          foregroundColor: AppColors.primary,
          padding: EdgeInsets.symmetric(
            horizontal: AppDimensions.small,
            vertical: AppDimensions.tiny,
          ),
          textStyle: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: AppDimensions.fontSizeRegular,
          ),
        ),
      ),

      // ثيم حقول النموذج
      inputDecorationTheme: InputDecorationTheme(
        filled: true,
        fillColor: AppColors.surfaceLight,
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.borderRadiusMedium),
          borderSide:
              BorderSide(color: AppColors.primary.withOpacity(0.3), width: 1),
        ),
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.borderRadiusMedium),
          borderSide:
              BorderSide(color: AppColors.primary.withOpacity(0.3), width: 1),
        ),
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.borderRadiusMedium),
          borderSide: BorderSide(color: AppColors.primary, width: 1),
        ),
        errorBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(AppDimensions.borderRadiusMedium),
          borderSide: BorderSide(color: AppColors.error),
        ),
        contentPadding: EdgeInsets.symmetric(
          horizontal: AppDimensions.medium,
          vertical: AppDimensions.medium,
        ),
      ),

      // ثيم شريط التبويب
      tabBarTheme: TabBarTheme(
        labelColor: AppColors.primary,
        unselectedLabelColor: AppColors.textSecondary,
        indicatorColor: AppColors.primary,
        labelStyle: TextStyle(
          fontWeight: FontWeight.bold,
          fontSize: AppDimensions.fontSizeRegular,
        ),
        unselectedLabelStyle: TextStyle(
          fontWeight: FontWeight.normal,
          fontSize: AppDimensions.fontSizeRegular,
        ),
      ),

      // ثيم الخط
      textTheme: TextTheme(
        headlineLarge: TextStyle(
          fontSize: 24,
          fontWeight: FontWeight.bold,
          color: AppColors.textPrimary,
        ),
        headlineMedium: TextStyle(
          fontSize: 20,
          fontWeight: FontWeight.bold,
          color: AppColors.textPrimary,
        ),
        headlineSmall: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: AppColors.textPrimary,
        ),
        titleLarge: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.w600,
          color: AppColors.textPrimary,
        ),
        titleMedium: TextStyle(
          fontSize: 16,
          fontWeight: FontWeight.w600,
          color: AppColors.textPrimary,
        ),
        titleSmall: TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
          color: AppColors.textPrimary,
        ),
        bodyLarge: TextStyle(
          fontSize: 16,
          color: AppColors.textPrimary,
        ),
        bodyMedium: TextStyle(
          fontSize: 14,
          color: AppColors.textPrimary,
        ),
        bodySmall: TextStyle(
          fontSize: 12,
          color: AppColors.textSecondary,
        ),
      ),

      // ثيم شريط التطبيق
      appBarTheme: AppBarTheme(
        backgroundColor: AppColors.primary,
        elevation: 0,
        centerTitle: true,
        titleTextStyle: TextStyle(
          fontSize: 18,
          fontWeight: FontWeight.bold,
          color: Colors.white,
          fontFamily: GoogleFonts.tajawal().fontFamily,
        ),
        iconTheme: IconThemeData(
          color: Colors.white,
        ),
      ),

      // ثيم الأيقونة
      iconTheme: IconThemeData(
        color: AppColors.textSecondary,
        size: 24,
      ),

      // ثيم الفاصل
      dividerTheme: DividerThemeData(
        color: Colors.transparent,
        thickness: 0,
        space: 0,
        indent: 0,
        endIndent: 0,
      ),

      // ثيم زر العائم
      floatingActionButtonTheme: FloatingActionButtonThemeData(
        backgroundColor: AppColors.primary,
        foregroundColor: Colors.white,
        shape: RoundedRectangleBorder(
          borderRadius:
              BorderRadius.circular(AppDimensions.borderRadiusCircular),
        ),
      ),
    );
  }

  /// ثيم النظام الداكن (يمكن تطبيق النفس المنطق)
  static ThemeData get darkTheme {
    // قم بتنفيذ ثيم داكن مشابه
    return lightTheme;
  }
}

/// الأبعاد والمسافات الموحدة في التطبيق
class AppDimensions {
  // المسافات الداخلية الشائعة
  static const double tiny = 4.0;
  static const double small = 8.0;
  static const double medium = 16.0;
  static const double large = 24.0;
  static const double extraLarge = 32.0;
  static const double huge = 48.0;
  
  // نصف قطر الحواف
  static const double borderRadiusSmall = 4.0;
  static const double borderRadiusMedium = 8.0;
  static const double borderRadiusLarge = 16.0;
  static const double borderRadiusCircular = 100.0;
  
  // أحجام الخطوط
  static const double fontSizeSmall = 12.0;
  static const double fontSizeRegular = 14.0;
  static const double fontSizeMedium = 16.0;
  static const double fontSizeLarge = 18.0;
  static const double fontSizeTitle = 20.0;
  static const double fontSizeHeadline = 24.0;
  
  // ارتفاعات شائعة
  static const double buttonHeight = 48.0;
  static const double formFieldHeight = 56.0;
  static const double cardHeight = 80.0;
  static const double avatarSizeSmall = 32.0;
  static const double avatarSizeMedium = 40.0;
  static const double avatarSizeLarge = 56.0;
  static const double avatarSizeHuge = 80.0;
  
  // سمك الخطوط
  static const double borderWidth = 1.0;
  static const double borderWidthThick = 2.0;
  static const double dividerThickness = 0.5;
  
  // المعدلات
  static const double defaultOpacity = 0.8;
  static const double mediumOpacity = 0.5;
  static const double lightOpacity = 0.2;
  
  // الظلال
  static const double elevationSmall = 2.0;
  static const double elevationMedium = 4.0;
  static const double elevationLarge = 8.0;
} 
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';

import '../values/colors.dart';
import 'input_decoration_themes.dart';

class AppThemes {
  static ThemeData themeData = ThemeData(
    primaryColor: AppColors.primaryColor,
    useMaterial3: true,
    textTheme: GoogleFonts.tajawalTextTheme(),
    scaffoldBackgroundColor: AppColors.backgroundColor,
    tabBarTheme: TabBarTheme(
      labelStyle: TextStyle(
        fontSize: 14,
        fontFamily: GoogleFonts.tajawalTextTheme().labelSmall?.fontFamily,
      ),
      unselectedLabelStyle: TextStyle(
        fontSize: 14,
        fontFamily: GoogleFonts.tajawalTextTheme().labelSmall?.fontFamily,
      ),
      indicatorSize: TabBarIndicatorSize.tab,
      indicator: const UnderlineTabIndicator(
        borderSide: BorderSide(
          width: 2,
          color: Colors.white,
        ),
      ),
    ),
    colorScheme: ColorScheme.fromSwatch().copyWith(
      primary: AppColors.primaryColor,
      secondary: AppColors.secondaryColor,
      onError: Colors.redAccent,
      onPrimary: Colors.white,
    ),

    appBarTheme: AppBarTheme(
      iconTheme: const IconThemeData(color: Colors.white),
      centerTitle: true,
      backgroundColor: AppColors.primaryColor,
      titleTextStyle: TextStyle(
        height: 1.5,
        fontSize: 18,
        fontWeight: FontWeight.w600,
        fontFamily: GoogleFonts.tajawalTextTheme().labelSmall?.fontFamily,
        color: Colors.white,
      ),
    ),
    inputDecorationTheme: primaryInputDecorationTheme,
    floatingActionButtonTheme: FloatingActionButtonThemeData(
      backgroundColor: AppColors.primaryColor,
      foregroundColor: Colors.white,
    ),
  );
}

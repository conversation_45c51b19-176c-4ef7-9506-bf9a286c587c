import 'dart:io';
import 'dart:typed_data';
import 'package:file_saver/file_saver.dart';
import 'package:flutter/foundation.dart';
import 'package:path_provider/path_provider.dart';
import 'package:universal_html/html.dart' as html;
import 'package:open_file/open_file.dart';

import 'platform_helper.dart';

/// مساعد للتعامل مع الملفات بطريقة متوافقة مع الويب والموبايل
class FileHelper {
  /// حفظ ملف من بيانات ثنائية
  static Future<String?> saveFile({
    required Uint8List fileData,
    required String fileName,
    required String mimeType,
    String? extension,
  }) async {
    try {
      if (PlatformHelper.isWeb) {
        return _saveFileWeb(fileData, fileName, mimeType, extension);
      } else {
        return _saveFileMobile(fileData, fileName, extension);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error saving file: $e');
      }
      return null;
    }
  }

  /// حفظ ملف في الويب
  static Future<String?> _saveFileWeb(
    Uint8List fileData,
    String fileName,
    String mimeType,
    String? extension,
  ) async {
    try {
      // استخدام file_saver للحفظ في الويب - استخدام MimeType.custom
      String mimeTypeStr = mimeType;
      
      final path = await FileSaver.instance.saveFile(
        name: fileName,
        bytes: fileData,
        ext: extension ?? '',
        mimeType: MimeType.custom,
        customMimeType: mimeTypeStr,
      );
      return path;
    } catch (e) {
      if (kDebugMode) {
        print('Error saving file in web: $e');
      }
      
      // طريقة بديلة باستخدام universal_html
      final blob = html.Blob([fileData], mimeType);
      final url = html.Url.createObjectUrlFromBlob(blob);
      final anchor = html.AnchorElement(href: url)
        ..setAttribute('download', fileName)
        ..click();
      html.Url.revokeObjectUrl(url);
      return fileName;
    }
  }

  /// حفظ ملف في تطبيقات الموبايل
  static Future<String?> _saveFileMobile(
    Uint8List fileData,
    String fileName,
    String? extension,
  ) async {
    try {
      // الحصول على مسار دليل التنزيلات
      final directory = await getTemporaryDirectory();
      final filePath = '${directory.path}/$fileName${extension != null ? '.$extension' : ''}';
      final file = File(filePath);
      
      // كتابة البيانات في الملف
      await file.writeAsBytes(fileData);
      
      // فتح الملف بعد الحفظ
      await OpenFile.open(filePath);
      
      return filePath;
    } catch (e) {
      if (kDebugMode) {
        print('Error saving file in mobile: $e');
      }
      return null;
    }
  }

  /// فتح ملف بناءً على المسار
  static Future<bool> openFile(String filePath) async {
    try {
      if (PlatformHelper.isWeb) {
        // في الويب، نستخدم رابط URL للفتح
        final url = Uri.parse(filePath).toString();
        html.window.open(url, '_blank');
        return true;
      } else {
        // في الموبايل، نستخدم مكتبة open_file
        final result = await OpenFile.open(filePath);
        return result.type == ResultType.done;
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error opening file: $e');
      }
      return false;
    }
  }

  /// فتح ملف من بيانات ثنائية مباشرة (مفيد للويب)
  static Future<bool> openDataAsFile({
    required Uint8List fileData,
    required String fileName,
    required String mimeType,
  }) async {
    try {
      if (PlatformHelper.isWeb) {
        // في الويب، نقوم بإنشاء blob وفتحه
        final blob = html.Blob([fileData], mimeType);
        final url = html.Url.createObjectUrlFromBlob(blob);
        html.window.open(url, '_blank');
        // الانتظار قليلاً قبل إزالة الرابط
        await Future.delayed(const Duration(seconds: 1));
        html.Url.revokeObjectUrl(url);
        return true;
      } else {
        // في الموبايل، نقوم بحفظ الملف أولاً ثم فتحه
        final savedPath = await saveFile(
          fileData: fileData,
          fileName: fileName,
          mimeType: mimeType,
        );
        
        if (savedPath != null) {
          return await openFile(savedPath);
        }
        return false;
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error opening data as file: $e');
      }
      return false;
    }
  }

  /// التحقق من وجود ملف
  static Future<bool> fileExists(String filePath) async {
    try {
      if (PlatformHelper.isWeb) {
        // الويب لا يدعم التحقق من وجود الملفات محليًا بنفس الطريقة
        return false;
      } else {
        final file = File(filePath);
        return await file.exists();
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error checking if file exists: $e');
      }
      return false;
    }
  }
} 
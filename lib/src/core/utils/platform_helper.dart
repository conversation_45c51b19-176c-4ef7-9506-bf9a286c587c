import 'dart:io';
import 'package:flutter/foundation.dart' show kIsWeb;

/// مساعد للتحقق من نوع المنصة والتكيف معها
class PlatformHelper {
  /// التحقق مما إذا كان التطبيق يعمل على متصفح ويب
  static bool get isWeb => kIsWeb;

  /// التحقق مما إذا كان التطبيق يعمل على منصة أندرويد
  static bool get isAndroid => !isWeb && Platform.isAndroid;

  /// التحقق مما إذا كان التطبيق يعمل على منصة iOS
  static bool get isIOS => !isWeb && Platform.isIOS;

  /// التحقق مما إذا كان التطبيق يعمل على الهاتف المحمول (أندرويد أو iOS)
  static bool get isMobile => isAndroid || isIOS;

  /// التحقق مما إذا كان التطبيق يعمل على منصة ماك
  static bool get isMacOS => !isWeb && Platform.isMacOS;

  /// التحقق مما إذا كان التطبيق يعمل على منصة ويندوز
  static bool get isWindows => !isWeb && Platform.isWindows;

  /// التحقق مما إذا كان التطبيق يعمل على منصة لينكس
  static bool get isLinux => !isWeb && Platform.isLinux;

  /// التحقق مما إذا كان التطبيق يعمل على سطح المكتب (ماك، ويندوز، لينكس)
  static bool get isDesktop => isMacOS || isWindows || isLinux;

  /// الحصول على اسم المنصة كنص
  static String get platformName {
    if (isWeb) return 'Web';
    if (isAndroid) return 'Android';
    if (isIOS) return 'iOS';
    if (isMacOS) return 'macOS';
    if (isWindows) return 'Windows';
    if (isLinux) return 'Linux';
    return 'Unknown';
  }
} 
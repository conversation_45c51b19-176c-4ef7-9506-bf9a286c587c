import 'package:almashal/src/core/values/app_config.dart';
import 'package:almashal/src/data/providers/api/api_provider.dart';
import 'package:almashal/src/data/services/auth_service.dart';
import 'package:get/get.dart';

class RecordVisitFunction {
  // Model types as defined in the backend:
  // 'news' => 'App\Models\News',
  // 'occasion' => 'App\Models\Occasion',
  // 'award' => 'App\Models\ExcellenceAward',
  // 'album' => 'App\Models\Album',

  static ApiProvider get apiProvider => AuthService.instance.isAuthenticated()
      ? AppConfig.authenticatedApiProvider
      : AppConfig.apiProvider;

  static void news(int newsId) async {
    try {
      var data = {'model_type': 'news', 'model_id': newsId};
      await apiProvider.recordVisit(data);
      Get.log('Visit recorded for news ID: $newsId');
    } catch (e) {
      Get.log('Error recording visit for news: $e');
    }
  }

  static void occasion(int occasionId) async {
    try {
      var data = {'model_type': 'occasion', 'model_id': occasionId};
      await apiProvider.recordVisit(data);
      Get.log('Visit recorded for occasion ID: $occasionId');
    } catch (e) {
      Get.log('Error recording visit for occasion: $e');
    }
  }

  static void award(int awardId) async {
    try {
      var data = {'model_type': 'award', 'model_id': awardId};
      await apiProvider.recordVisit(data);
      Get.log('Visit recorded for award ID: $awardId');
    } catch (e) {
      Get.log('Error recording visit for award: $e');
    }
  }

  static void album(int albumId) async {
    try {
      var data = {'model_type': 'album', 'model_id': albumId};
      await apiProvider.recordVisit(data);
      Get.log('Visit recorded for album ID: $albumId');
    } catch (e) {
      Get.log('Error recording visit for album: $e');
    }
  }
}

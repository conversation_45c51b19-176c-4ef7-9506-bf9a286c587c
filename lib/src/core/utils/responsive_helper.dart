import 'package:flutter/material.dart';
// استيراد مباشر للقيم من ملف WebConfig
// import 'package:almashal/src/core/values/web_config.dart';

// نسخة محلية من WebConfig لتجنب مشاكل الاستيراد
class WebConfig {
  // إعدادات تخصيص الواجهة
  static const bool useMobileLayout = false; // استخدام تخطيط الموبايل على الشاشات الصغيرة
  
  // الحد الأدنى لعرض الشاشة للتبديل إلى وضع سطح المكتب
  static const int minDesktopWidth = 992;
}

/// مساعد للتصميم المتجاوب لدعم أحجام الشاشات المختلفة
class ResponsiveHelper {
  /// التحقق مما إذا كان الجهاز هو هاتف محمول (صغير)
  static bool isMobile(BuildContext context) {
    return MediaQuery.of(context).size.width < 576;
  }

  /// التحقق مما إذا كان الجهاز هو جهاز لوحي صغير
  static bool isSmallTablet(BuildContext context) {
    return MediaQuery.of(context).size.width >= 576 &&
        MediaQuery.of(context).size.width < 768;
  }

  /// التحقق مما إذا كان الجهاز هو جهاز لوحي
  static bool isTablet(BuildContext context) {
    return MediaQuery.of(context).size.width >= 768 &&
        MediaQuery.of(context).size.width < 992;
  }

  /// التحقق مما إذا كان الجهاز هو حاسوب صغير
  static bool isSmallDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= 992 &&
        MediaQuery.of(context).size.width < 1200;
  }

  /// التحقق مما إذا كان الجهاز هو حاسوب مكتبي
  static bool isDesktop(BuildContext context) {
    return MediaQuery.of(context).size.width >= 1200;
  }

  /// الحصول على عرض الشاشة بناءً على حجم الجهاز
  static double getScreenWidth(BuildContext context) {
    // قم بتحديد عرض افتراضي للأجهزة المكتبية والهواتف الكبيرة
    if (isDesktop(context)) {
      return 1170;
    } else if (isSmallDesktop(context)) {
      return 970;
    } else if (isTablet(context)) {
      return 750;
    } else if (isSmallTablet(context)) {
      return 560;
    } else {
      return MediaQuery.of(context).size.width;
    }
  }

  /// الحصول على ارتفاع الشاشة بناءً على حجم الجهاز
  static double getScreenHeight(BuildContext context) {
    return MediaQuery.of(context).size.height;
  }

  /// الحصول على هوامش أفقية للمحتوى بناءً على حجم الجهاز
  static double getContentHorizontalPadding(BuildContext context) {
    if (isDesktop(context) || isSmallDesktop(context)) {
      return 32.0;
    } else if (isTablet(context)) {
      return 24.0;
    } else {
      return 16.0;
    }
  }

  /// الحصول على هوامش رأسية للمحتوى بناءً على حجم الجهاز
  static double getContentVerticalPadding(BuildContext context) {
    if (isDesktop(context) || isSmallDesktop(context)) {
      return 24.0;
    } else if (isTablet(context)) {
      return 20.0;
    } else {
      return 16.0;
    }
  }

  /// تحديد ما إذا كان التطبيق سيستخدم تخطيط الجوال أم تخطيط سطح المكتب
  static bool useMobileLayout(BuildContext context) {
    if (WebConfig.useMobileLayout) {
      return true;
    }
    return MediaQuery.of(context).size.width < WebConfig.minDesktopWidth;
  }

  /// تحديد حجم الخط المناسب بناءً على حجم الجهاز
  static double getFontSize(BuildContext context, double baseSize) {
    if (isDesktop(context)) {
      return baseSize * 1.2;
    } else if (isSmallDesktop(context) || isTablet(context)) {
      return baseSize * 1.1;
    } else {
      return baseSize;
    }
  }

  /// الحصول على عدد أعمدة الشبكة المناسب بناءً على حجم الجهاز
  static int getGridColumnCount(BuildContext context) {
    if (isDesktop(context)) {
      return 4;
    } else if (isSmallDesktop(context)) {
      return 3;
    } else if (isTablet(context)) {
      return 2;
    } else {
      return 1;
    }
  }
} 
import 'package:dio/dio.dart';
import 'package:get/get.dart';
import '../../data/providers/api/api_provider.dart';
import '../../controllers/profile_controller.dart';
import '../../data/services/error_service.dart';
import '../../core/values/app_config.dart';

class ServiceLocator {
  static void init() {
    // Register Error Services
    Get.put(ErrorBag());
    Get.put(ErrorService());

    // Register Dio
    final dio = Dio(BaseOptions(
      baseUrl: AppConfig.apiUrl,
      connectTimeout: const Duration(seconds: 30),
      receiveTimeout: const Duration(seconds: 30),
      headers: {
        'Accept': 'application/json',
        'Content-Type': 'application/json',
        'api-key': 'NwaAi8q5SXQAu9P5X3bqSPGkakoI',
      },
    ));

    Get.put(dio);

    // Register API Provider
    Get.put(ApiProvider(Get.find<Dio>(), baseUrl: AppConfig.apiUrl));

    // Register Controllers
    Get.put(ProfileController(Get.find<ApiProvider>()));
  }
}

import 'package:flutter/material.dart';

class AppStyles {
  AppStyles._();
  // border Radius
  static BorderRadius sliderBorderRadius = BorderRadius.circular(16);
  static BorderRadius cardBorderRadius = BorderRadius.circular(8);
  static BorderRadius buttonBorderRadius = BorderRadius.circular(8);
  static BorderRadius formFieldBorderRadius = BorderRadius.circular(8);
  // padding
  static EdgeInsets authPagesPadding = const EdgeInsets.all(32);
  static EdgeInsets pagesPadding = const EdgeInsets.all(16);
  // decoraations
  static BoxDecoration cardBoxdecoration = BoxDecoration(
    borderRadius: AppStyles.cardBorderRadius,
    color: Colors.white,
    boxShadow: [
      BoxShadow(
        color: Colors.grey.shade300,
        blurRadius: 2,
        spreadRadius: 1,
      ),
    ],
  );
  // text styles
  //card title
  static TextStyle cardTitle = const TextStyle(
    fontSize: 13,
    color: Colors.white,
    height: 1.2,
  );
  // button text
  static TextStyle buttonText = const TextStyle(
    fontSize: 16,
    color: Colors.white,
  );
}

// إعدادات وتكوينات خاصة بمنصة الويب
class WebConfig {
  // مسار API الخاص بالويب (يمكن أن يكون مختلفًا عن تطبيق الموبايل)
  static const String apiUrl = 'https://api.almashalfamily.com/api/v1';
  
  // عناوين URL للموارد
  static const String imageBaseUrl = 'https://api.almashalfamily.com/storage/';
  static const String thumbBaseUrl = 'https://api.almashalfamily.com/storage/';
  
  // حجم الصفحة الافتراضي
  static const int pageSize = 10;
  
  // مفتاح API
  static const String apiKey = 'NwaAi8q5SXQAu9P5X3bqSPGkakoI';
  
  // ثوابت التخزين للويب
  static const String tokenKey = 'token_key';
  static const String userDataKey = 'user_data_key';
  
  // إعدادات تخصيص الواجهة
  static const bool useMobileLayout = false; // استخدام تخطيط الموبايل على الشاشات الصغيرة
  
  // الحد الأدنى لعرض الشاشة للتبديل إلى وضع سطح المكتب
  static const int minDesktopWidth = 992;
  
  // وضع التطوير
  static const bool isDebug = true;
} 
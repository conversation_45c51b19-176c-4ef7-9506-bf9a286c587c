import 'package:almashal/src/core/routes/app_pages.dart';
import 'package:almashal/src/core/utils/common_functions.dart';
import 'package:almashal/src/data/services/auth_service.dart';
import 'package:almashal/src/view/components/bottomsheets/add_comment_bottom_sheet.dart';
import 'package:feather_icons/feather_icons.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AddCommentFloatingActionButton extends StatelessWidget {
  const AddCommentFloatingActionButton({
    super.key,
    required this.type,
    required this.id,
  });
  final String type;
  final int id;
  @override
  Widget build(BuildContext context) {
    return FloatingActionButton(
      onPressed: () {
        if (!AuthService.instance.isAuthenticated()) {
          CommonFunctions.showErrorMessage('يرجى تسجيل الدخول للمتابعة');
          Get.toNamed(Routes.LOGIN_PAGE);
        } else {
          Get.bottomSheet(
            AddCommentBottomSheet(
              type: type,
              id: id,
            ),
          );
        }
      },
      child: const Icon(FeatherIcons.messageSquare),
    );
  }
}

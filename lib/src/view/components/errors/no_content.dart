import 'package:almashal/src/core/values/assets.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

class NoContent extends StatelessWidget {
  const NoContent({
    super.key,
    this.message = 'لايوجد محتوى',
  });
  final String message;
  @override
  Widget build(BuildContext context) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          SvgPicture.asset(
            Assets.noContent,
            width: 150,
          ),
          Padding(
            padding: const EdgeInsets.only(top: 32.0),
            child: Text(
              message,
              style: TextStyle(
                fontSize: 16,
                fontWeight: FontWeight.bold,
                color: Get.theme.primaryColor,
              ),
            ),
          )
        ],
      ),
    );
  }
}

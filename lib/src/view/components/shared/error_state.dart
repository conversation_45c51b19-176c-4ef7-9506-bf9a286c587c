import 'package:flutter/material.dart';
import 'package:almashal/src/core/theme/app_colors.dart';
import 'package:almashal/src/core/theme/app_dimensions.dart';

/// مكون موحد لعرض حالة الخطأ في التطبيق
class ErrorState extends StatelessWidget {
  /// رسالة الخطأ
  final String message;
  
  /// تفاصيل الخطأ
  final String? details;
  
  /// دالة يتم استدعاؤها عند الضغط على زر إعادة المحاولة
  final VoidCallback? onRetry;
  
  /// عنوان زر إعادة المحاولة
  final String retryButtonText;
  
  /// أيقونة الخطأ
  final IconData icon;
  
  /// لون الأيقونة
  final Color? iconColor;
  
  /// حجم الأيقونة
  final double iconSize;

  /// منشئ مكون حالة الخطأ
  const ErrorState({
    Key? key,
    required this.message,
    this.details,
    this.onRetry,
    this.retryButtonText = 'إعادة المحاولة',
    this.icon = Icons.error_outline,
    this.iconColor,
    this.iconSize = 80.0,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveIconColor = iconColor ?? AppColors.error;
    
    return Center(
      child: Padding(
        padding: EdgeInsets.all(AppDimensions.medium),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: iconSize,
              color: effectiveIconColor,
            ),
            SizedBox(height: AppDimensions.medium),
            Text(
              message,
              textAlign: TextAlign.center,
              style: theme.textTheme.titleMedium?.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
            if (details != null) ...[
              SizedBox(height: AppDimensions.small),
              Text(
                details!,
                textAlign: TextAlign.center,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],
            if (onRetry != null) ...[
              SizedBox(height: AppDimensions.large),
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: Icon(Icons.refresh),
                label: Text(retryButtonText),
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppColors.primary,
                  foregroundColor: Colors.white,
                  padding: EdgeInsets.symmetric(
                    horizontal: AppDimensions.medium,
                    vertical: AppDimensions.small,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
} 
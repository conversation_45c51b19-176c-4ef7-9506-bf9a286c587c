import 'package:flutter/material.dart';
import 'package:almashal/src/core/theme/app_colors.dart';
import 'package:almashal/src/core/theme/app_dimensions.dart';

/// مكون موحد لعرض حالة فارغة في التطبيق
class EmptyState extends StatelessWidget {
  /// نص الرسالة
  final String message;
  
  /// وصف اختياري
  final String? description;
  
  /// دالة يتم استدعاؤها عند الضغط على زر التحديث
  final VoidCallback? onRefresh;
  
  /// أيقونة الحالة الفارغة
  final IconData icon;
  
  /// لون الأيقونة
  final Color? iconColor;
  
  /// حجم الأيقونة
  final double iconSize;
  
  /// عنوان زر التحديث
  final String refreshButtonText;

  /// منشئ مكون الحالة الفارغة
  const EmptyState({
    Key? key,
    required this.message,
    this.description,
    this.onRefresh,
    this.icon = Icons.inbox,
    this.iconColor,
    this.iconSize = 80.0,
    this.refreshButtonText = 'تحديث',
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final effectiveIconColor = iconColor ?? AppColors.primary.withOpacity(0.5);
    
    return Center(
      child: Padding(
        padding: EdgeInsets.all(AppDimensions.medium),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          crossAxisAlignment: CrossAxisAlignment.center,
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              icon,
              size: iconSize,
              color: effectiveIconColor,
            ),
            SizedBox(height: AppDimensions.medium),
            Text(
              message,
              textAlign: TextAlign.center,
              style: theme.textTheme.titleMedium?.copyWith(
                color: AppColors.textPrimary,
                fontWeight: FontWeight.bold,
              ),
            ),
            if (description != null) ...[
              SizedBox(height: AppDimensions.small),
              Text(
                description!,
                textAlign: TextAlign.center,
                style: theme.textTheme.bodyMedium?.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],
            if (onRefresh != null) ...[
              SizedBox(height: AppDimensions.large),
              TextButton.icon(
                onPressed: onRefresh,
                icon: Icon(Icons.refresh, color: AppColors.primary),
                label: Text(
                  refreshButtonText,
                  style: TextStyle(color: AppColors.primary),
                ),
                style: TextButton.styleFrom(
                  padding: EdgeInsets.symmetric(
                    horizontal: AppDimensions.medium,
                    vertical: AppDimensions.small,
                  ),
                ),
              ),
            ],
          ],
        ),
      ),
    );
  }
} 
import 'package:flutter/material.dart';
import 'package:almashal/src/core/theme/app_colors.dart';
import 'package:almashal/src/core/theme/app_dimensions.dart';

/// مكون فاصل مخصص يستخدم الثيم الموحد للتطبيق
/// يمكن استخدامه لإظهار أو إخفاء الفاصل حسب الحاجة
class CustomDivider extends StatelessWidget {
  /// المسافة قبل الفاصل
  final double? indent;
  
  /// المسافة بعد الفاصل
  final double? endIndent;
  
  /// ارتفاع الفاصل
  final double? height;
  
  /// سماكة الفاصل
  final double? thickness;
  
  /// لون الفاصل
  final Color? color;
  
  /// شفافية الفاصل
  final double opacity;
  
  /// ما إذا كان الفاصل مرئياً أم لا
  final bool visible;

  /// منشئ مكون الفاصل المخصص
  const CustomDivider({
    Key? key,
    this.indent,
    this.endIndent,
    this.height,
    this.thickness,
    this.color,
    this.opacity = 0.3,
    this.visible = false, // افتراضياً غير مرئي
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    // إذا كان الفاصل غير مرئي، نعيد مساحة فارغة بنفس الارتفاع
    if (!visible) {
      return SizedBox(height: height ?? 0);
    }
    
    // استخدام لون الفاصل من الثيم أو اللون المخصص إذا تم تحديده
    final effectiveColor = color ?? AppColors.primary.withOpacity(opacity);
    
    return Divider(
      color: effectiveColor,
      height: height ?? AppDimensions.medium,
      thickness: thickness ?? AppDimensions.dividerThickness,
      indent: indent,
      endIndent: endIndent,
    );
  }
} 
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:almashal/src/core/theme/app_colors.dart';
import 'package:almashal/src/core/theme/app_dimensions.dart';

/// مكون موحد لعرض حالة التحميل في التطبيق
class LoadingState extends StatelessWidget {
  /// نص الرسالة الاختياري
  final String? message;
  
  /// حجم مؤشر التحميل
  final double size;
  
  /// نوع مؤشر التحميل 
  final LoadingIndicatorType type;
  
  /// لون مؤشر التحميل
  final Color? color;

  /// منشئ مكون حالة التحميل
  const LoadingState({
    Key? key,
    this.message,
    this.size = 40.0,
    this.type = LoadingIndicatorType.threeBounce,
    this.color,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final indicatorColor = color ?? AppColors.primary;
    
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.center,
        mainAxisSize: MainAxisSize.min,
        children: [
          _buildLoadingIndicator(indicatorColor),
          if (message != null) ...[
            SizedBox(height: AppDimensions.medium),
            Text(
              message!,
              textAlign: TextAlign.center,
              style: theme.textTheme.bodyMedium?.copyWith(
                color: AppColors.textSecondary,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// بناء مؤشر التحميل المناسب حسب النوع المحدد
  Widget _buildLoadingIndicator(Color color) {
    switch (type) {
      case LoadingIndicatorType.threeBounce:
        return SpinKitThreeBounce(
          color: color,
          size: size,
        );
      case LoadingIndicatorType.circle:
        return SpinKitCircle(
          color: color,
          size: size,
        );
      case LoadingIndicatorType.doubleBounce:
        return SpinKitDoubleBounce(
          color: color,
          size: size,
        );
      case LoadingIndicatorType.wave:
        return SpinKitWave(
          color: color,
          size: size,
        );
      case LoadingIndicatorType.fadingCircle:
        return SpinKitFadingCircle(
          color: color,
          size: size,
        );
    }
  }
}

/// أنواع مؤشرات التحميل المتاحة
enum LoadingIndicatorType {
  threeBounce,
  circle,
  doubleBounce,
  wave,
  fadingCircle,
} 
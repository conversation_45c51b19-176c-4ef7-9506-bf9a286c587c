import 'package:flutter/material.dart';
import 'package:almashal/src/core/theme/app_colors.dart';
import 'package:almashal/src/core/theme/app_dimensions.dart';

/// بطاقة موحدة تستخدم في جميع أنحاء التطبيق
class CustomCard extends StatelessWidget {
  /// محتوى البطاقة
  final Widget child;
  
  /// عنوان البطاقة الاختياري
  final String? title;
  
  /// وصف البطاقة الاختياري
  final String? subtitle;
  
  /// أيقونة العنوان الاختيارية
  final IconData? titleIcon;
  
  /// دالة يتم استدعاؤها عند الضغط على البطاقة
  final VoidCallback? onTap;
  
  /// دالة يتم استدعاؤها عند الضغط المطول على البطاقة
  final VoidCallback? onLongPress;
  
  /// حجم ارتفاع البطاقة، يكون null للحجم التلقائي
  final double? height;
  
  /// الهوامش الخارجية للبطاقة
  final EdgeInsetsGeometry margin;
  
  /// البطانة الداخلية للبطاقة
  final EdgeInsetsGeometry padding;
  
  /// مؤشر إذا كانت البطاقة بخلفية بيضاء أو خلفية ملونة خفيفة
  final bool useLightBackground;
  
  /// مستوى ارتفاع البطاقة (للظل)
  final double elevation;
  
  /// قائمة الإجراءات الاختيارية في أسفل البطاقة
  final List<Widget>? actions;

  /// منشئ مكون البطاقة الموحدة
  const CustomCard({
    Key? key,
    required this.child,
    this.title,
    this.subtitle,
    this.titleIcon,
    this.onTap,
    this.onLongPress,
    this.height,
    this.margin = const EdgeInsets.all(8.0),
    this.padding = const EdgeInsets.all(16.0),
    this.useLightBackground = true,
    this.elevation = 2.0,
    this.actions,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    final theme = Theme.of(context);
    final backgroundColor = useLightBackground
        ? Colors.white
        : AppColors.surfaceLight;
    
    // محتوى البطاقة
    Widget cardContent = Padding(
      padding: padding,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          if (title != null) ...[
            Row(
              children: [
                if (titleIcon != null) ...[
                  Icon(
                    titleIcon,
                    color: AppColors.primary,
                    size: 20.0,
                  ),
                  SizedBox(width: AppDimensions.small),
                ],
                Expanded(
                  child: Text(
                    title!,
                    style: theme.textTheme.titleMedium?.copyWith(
                      color: AppColors.textPrimary,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ],
            ),
            if (subtitle != null) ...[
              SizedBox(height: AppDimensions.tiny),
              Text(
                subtitle!,
                style: theme.textTheme.bodySmall?.copyWith(
                  color: AppColors.textSecondary,
                ),
              ),
            ],
            SizedBox(height: AppDimensions.small),
            Divider(height: 1, thickness: 1, color: AppColors.divider),
            SizedBox(height: AppDimensions.small),
          ],
          child,
          if (actions != null && actions!.isNotEmpty) ...[
            SizedBox(height: AppDimensions.small),
            Divider(height: 1, thickness: 1, color: AppColors.divider),
            SizedBox(height: AppDimensions.small),
            Row(
              mainAxisAlignment: MainAxisAlignment.end,
              children: actions!.map((action) {
                return Padding(
                  padding: EdgeInsets.only(left: AppDimensions.small),
                  child: action,
                );
              }).toList(),
            ),
          ],
        ],
      ),
    );
    
    // بناء البطاقة
    Widget card = Card(
      elevation: elevation,
      color: backgroundColor,
      margin: margin,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(AppDimensions.borderRadiusMedium),
      ),
      child: height != null
          ? SizedBox(height: height, child: cardContent)
          : cardContent,
    );
    
    // إضافة تفاعل إذا كان مطلوباً
    if (onTap != null || onLongPress != null) {
      return InkWell(
        onTap: onTap,
        onLongPress: onLongPress,
        borderRadius: BorderRadius.circular(AppDimensions.borderRadiusMedium),
        child: card,
      );
    }
    
    return card;
  }
} 
import 'package:almashal/src/core/routes/app_pages.dart';
import 'package:almashal/src/data/models/user.dart';
import 'package:almashal/src/data/services/auth_service.dart';
import 'package:almashal/src/view/components/image/custom_cached_network_image.dart';
import 'package:feather_icons/feather_icons.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class UserCard extends StatelessWidget {
  const UserCard({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 150,
      child: Stack(
        children: [
          Container(
            width: double.maxFinite,
            margin: const EdgeInsets.only(bottom: 30),
            height: 130,
            padding: const EdgeInsets.all(32.0),
            color: Get.theme.primaryColor,
          ),
          Positioned(
            bottom: 14,
            right: 0,
            left: 0,
            child: Obx(
              () {
                bool isLogin = AuthService.instance.isAuthenticated();
                User? user = AuthService.instance.userData.value?.user;
                return Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  width: double.maxFinite,
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      CustomCachedNetworkImage(
                        width: 70,
                        height: 70,
                        fit: BoxFit.cover,
                        borderRadius: BorderRadius.circular(100),
                        imageUrl: user?.image ??
                            "https://almashalfamily.com/assets/images/avatar.png",
                      ),
                      const SizedBox(
                        width: 16,
                      ),
                      Expanded(
                        child: Text(
                          user?.displayName ?? 'زائر',
                          style: const TextStyle(
                            color: Colors.white,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                      if (isLogin)
                        IconButton(
                          onPressed: () {
                            Get.back();
                            Get.toNamed(Routes.PROFILE_PAGE);
                          },
                          icon: const Icon(
                            FeatherIcons.settings,
                            color: Colors.white,
                          ),
                        )
                    ],
                  ),
                );
              },
            ),
          ),
        ],
      ),
    );
  }
}

import 'package:almashal/src/core/routes/app_pages.dart';
import 'package:almashal/src/view/components/bottomsheets/contact_bottom_sheet.dart';
import 'package:almashal/src/view/components/layouts/social_media_links_components.dart';
import 'package:almashal/src/view/components/layouts/user_card.dart';
import 'package:feather_icons/feather_icons.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:share_plus/share_plus.dart';

class CustomDrawer extends StatelessWidget {
  const CustomDrawer({super.key});

  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: Column(
        children: [
          Expanded(
            child: Column(
              children: [
                const UserCard(),
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        DrawerItem(
                          leading: const Icon(FeatherIcons.home),
                          title: 'الرئيسة',
                          onTap: () {
                            Get.toNamed(Routes.HOME_PAGE);
                          },
                        ),
                        DrawerItem(
                          leading: const Icon(FeatherIcons.mail),
                          title: 'تواصل معنا',
                          onTap: () {
                            Get.bottomSheet(
                              const ContactBottomSheet(),
                              isScrollControlled: true,
                            );
                          },
                        ),
                        DrawerItem(
                          leading: const Icon(FeatherIcons.share2),
                          title: 'مشاركة التطبيق',
                          onTap: () {
                            // TODO share app
                            var text =
                                " قم بتحميل تطبيق أسرة المشعل من الرابط \n";
                            // genrate onelink link
                            var link = "https://onelink.to/hnmjvq";
                            Share.share(text + link);
                          },
                          withDivider: false,
                        ),
                      ],
                    ),
                  ),
                ),
                const SocialMediaLinksComponent(),
              ],
            ),
          ),
          // SafeArea(
          //   child: Column(
          //     children: [
          //       const Divider(),
          //       const Padding(
          //         padding: EdgeInsets.only(top: 8.0),
          //         child: Text('تصميم وبرمجة'),
          //       ),
          //       InkWell(
          //         onTap: () async {
          //           String url = "https://wa.me/967773095254";
          //           if (!await launchUrl(Uri.parse(url))) {
          //             throw Exception('Could not launch $url');
          //           }
          //         },
          //         child: Padding(
          //           padding: const EdgeInsets.all(8.0),
          //           child: Column(
          //             children: [
          //               Image.asset(
          //                 Assets.smartFingers,
          //                 height: 80,
          //               ),
          //               // const Padding(
          //               //   padding: EdgeInsets.only(bottom: 4.0, top: 16),
          //               //   child: Text(
          //               //     'الأنامل الذكية للخدمات الاعلامية والتقنية',
          //               //     style: TextStyle(
          //               //       fontSize: 12,
          //               //     ),
          //               //   ),
          //               // ),
          //               // Padding(
          //               //   padding: const EdgeInsets.only(bottom: 4.0),
          //               //   child: Row(
          //               //     mainAxisAlignment: MainAxisAlignment.center,
          //               //     children: [
          //               //       Text(
          //               //         'www.smart-fingers.com',
          //               //         style: TextStyle(color: Get.theme.primaryColor),
          //               //       ),
          //               //     ],
          //               //   ),
          //               // ),
          //               // 1
          //             ],
          //           ),
          //         ),
          //       ),
          //     ],
          //   ),
          // )
        ],
      ),
    );
  }
}

class DrawerItem extends StatelessWidget {
  const DrawerItem({
    super.key,
    required this.leading,
    required this.title,
    this.onTap,
    this.withDivider = true,
  });
  final Widget leading;
  final String title;
  final VoidCallback? onTap;
  final bool withDivider;

  @override
  Widget build(BuildContext context) {
    return InkWell(
      onTap: onTap == null
          ? null
          : () {
              Get.back();
              onTap!();
            },
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.symmetric(vertical: 8.0, horizontal: 32),
            child: Row(
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                leading,
                const SizedBox(
                  width: 32,
                ),
                Text(
                  title,
                  style: const TextStyle(
                    fontSize: 16,
                    height: 3,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
          if (withDivider)
            const Divider(
              height: 1,
              thickness: 1,
            )
        ],
      ),
    );
  }
}

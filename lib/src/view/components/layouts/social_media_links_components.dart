import 'package:almashal/src/data/models/social_media_link.dart';
import 'package:almashal/src/data/services/auth_service.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';

class SocialMediaLinksComponent extends StatelessWidget {
  const SocialMediaLinksComponent({
    super.key,
    this.isInHome = false,
  });
  final bool isInHome;

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      List<SocialMediaLink> links = AuthService.instance.socialMediaLinks.value;
      return Column(
        children: [
          if (!isInHome)
            Text(
              'تابعونا',
              style: TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 18,
                color: isInHome ? Colors.white : Colors.black,
              ),
            ),
          SingleChildScrollView(
            padding: const EdgeInsets.all(8.0),
            child: Row(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: links
                  .map(
                    (e) => InkWell(
                      onTap: () async {
                        if (await canLaunchUrl(Uri.parse(e.url))) {
                          launchUrl(Uri.parse(e.url));
                        }
                      },
                      child: Container(
                        margin: const EdgeInsets.symmetric(
                          horizontal: 4,
                        ),
                        decoration: BoxDecoration(
                          color: Get.theme.primaryColor,
                          borderRadius: BorderRadius.circular(8.0),
                        ),
                        height: 40,
                        width: 40,
                        padding: const EdgeInsets.all(8.0),
                        child: SvgPicture.network(
                          e.icon,
                          color: Colors.white,
                          height: double.maxFinite,
                        ),
                      ),
                    ),
                  )
                  .toList(),
            ),
          ),
        ],
      );
    });
  }
}

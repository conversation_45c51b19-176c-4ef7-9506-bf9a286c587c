import 'package:flutter/material.dart';

class VisitCountWidget extends StatelessWidget {
  final int visitsCount;
  final Color? textColor;
  final double? fontSize;
  final EdgeInsetsGeometry? padding;

  const VisitCountWidget({
    super.key,
    required this.visitsCount,
    this.textColor,
    this.fontSize,
    this.padding,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: padding ?? const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      // decoration: BoxDecoration(
      //   color: Theme.of(context).colorScheme.surfaceVariant,
      //   borderRadius: BorderRadius.circular(16),
      // ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          Icon(
            Icons.visibility,
            size: (fontSize ?? 14) + 8,
            color: textColor ?? Colors.grey.shade600,
          ),
          const SizedBox(width: 4),
          Container(
            alignment: Alignment.center,
            height:  16,
            child: Text(
              visitsCount.toString(),
              style: TextStyle(
                fontSize: fontSize ?? 14,
                color: textColor ?? Colors.grey.shade600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  String _getVisitText(int count) {
    if (count == 0) {
      return 'مشاهدة';
    } else if (count == 1) {
      return 'مشاهدة';
    } else if (count == 2) {
      return 'مشاهدتان';
    } else if (count >= 3 && count <= 10) {
      return 'مشاهدات';
    } else {
      return 'مشاهدة';
    }
  }
}

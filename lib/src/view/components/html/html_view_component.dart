import 'package:almashal/src/core/extensions.dart';
import 'package:almashal/src/core/values/colors.dart';
import 'package:almashal/src/view/components/image/custom_cached_network_image.dart';
import 'package:almashal/src/view/components/image/image_view.dart';
import 'package:flutter/material.dart';
import 'package:flutter_widget_from_html/flutter_widget_from_html.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';

class HtmlViewComponent extends StatelessWidget {
  final String html;
  const HtmlViewComponent({
    super.key,
    required this.html,
  });

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 16.0),
      child: HtmlWidget(
        html,
        onTapUrl: (url) {
          launchUrl(Uri.parse(url));
          return true;
        },
        customWidgetBuilder: (element) {
          if (element.localName == 'img') {
            String? src;
            src = element.attributes['src'];
            if (src != null) {
              return InkWell(
                onTap: () {
                  Get.dialog(
                    ImageView(imageUrl: src!),
                  );
                },
                child: SizedBox(
                  width: double.maxFinite,
                  child: CustomCachedNetworkImage(
                    imageUrl: src,
                    fit: BoxFit.cover,
                  ),
                ),
              );
            }
          }
          return null;
        },
        customStylesBuilder: (element) {
          if (['h1', 'h2', 'h3', 'h4', 'h5', 'h6']
              .contains(element.localName)) {
            // convert color to hex color
            String color = AppColors.primaryColor.toHex();
            return {
              "color": color,
            };
          }
          return null;
        },
        textStyle: const TextStyle(
          fontSize: 17,
        ),
        // customRender: {
        //   "figure": (RenderContext context, Widget child) {
        //     String? src;
        //     var image = context.tree.element?.getElementsByTagName('img');
        //     if (image != null) {
        //       src = image.first.attributes['src'];
        //     }

        //     if (src != null) {
        //       return InkWell(
        //         onTap: () {
        //           Get.toNamed(Routes.IMAGE_VIEW_PAGE, arguments: src);
        //         },
        //         child: SizedBox(
        //           width: double.maxFinite,
        //           child: CustomCachedNetworkImage(
        //             imageUrl: src,
        //             fit: BoxFit.cover,
        //           ),
        //         ),
        //       );
        //     }
        //     return null;
        //   },
        //   "a": (context, parsedChild) {
        //     var href = context.tree.attributes['href'];
        //     var text = context.tree.element?.text;
        //     return InkWell(
        //       child: Text(
        //         text ?? "",
        //         style: const TextStyle(
        //           decoration: TextDecoration.underline,
        //           decorationThickness: 1,
        //         ),
        //       ),
        //       onTap: () async {
        //         if (await canLaunchUrl(Uri.parse(href ?? ""))) {
        //           launchUrl(Uri.parse(href ?? ""));
        //         }
        //       },
        //     );
        //   },
        // },
        // style: {
        //   'body': Style(margin: EdgeInsets.zero),
        //   'figure': Style(margin: EdgeInsets.zero),
        //   '*': Style(
        //     // color: Colors.black,
        //     textAlign: TextAlign.right,
        //     fontFamily: GoogleFonts.tajawal().fontFamily,
        //     padding: EdgeInsets.zero,
        //   ),
        //   'h1,h2,h3,h4,h5,h6': Style(
        //     color: Get.theme.primaryColor,
        //   ),
        //   'p': Style(
        //       padding: EdgeInsets.zero,
        //       width: double.maxFinite,
        //       fontSize: FontSize.em(1.1)),
        // },
      ),
    );
  }
}

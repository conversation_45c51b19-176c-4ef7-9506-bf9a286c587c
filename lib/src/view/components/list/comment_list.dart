import 'package:almashal/src/controllers/comments_controller.dart';
import 'package:almashal/src/data/enums/page_loading_status.dart';
import 'package:almashal/src/data/services/auth_service.dart';
import 'package:almashal/src/view/components/cards/comment_card.dart';
import 'package:almashal/src/view/components/list/replies_list.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';

class CommentList extends StatelessWidget {
  const CommentList({
    super.key,
    required this.type,
    required this.id,
  });
  final String type;
  final int id;

  @override
  Widget build(BuildContext context) {
    var controller = Get.put<CommentsController>(
      CommentsController(
        type: type,
        id: id,
      ),
      tag: '${type}_$id',
    );
    return Column(
      children: [
        const SizedBox(height: 32),
        const Text(
          'التعليقات',
          style: TextStyle(
            fontSize: 20,
            fontWeight: FontWeight.w500,
            color: Colors.black,
          ),
        ),
        const SizedBox(
          height: 8,
        ),
        Obx(
          () {
            final comments = controller.comments.value;
            if (controller.pageLoadingStatus.value ==
                PageLoadingStatus.loading) {
              return Center(
                child: SpinKitWave(
                  color: Get.theme.primaryColor,
                  size: 30,
                ),
              );
            }
            if (comments.isEmpty) {
              return const Center(
                child: Text('لا يوجد تعليقات'),
              );
            }
            return ListView.builder(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              itemCount: comments.length,
              itemBuilder: (context, index) {
                if ((index == comments.length - 1) &&
                    !controller.cantLoadMore()) {
                  controller.loadMore();
                  return Column(
                    children: [
                      CommentCard(
                        comment: comments[index],
                        onLike: () => comments[index].liked
                            ? controller.unlikeComment(comments[index].id)
                            : controller.likeComment(comments[index].id),
                        onReply: () {
                          // Show bottom sheet for reply
                          Get.bottomSheet(
                            SingleChildScrollView(
                              child: Container(
                                padding: const EdgeInsets.all(16),
                                decoration: const BoxDecoration(
                                  color: Colors.white,
                                  borderRadius: BorderRadius.only(
                                    topLeft: Radius.circular(16),
                                    topRight: Radius.circular(16),
                                  ),
                                ),
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    const Text(
                                      'الرد على التعليق',
                                      style: TextStyle(
                                        fontSize: 18,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                    const SizedBox(height: 16),
                                    // Display the comment being replied to
                                    CommentCard(comment: comments[index]),
                                    const SizedBox(height: 16),
                                    // Display replies list
                                    RepliesList(
                                      replies: comments[index].replies ?? [],
                                      type: type,
                                      id: id,
                                    ),
                                    const SizedBox(height: 16),
                                    // Add reply input field and send button
                                    Container(
                                      padding: const EdgeInsets.symmetric(
                                          horizontal: 8.0),
                                      decoration: BoxDecoration(
                                        color: Colors.grey[200],
                                        borderRadius:
                                            BorderRadius.circular(24.0),
                                      ),
                                      child: Row(
                                        children: [
                                          Expanded(
                                            child: TextField(
                                              decoration: const InputDecoration(
                                                hintText: 'اكتب ردك هنا...',
                                                border: InputBorder.none,
                                                contentPadding:
                                                    EdgeInsets.symmetric(
                                                        horizontal: 16.0,
                                                        vertical: 8.0),
                                              ),
                                              textDirection: TextDirection.rtl,
                                              controller: controller
                                                  .replyTextController,
                                              onSubmitted: (value) {
                                                if (value.trim().isNotEmpty) {
                                                  controller.addComment({
                                                    'body': value,
                                                    'parent_id':
                                                        comments[index].id,
                                                    'commentable_type': type,
                                                    'commentable_id': id,
                                                  });
                                                }
                                              },
                                            ),
                                          ),
                                          IconButton(
                                            icon: const Icon(Icons.send,
                                                color: Colors.blue),
                                            onPressed: () {
                                              if (controller
                                                  .replyTextController.text
                                                  .trim()
                                                  .isNotEmpty) {
                                                // الحصول على مراقب التعليقات الخاص بقائمة الردود
                                                final repliesController = Get
                                                    .find<CommentsController>(
                                                  tag:
                                                      'replies_${comments[index].id}',
                                                );

                                                print(
                                                    "الرد على التعليق رقم ${comments[index].id} (زر الإرسال)");

                                                // استخدام المراقب الخاص بالردود لإضافة رد جديد
                                                repliesController.addComment({
                                                  'body': controller
                                                      .replyTextController.text,
                                                  'commentable_id':
                                                      comments[index].id,
                                                  'commentable_type': 'comment',
                                                });
                                                controller.replyTextController
                                                    .clear();
                                              }
                                            },
                                          ),
                                        ],
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                            ),
                          );
                        },
                        onDelete: AuthService
                                    .instance.userData.value?.user.id ==
                                comments[index].user?.id
                            ? () => controller.deleteComment(comments[index].id)
                            : null,
                      ),
                      const SizedBox(
                        height: 100,
                        child: SpinKitWave(
                          color: Colors.grey,
                          size: 30,
                        ),
                      ),
                    ],
                  );
                }
                return Padding(
                  padding: const EdgeInsets.only(bottom: 16.0),
                  child: CommentCard(
                    comment: comments[index],
                    onLike: () => comments[index].liked
                        ? controller.unlikeComment(comments[index].id)
                        : controller.likeComment(comments[index].id),
                    onReply: () {
                      // Show bottom sheet for reply
                      Get.bottomSheet(
                        SingleChildScrollView(
                          child: Container(
                            padding: const EdgeInsets.all(16),
                            decoration: const BoxDecoration(
                              color: Colors.white,
                              borderRadius: BorderRadius.only(
                                topLeft: Radius.circular(16),
                                topRight: Radius.circular(16),
                              ),
                            ),
                            child: Column(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Text(
                                  'الرد على التعليق',
                                  style: TextStyle(
                                    fontSize: 18,
                                    fontWeight: FontWeight.bold,
                                  ),
                                ),
                                const SizedBox(height: 16),
                                // Display the comment being replied to
                                CommentCard(comment: comments[index]),
                                const SizedBox(height: 16),
                                // Display replies list
                                RepliesList(
                                  replies: comments[index].replies ?? [],
                                  type: type,
                                  id: id,
                                ),
                                const SizedBox(height: 16),
                                // Add reply input field and send button
                                Container(
                                  padding: const EdgeInsets.symmetric(
                                      horizontal: 8.0),
                                  decoration: BoxDecoration(
                                    color: Colors.grey[200],
                                    borderRadius: BorderRadius.circular(24.0),
                                  ),
                                  child: Row(
                                    children: [
                                      Expanded(
                                        child: TextField(
                                          decoration: const InputDecoration(
                                            hintText: 'اكتب ردك هنا...',
                                            border: InputBorder.none,
                                            contentPadding:
                                                EdgeInsets.symmetric(
                                                    horizontal: 16.0,
                                                    vertical: 8.0),
                                          ),
                                          textDirection: TextDirection.rtl,
                                          controller:
                                              controller.replyTextController,
                                          onSubmitted: (value) {
                                            if (value.trim().isNotEmpty) {
                                              // الحصول على مراقب التعليقات الخاص بقائمة الردود
                                              final repliesController =
                                                  Get.find<CommentsController>(
                                                tag:
                                                    'replies_${comments[index].id}',
                                              );

                                              print(
                                                  "الرد على التعليق رقم ${comments[index].id} (زر الإرسال)");

                                              // استخدام المراقب الخاص بالردود لإضافة رد جديد
                                              repliesController.addComment({
                                                'body': value,
                                                'commentable_id':
                                                    comments[index].id,
                                                'commentable_type': 'comment',
                                              });

                                              // تنظيف حقل النص
                                              controller.replyTextController
                                                  .clear();
                                            }
                                          },
                                        ),
                                      ),
                                      IconButton(
                                        icon: const Icon(Icons.send,
                                            color: Color.fromARGB(
                                                255, 169, 136, 88)),
                                        onPressed: () {
                                          if (controller
                                              .replyTextController.text
                                              .trim()
                                              .isNotEmpty) {
                                            // الحصول على مراقب التعليقات الخاص بقائمة الردود
                                            final repliesController =
                                                Get.find<CommentsController>(
                                              tag:
                                                  'replies_${comments[index].id}',
                                            );

                                            print(
                                                "الرد على التعليق رقم ${comments[index].id} (زر الإرسال)");

                                            // استخدام المراقب الخاص بالردود لإضافة رد جديد
                                            repliesController.addComment({
                                              'body': controller
                                                  .replyTextController.text,
                                              'commentable_id':
                                                  comments[index].id,
                                              'commentable_type': 'comment',
                                            });

                                            // تنظيف حقل النص
                                            controller.replyTextController
                                                .clear();
                                          }
                                        },
                                      ),
                                    ],
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      );
                    },
                    onDelete: AuthService.instance.userData.value?.user.id ==
                            comments[index].user?.id
                        ? () => controller.deleteComment(comments[index].id)
                        : null,
                  ),
                );
              },
            );
          },
        ),
        SizedBox(
          height: 54,
        )
      ],
    );
  }
}

import 'package:almashal/src/controllers/comments_controller.dart';
import 'package:almashal/src/data/enums/page_loading_status.dart';
import 'package:almashal/src/data/models/comment.dart';
import 'package:almashal/src/data/services/auth_service.dart';
import 'package:almashal/src/view/components/cards/comment_card.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';

class RepliesList extends StatelessWidget {
  const RepliesList({
    super.key,
    required this.replies,
    required this.type,
    required this.id,
  });
  final List<Comment> replies;
  final String type;
  final int id;
  @override
  Widget build(BuildContext context) {
    var controller = Get.find<CommentsController>(tag: '${type}_$id');
    if (replies.isEmpty) {
      return const Center(
        child: Text('لا يوجد ردود'),
      );
    }
    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: replies.length,
      itemBuilder: (context, index) {
        return Padding(
          padding: const EdgeInsets.only(bottom: 16.0),
          child: CommentCard(
            comment: replies[index],
            onLike: () => replies[index].liked
                ? controller.unlikeComment(replies[index].id)
                : controller.likeComment(replies[index].id),
            onDelete: AuthService.instance.userData.value?.user.id ==
                    replies[index].user?.id
                ? () => controller.deleteComment(replies[index].id)
                : null,
          ),
        );
      },
    );
  }
}

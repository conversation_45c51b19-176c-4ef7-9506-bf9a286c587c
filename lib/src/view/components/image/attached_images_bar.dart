import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/values/app_config.dart';
import '../../../core/values/app_styles.dart';
import 'custom_cached_network_image.dart';

class AttachedImagesBar extends StatelessWidget {
  final List<String> images;
  const AttachedImagesBar({
    super.key,
    required this.images,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'الصور المرفقة',
          style: TextStyle(
            fontWeight: FontWeight.bold,
            fontSize: 16,
            color: Get.theme.primaryColor,
          ),
        ),
        const SizedBox(
          height: 16,
        ),
        SizedBox(
          height: 90,
          child: ListView.builder(
            itemCount: images.length,
            scrollDirection: Axis.horizontal,
            itemBuilder: (BuildContext context, int index) {
              return Padding(
                padding: const EdgeInsetsDirectional.only(end: 10),
                child: ClipRRect(
                  borderRadius: AppStyles.cardBorderRadius,
                  child: AspectRatio(
                    aspectRatio: 1.5,
                    child: CustomCachedNetworkImage(
                      imageUrl: AppConfig.thumbBaseUrl + images[index],
                      fit: BoxFit.cover,
                      withPreview: true,
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}

import 'package:almashal/src/core/utils/common_functions.dart';
import 'package:almashal/src/core/values/app_styles.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';

class CustomCachedNetworkImage extends StatelessWidget {
  const CustomCachedNetworkImage({
    super.key,
    required this.imageUrl,
    this.fit,
    this.borderRadius,
    this.width,
    this.height,
    this.thumbImageUrl,
    this.withPreview = false,
  });

  final String imageUrl;
  final String? thumbImageUrl;
  final BoxFit? fit;
  final BorderRadius? borderRadius;
  final double? width;
  final double? height;
  final bool withPreview;

  @override
  Widget build(BuildContext context) {
    return ClipRRect(
      borderRadius: borderRadius ?? AppStyles.cardBorderRadius,
      child: InkWell(
        onTap: withPreview
            ? () {
                CommonFunctions.showImagePreviewDialog(imageUrl);
              }
            : null,
        child: CachedNetworkImage(
          fit: fit,
          width: width,
          height: height,
          errorWidget: (context, url, error) {
            return Image.network(
                "https://almashalfamily.com/assets/images/male-avatar.jpg");
          },
          progressIndicatorBuilder: (context, url, progress) {
            return Stack(
              alignment: Alignment.center,
              children: [
                if (thumbImageUrl != null)
                  CachedNetworkImage(
                    imageUrl: thumbImageUrl!,
                    fit: fit,
                    width: width,
                    height: height,
                  ),
                SpinKitRipple(
                  color: Get.theme.primaryColor,
                  size: 50,
                ),
              ],
            );
          },
          imageUrl: imageUrl,
        ),
      ),
    );
  }
}

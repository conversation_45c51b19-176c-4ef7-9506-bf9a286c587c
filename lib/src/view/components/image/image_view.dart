import 'package:almashal/src/core/values/app_config.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:photo_view/photo_view.dart';
import 'package:share_plus/share_plus.dart';
import 'package:flutter_cache_manager/flutter_cache_manager.dart';

class ImageView extends StatefulWidget {
  const ImageView({super.key, required this.imageUrl});
  final String imageUrl;
  @override
  State<ImageView> createState() => _ImageViewState();
}

class _ImageViewState extends State<ImageView> {
  String thumbImageUrl = "";
  String imageUrl = "";
  @override
  void initState() {
    print(widget.imageUrl);
    print(AppConfig.thumbBaseUrl);
    if (widget.imageUrl.contains(AppConfig.thumbBaseUrl)) {
      thumbImageUrl = widget.imageUrl;
      imageUrl = widget.imageUrl
          .replaceAll(AppConfig.thumbBaseUrl, AppConfig.imageBaseUrl);
    } else {
      imageUrl = widget.imageUrl;
      thumbImageUrl = widget.imageUrl
          .replaceAll(AppConfig.imageBaseUrl, AppConfig.thumbBaseUrl);
    }
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Material(
      color: Colors.black.withOpacity(0.8),
      child: Center(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.end,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(vertical: 8.0),
              color: Colors.black.withOpacity(0.3),
              child: Row(
                children: [
                  IconButton(
                    onPressed: () {
                      Get.back();
                    },
                    icon: const Icon(
                      Icons.close,
                      color: Colors.white,
                    ),
                  ),
                  const Spacer(),
                  IconButton(
                    onPressed: () async {
                      // get file from cache image
                      final file = await DefaultCacheManager()
                          .getFileFromCache(imageUrl);
                      if (file?.file.path != null) {
                        Share.shareXFiles([XFile(file!.file.path)]);
                      }
                    },
                    icon: const Icon(
                      Icons.share,
                      color: Colors.white,
                    ),
                  ),
                ],
              ),
            ),
            Expanded(
              child: PhotoView(
                basePosition: Alignment.center,
                backgroundDecoration:
                    const BoxDecoration(color: Colors.transparent),
                loadingBuilder: (context, event) {
                  return SizedBox(
                    width: double.maxFinite,
                    child: Stack(
                      alignment: Alignment.center,
                      children: [
                        CachedNetworkImage(
                          imageUrl: thumbImageUrl,
                          width: double.maxFinite,
                        ),
                        Center(
                          child: SpinKitRipple(
                            color: Get.theme.primaryColor,
                            size: 50,
                          ),
                        ),
                      ],
                    ),
                  );
                },
                minScale: PhotoViewComputedScale.contained * 0.5,
                maxScale: PhotoViewComputedScale.covered * 10,
                imageProvider: CachedNetworkImageProvider(
                  imageUrl,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }
}

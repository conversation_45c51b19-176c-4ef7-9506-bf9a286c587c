import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

import '../../../core/routes/app_pages.dart';
import '../../../core/values/assets.dart';
import 'custom_cached_network_image.dart';

class AttachedYoutubeVideosBar extends StatelessWidget {
  final List<String> youtubeVideos;
  const AttachedYoutubeVideosBar({super.key, required this.youtubeVideos});

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Padding(
          padding: const EdgeInsets.symmetric(vertical: 16.0),
          child: Text(
            'الفيديوهات المرفقة',
            style: TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 16,
              color: Get.theme.primaryColor,
            ),
          ),
        ),
        SizedBox(
          height: 90,
          child: ListView.builder(
            itemCount: youtubeVideos.length,
            scrollDirection: Axis.horizontal,
            itemBuilder: (BuildContext context, int index) {
              return Padding(
                padding: const EdgeInsetsDirectional.only(end: 10),
                child: AspectRatio(
                  aspectRatio: 1.5,
                  child: InkWell(
                    onTap: () {
                      Get.toNamed(
                        Routes.VIDEO_VIEW_PAGE,
                        arguments: {
                          'media': youtubeVideos[index],
                        },
                      );
                    },
                    child: SizedBox(
                      width: double.maxFinite,
                      child: Stack(
                        alignment: Alignment.center,
                        children: [
                          CustomCachedNetworkImage(
                            imageUrl:
                                "https://img.youtube.com/vi/${youtubeVideos[index]}/default.jpg",
                            fit: BoxFit.cover,
                          ),
                          SvgPicture.asset(
                            Assets.play,
                            width: 20,
                          ),
                        ],
                      ),
                    ),
                  ),
                ),
              );
            },
          ),
        ),
      ],
    );
  }
}

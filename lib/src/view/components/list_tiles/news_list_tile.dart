import 'package:almashal/src/view/components/list_tiles/custom_image_list_tile.dart';
import 'package:flutter/material.dart';

class NewsListTile extends StatelessWidget {
  final String text;
  final String imageUrl;
  final String date;
  final VoidCallback onTap;
  final EdgeInsetsGeometry? margin;
  const NewsListTile(
      {super.key,
      required this.text,
      required this.imageUrl,
      required this.date,
      required this.onTap,
      this.margin});

  @override
  Widget build(BuildContext context) {
    return CustomImageListTile(
      margin: margin,
      imageUrl: imageUrl,
      title: text,
      subtitle: date,
      onTap: onTap,
    );
  }
}

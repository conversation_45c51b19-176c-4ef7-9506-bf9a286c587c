import 'package:almashal/src/core/values/app_styles.dart';
import 'package:almashal/src/view/components/image/custom_cached_network_image.dart';
import 'package:feather_icons/feather_icons.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class CustomImageListTile extends StatelessWidget {
  final EdgeInsetsGeometry? margin;
  final String imageUrl;
  final String title;
  final String? subtitle;
  final VoidCallback? onTap;
  const CustomImageListTile({
    super.key,
    this.margin,
    required this.imageUrl,
    this.onTap,
    required this.title,
    this.subtitle,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      decoration: AppStyles.cardBoxdecoration,
      child: AspectRatio(
        aspectRatio: 3.6,
        child: Material(
          color: Colors.transparent,
          borderRadius: AppStyles.cardBorderRadius,
          child: InkWell(
            borderRadius: AppStyles.cardBorderRadius,
            onTap: onTap,
            child: Row(
              children: [
                Container(
                  decoration: BoxDecoration(
                    borderRadius: AppStyles.cardBorderRadius,
                    border: Border.all(
                      color: Colors.grey.shade200,
                    ),
                  ),
                  margin: const EdgeInsets.all(8.0),
                  child: AspectRatio(
                    aspectRatio: 1.2,
                    child: ClipRRect(
                      borderRadius: AppStyles.cardBorderRadius,
                      child: CustomCachedNetworkImage(
                        imageUrl: imageUrl,
                        fit: BoxFit.cover,
                      ),
                    ),
                  ),
                ),
                Expanded(
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Text(
                          title,
                          maxLines: 2,
                          style: TextStyle(
                            fontSize: 15,
                            fontWeight: FontWeight.w600,
                            color: Colors.grey.shade800,
                          ),
                        ),
                        if (subtitle != null)
                          Padding(
                            padding: const EdgeInsets.only(top: 8.0),
                            child: Text(
                              subtitle!,
                              style: TextStyle(
                                color: Get.theme.colorScheme.secondary,
                              ),
                            ),
                          ),
                      ],
                    ),
                  ),
                ),
                Icon(
                  FeatherIcons.chevronRight,
                  color: Get.theme.primaryColor,
                  size: 25,
                ),
                SizedBox(
                  width: 8,
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

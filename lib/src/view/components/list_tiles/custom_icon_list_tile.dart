import 'package:almashal/src/core/values/app_styles.dart';
import 'package:feather_icons/feather_icons.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

class CustomIconListTile extends StatelessWidget {
  const CustomIconListTile(
      {super.key,
      required this.svgAsset,
      required this.title,
      this.subtitle,
      this.onTap,
      this.last = false,
      this.svgSize});
  final String svgAsset;
  final String title;
  final String? subtitle;
  final VoidCallback? onTap;
  final bool last;
  final double? svgSize;
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: last ? null : const EdgeInsets.only(bottom: 16),
      decoration: AppStyles.cardBoxdecoration,
      child: AspectRatio(
        aspectRatio: 3.6,
        child: Material(
          color: Colors.transparent,
          borderRadius: AppStyles.cardBorderRadius,
          child: InkWell(
            borderRadius: AppStyles.cardBorderRadius,
            onTap: onTap,
            child: Padding(
              padding: const EdgeInsets.all(8),
              child: Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  svgSize == null
                      ? Padding(
                          padding: const EdgeInsets.all(8.0),
                          child: AspectRatio(
                            aspectRatio: 1.2,
                            child: SvgPicture.asset(
                              svgAsset,
                              width: 50,
                              color: Get.theme.primaryColor,
                            ),
                          ),
                        )
                      : Padding(
                          padding: const EdgeInsets.all(16.0),
                          child: SvgPicture.asset(
                            svgAsset,
                            width: svgSize,
                            color: Get.theme.primaryColor,
                          ),
                        ),
                  const SizedBox(
                    width: 16,
                  ),
                  Expanded(
                    child: Text(
                      title,
                      style: TextStyle(
                        fontSize: 15,
                        fontWeight: FontWeight.w600,
                        color: Colors.grey.shade800,
                      ),
                    ),
                  ),
                  Icon(
                    FeatherIcons.chevronRight,
                    color: Get.theme.primaryColor,
                    size: 25,
                  ),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

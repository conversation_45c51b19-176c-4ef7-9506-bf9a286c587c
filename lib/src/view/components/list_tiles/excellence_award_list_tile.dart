import 'package:almashal/src/core/routes/app_pages.dart';
import 'package:almashal/src/core/values/assets.dart';
import 'package:almashal/src/data/models/excellence_award.dart';
import 'package:almashal/src/view/components/list_tiles/custom_icon_list_tile.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AwardListTitle extends StatelessWidget {
  const AwardListTitle({
    super.key,
    required this.award,
    this.last = false,
  });
  final ExcellenceAward award;
  final bool last;
  @override
  Widget build(BuildContext context) {
    return CustomIconListTile(
      svgSize: 60,
      svgAsset: Assets.medal,
      title: award.title,
      onTap: () {
        Get.toNamed(
          Routes.EXCELLENCE_AWARD_DETAIL_PAGE,
          arguments: award,
        );
      },
    );
  }
}

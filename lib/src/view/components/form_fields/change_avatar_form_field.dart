import 'package:almashal/src/core/utils/platform_helper.dart';
import 'package:almashal/src/view/components/bottomsheets/custom_bottom_sheet.dart';
import 'package:almashal/src/view/components/image/custom_cached_network_image.dart';
import 'package:feather_icons/feather_icons.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:image_picker/image_picker.dart';

class ChangeAvatarFormField extends StatelessWidget {
  const ChangeAvatarFormField({
    super.key,
    this.imageUrl,
    this.onSaved,
    this.onChanged,
  });

  final String? imageUrl;
  final Function(XFile?)? onSaved;
  final Function(XFile?)? onChanged;

  @override
  Widget build(BuildContext context) {
    return FormField<XFile>(
      onSaved: onSaved,
      builder: (FormFieldState<XFile> field) {
        return Stack(
          children: [
            Container(
              width: 100,
              height: 100,
              decoration: BoxDecoration(
                color: Theme.of(context).primaryColor.withOpacity(0.1),
                shape: BoxShape.circle,
                boxShadow: [
                  BoxShadow(
                    color: Colors.black.withOpacity(0.1),
                    blurRadius: 10,
                    offset: const Offset(0, 5),
                  ),
                ],
              ),
              child: imageUrl != null && imageUrl!.isNotEmpty
                  ? CustomCachedNetworkImage(
                      imageUrl: imageUrl!,
                      borderRadius: BorderRadius.circular(100),
                      width: 100,
                      height: 100,
                      fit: BoxFit.cover,
                    )
                  : CircleAvatar(
                      backgroundColor: Theme.of(context).primaryColor,
                      child: const Icon(
                        Icons.person,
                        color: Colors.white,
                        size: 50,
                      ),
                    ),
            ),
            Positioned(
              bottom: 0,
              right: 0,
              left: 0,
              child: Container(
                height: 35,
                decoration: BoxDecoration(
                  borderRadius: const BorderRadius.vertical(
                    bottom: Radius.circular(100),
                  ),
                  color: Theme.of(context).primaryColor.withOpacity(0.7),
                ),
                child: InkWell(
                  borderRadius: const BorderRadius.vertical(
                    bottom: Radius.circular(100),
                  ),
                  onTap: () {
                    Get.bottomSheet(
                      CustomBottomSheet(
                        title: 'تغيير الصورة الشخصية',
                        body: Column(
                          children: [
                            ListTile(
                              title: const Text('الكاميرا'),
                              leading: const Icon(FeatherIcons.camera),
                              onTap: () async {
                                final ImagePicker picker = ImagePicker();
                                if (PlatformHelper.isWeb) {
                                  // استخدام خيار الجاليري للويب حيث أن الكاميرا قد لا تكون متاحة
                                  final XFile? image = await picker.pickImage(
                                    source: ImageSource.gallery,
                                    imageQuality: 50,
                                  );
                                  if (image != null) {
                                    onChanged?.call(image);
                                    field.didChange(image);
                                  }
                                } else {
                                  final XFile? image = await picker.pickImage(
                                    source: ImageSource.camera,
                                    imageQuality: 50,
                                  );
                                  if (image != null) {
                                    onChanged?.call(image);
                                    field.didChange(image);
                                  }
                                }
                                Get.back();
                              },
                            ),
                            ListTile(
                              title: const Text('المعرض'),
                              leading: const Icon(FeatherIcons.image),
                              onTap: () async {
                                final ImagePicker picker = ImagePicker();
                                final XFile? image = await picker.pickImage(
                                  source: ImageSource.gallery,
                                  imageQuality: 50,
                                );
                                if (image != null) {
                                  onChanged?.call(image);
                                  field.didChange(image);
                                }
                                Get.back();
                              },
                            ),
                          ],
                        ),
                      ),
                    );
                  },
                  child: const Padding(
                    padding: EdgeInsets.all(8.0),
                    child: Icon(
                      FeatherIcons.edit,
                      size: 20,
                      color: Colors.white,
                    ),
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }
}

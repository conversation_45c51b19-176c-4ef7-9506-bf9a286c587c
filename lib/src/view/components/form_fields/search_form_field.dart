import 'package:almashal/src/view/components/form_fields/app_text_form_field.dart';
import 'package:feather_icons/feather_icons.dart';
import 'package:flutter/material.dart';

class SearchForm<PERSON>ield extends StatelessWidget {
  const SearchFormField({
    super.key,
    this.onSearchSubmitted,
    this.controller,
  });
  final Function(String?)? onSearchSubmitted;
  final TextEditingController? controller;

  @override
  Widget build(BuildContext context) {
    return Padding(
      padding: const EdgeInsets.only(
        top: 8,
        right: 16,
        left: 16,
      ),
      child: AppTextFormField(
        controller: controller,
        maxLines: 1,
        keyboardType: TextInputType.text,
        hintText: "بحث",
        prefixIcon: const Icon(FeatherIcons.search),
        textInputAction: TextInputAction.search,
        onFieldSubmitted: onSearchSubmitted,
      ),
    );
  }
}

import 'dart:io';

import 'package:almashal/src/core/theme/app_colors.dart';
import 'package:file_picker/file_picker.dart';
import 'package:flutter/material.dart';

import 'package:image_picker/image_picker.dart';

enum FileFieldType { file, network }

class FileFormField extends StatefulWidget {
  final FormFieldValidator<String>? validator;
  final String? initialValue;
  final EdgeInsetsGeometry? margin;
  final String? title;
  final FileFieldType? fileFieldType;
  final void Function(String?, FileFieldType?)? onSaved;

  const FileFormField({
    super.key,
    this.onSaved,
    this.validator,
    this.initialValue,
    this.margin,
    this.title,
    this.fileFieldType,
  });

  @override
  _FileFormFieldState createState() => _FileFormFieldState();
}

class _FileFormFieldState extends State<FileFormField> {
  late FileFieldType? fileFieldType;
  @override
  void initState() {
    fileFieldType = widget.fileFieldType;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return FormField(
        initialValue: widget.initialValue,
        validator: widget.validator,
        onSaved: (value) {
          if (widget.onSaved != null) {
            widget.onSaved!(value, fileFieldType);
          }
        },
        builder: (state) {
          return Container(
            margin: widget.margin ?? const EdgeInsets.only(bottom: 16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: <Widget>[
                if (widget.title != null)
                  Padding(
                    padding: EdgeInsetsDirectional.only(start: 8, bottom: 8.0),
                    child: Text(widget.title ?? "",
                        style: TextStyle(
                          color: Colors.grey.shade500,
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        )),
                  ),
                if (fileFieldType == FileFieldType.network &&
                    state.value != null)
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(7),
                      color: AppColors.primary.withValues(alpha: 0.1),
                    ),
                    child: InkWell(
                      borderRadius: BorderRadius.circular(7),
                      onTap: fileFieldType == FileFieldType.network
                          ? () {
                              // HelperFunction.openUrl(
                              //     imageBaseUrl + state.value!);
                            }
                          : null,
                      child: Container(
                        padding: EdgeInsets.all(10),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(Icons.download_rounded),
                            // SvgPicture.asset(
                            //   AppAssets.uploadFile,
                            //   height: 25,
                            //   fit: BoxFit.contain,
                            // ),
                            SizedBox(
                              width: 7,
                            ),
                            Flexible(
                              child: Text(
                                state.value!.split('/').last,
                                style: TextStyle(
                                  fontSize: 14,
                                  color: Colors.grey.shade500,
                                ),
                              ),
                            ),
                          ],
                        ),
                      ),
                    ),
                  )
                else
                  GestureDetector(
                    onTap: () async {
                      FilePickerResult? result =
                          await FilePicker.platform.pickFiles(
                        allowMultiple: true,
                        type: FileType.custom,
                        allowedExtensions: ['pdf'],
                      );
                      if (result != null) {
                        var file = File(result.files.single.path ?? "");
                        state.didChange(file.path);

                        fileFieldType = FileFieldType.file;
                        // onFileSelected(file, MediaType.video);
                      }
                    },
                    child: Container(
                      padding: EdgeInsets.all(10),
                      // height: 100,
                      width: double.maxFinite,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        color: Colors.white,
                        border: Border.all(
                          color: Colors.grey.shade300,
                        ),
                      ),
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(10),
                        child: Builder(builder: (ctx) {
                          // if (state.value == null) {
                          return Column(
                            children: [
                              Center(
                                child: Column(
                                  mainAxisSize: MainAxisSize.min,
                                  children: [
                                    Icon(
                                      Icons.upload_file,
                                      size: 50,
                                      color: AppColors.primary,
                                    ),
                                    // SvgPicture.asset(
                                    //   AppAssets.uploadFile,
                                    //   height: 50,
                                    //   fit: BoxFit.contain,
                                    // ),
                                    SizedBox(height: 5),
                                    Text(
                                      "إرفع ملف",
                                      style: TextStyle(
                                        color: AppColors.primary,
                                        fontSize: 16,
                                        fontWeight: FontWeight.bold,
                                      ),
                                    ),
                                  ],
                                ),
                              ),
                              if (state.value != null)
                                Container(
                                  decoration: BoxDecoration(
                                    borderRadius: BorderRadius.circular(7),
                                    color: AppColors.primary.withOpacity(0.1),
                                  ),
                                  child: InkWell(
                                    borderRadius: BorderRadius.circular(7),
                                    onTap:
                                        FileFieldType == FileFieldType.network
                                            ? () {}
                                            : null,
                                    child: Container(
                                      padding: EdgeInsets.all(10),
                                      child: Row(
                                        mainAxisSize: MainAxisSize.min,
                                        children: [
                                          Icon(Icons.download_rounded),
                                          // SvgPicture.asset(
                                          //   AppAssets.uploadFile,
                                          //   height: 25,
                                          //   fit: BoxFit.contain,
                                          // ),
                                          SizedBox(
                                            width: 7,
                                          ),
                                          Flexible(
                                            child: Text(
                                              state.value!.split('/').last,
                                              style: TextStyle(
                                                fontSize: 14,
                                                color: Colors.grey.shade500,
                                              ),
                                            ),
                                          ),
                                        ],
                                      ),
                                    ),
                                  ),
                                ),
                            ],
                          );
                          // }
                        }),
                      ),
                    ),
                  ),
                if (state.hasError)
                  Padding(
                    padding: const EdgeInsets.only(top: 5.0),
                    child: Text(
                      state.errorText ?? "",
                      style: TextStyle(color: Colors.red, fontSize: 12),
                    ),
                  ),
              ],
            ),
          );
        });
  }
}

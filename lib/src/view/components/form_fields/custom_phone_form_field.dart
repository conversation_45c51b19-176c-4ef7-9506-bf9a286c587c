import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:phone_form_field/phone_form_field.dart';

class CustomPhoneFormField extends StatelessWidget {
  const CustomPhoneFormField({
    super.key,
    this.validator,
    this.controller,
    this.initialValue,
    this.countrySelectorNavigator,
    this.onSaved,
    this.labelText,
    this.hintText,
    this.errorText,
    this.fillColor,
    required this.filled,
    this.prefixIcon,
    this.suffixIcon,
  });
  final String? Function(PhoneNumber?)? validator;
  final String? Function(PhoneNumber?)? onSaved;

  final PhoneController? controller;
  final PhoneNumber? initialValue;
  final CountrySelectorNavigator? countrySelectorNavigator;
  final String? labelText;
  final String? hintText;
  final String? errorText;
  final Color? fillColor;
  final bool filled;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  @override
  Widget build(BuildContext context) {
    return PhoneFormField(
      controller: controller,
      initialValue: initialValue,
      validator: validator,
      onSaved: onSaved,
      decoration: InputDecoration(
        filled: filled,
        fillColor: fillColor ?? Colors.transparent,
        labelText: labelText,
        prefixIcon: prefixIcon,
        suffixIcon: suffixIcon,
        alignLabelWithHint: true,
        errorText: errorText,
        hintText: hintText,
        hintStyle: TextStyle(
          color: Theme.of(context).hintColor,
        ),
        // مستخدم عند حالة عادية
        border: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: Get.theme.primaryColor.withOpacity(0.3),
          ),
        ),
        // مستخدم عندما يكون الحقل مفعل ولكن بدون تركيز
        enabledBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: Get.theme.primaryColor.withOpacity(0.3),
          ),
        ),
        // مستخدم عندما يكون الحقل مركز عليه (في وضع الكتابة)
        focusedBorder: OutlineInputBorder(
          borderRadius: BorderRadius.circular(12),
          borderSide: BorderSide(
            color: Get.theme.primaryColor,
            width: 1.5,
          ),
        ),
        // منع تغير اللون عند التركيز (فوكس)
        focusColor: Colors.transparent,
        // منع تأثير التحويم
        hoverColor: Colors.transparent,
        contentPadding: const EdgeInsets.symmetric(
          vertical: 16,
          horizontal: 16,
        ),
      ),
      countrySelectorNavigator: countrySelectorNavigator ??
          CountrySelectorNavigator.bottomSheet(
            countries: IsoCode.values
                .where(
                  (country) => country != IsoCode.IL,
                )
                .toList(),
          ),
    );
  }
}

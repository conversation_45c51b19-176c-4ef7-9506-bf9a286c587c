import 'package:feather_icons/feather_icons.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

class CustomDropdownFormField<T> extends StatelessWidget {
  final List<DropdownMenuItem<T>> items;
  // selectedItemBuilder
  final List<Widget> Function(BuildContext context)? selectedItemBuilder;
  final void Function(T? value)? onSave;
  final String? Function(T? value)? validator;
  final void Function(T? value)? onChanged;
  final int? maxLine;
  final Color? borderColor;
  final Widget label;
  final double? borderWidth;
  final Widget? prefixIcon;
  final Widget? prefix;
  final Widget? suffix;
  final EdgeInsetsGeometry? margin;
  final String? hintText;
  final String? titleText;
  final bool obscureText;
  final TextInputType keyboardType;
  final int? maxLength;
  final int? minLines;
  final T? value;
  final Widget? suffixIcon;
  final TextEditingController? controller;
  final bool readOnly;
  final FocusNode? focusNode;
  final TextInputAction? textInputAction;
  final TextAlign? textAlign;
  final List<TextInputFormatter> inputFormatters;
  final Color? dropdownColor;
  final String? errorText;
  const CustomDropdownFormField({
    super.key,
    this.onSave,
    this.validator,
    this.onChanged,
    this.maxLine,
    required this.label,
    this.prefixIcon,
    this.hintText,
    this.titleText,
    this.obscureText = false,
    this.keyboardType = TextInputType.text,
    this.maxLength,
    this.minLines,
    this.value,
    this.suffixIcon,
    this.controller,
    this.readOnly = false,
    this.margin,
    this.focusNode,
    this.textInputAction,
    this.inputFormatters = const [],
    this.borderWidth,
    this.borderColor,
    this.prefix,
    this.suffix,
    this.textAlign,
    required this.items,
    this.selectedItemBuilder,
    this.dropdownColor,
    this.errorText,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          if (titleText != null)
            Padding(
              padding:
                  const EdgeInsets.symmetric(vertical: 8.0, horizontal: 16),
              child: Text(
                titleText!,
                style: TextStyle(
                  color: Get.theme.primaryColor,
                ),
              ),
            ),
          DropdownButtonFormField<T>(
            isDense: false,
            iconEnabledColor: Get.theme.colorScheme.secondary,
            focusNode: focusNode,
            onSaved: onSave,
            onChanged: onChanged,
            value: value,
            validator: validator,
            dropdownColor: dropdownColor,
            iconSize: 15,
            selectedItemBuilder: selectedItemBuilder,
            icon: const Icon(
              FeatherIcons.chevronDown,
              size: 15,
            ),
            style: TextStyle(
              color: Colors.black,
              fontFamily: Get.theme.textTheme.titleMedium?.fontFamily,
            ),
            decoration: InputDecoration(
              filled: true,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 0,
              ),
              errorText: errorText,
              border: Get.theme.inputDecorationTheme.border!.copyWith(
                borderSide: BorderSide(
                  color:
                      Get.theme.inputDecorationTheme.border!.borderSide.color,
                  width: borderWidth ??
                      Get.theme.inputDecorationTheme.border!.borderSide.width,
                ),
              ),
              errorBorder: Get.theme.inputDecorationTheme.errorBorder!.copyWith(
                borderSide: BorderSide(
                  color: Get
                      .theme.inputDecorationTheme.errorBorder!.borderSide.color,
                  width: borderWidth ??
                      Get.theme.inputDecorationTheme.errorBorder!.borderSide
                          .width,
                ),
              ),
              focusedBorder:
                  Get.theme.inputDecorationTheme.focusedBorder!.copyWith(
                borderSide: BorderSide(
                  color: Get.theme.inputDecorationTheme.focusedBorder!
                      .borderSide.color,
                  width: borderWidth ??
                      Get.theme.inputDecorationTheme.focusedBorder!.borderSide
                          .width,
                ),
              ),
              enabledBorder:
                  Get.theme.inputDecorationTheme.enabledBorder!.copyWith(
                borderSide: BorderSide(
                  color: borderColor ??
                      Get.theme.inputDecorationTheme.enabledBorder!.borderSide
                          .color,
                  width: borderWidth ??
                      (Get.theme.inputDecorationTheme.enabledBorder?.borderSide
                              .width ??
                          1),
                ),
              ),
              focusedErrorBorder:
                  Get.theme.inputDecorationTheme.focusedErrorBorder?.copyWith(
                borderSide: BorderSide(
                  color: Get.theme.inputDecorationTheme.focusedErrorBorder!
                      .borderSide.color,
                  width: borderWidth ??
                      Get.theme.inputDecorationTheme.focusedErrorBorder!
                          .borderSide.width,
                ),
              ),
              hintText: hintText,
              label: label,
              prefixIconColor: Get.theme.primaryColor,
              suffixIconColor: Get.theme.primaryColor,
              prefixIcon: prefixIcon,
              suffixIcon: suffixIcon,
              prefix: prefix,
              suffix: suffix,
            ),
            items: items,
            isExpanded: true,
          ),
        ],
      ),
    );
  }
}

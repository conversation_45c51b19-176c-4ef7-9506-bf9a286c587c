import 'package:flutter/material.dart';
import 'package:get/route_manager.dart';

class AppTextForm<PERSON>ield extends StatefulWidget {
  final TextEditingController? controller;
  final String? labelText;
  final String? initialValue;
  final String? hintText;
  final bool? obscureText;
  final bool? autofocus;
  final bool? enabled;
  final bool? autocorrect;
  final bool? autovalidate;
  final FormFieldValidator<String>? validator;
  final FormFieldSetter<String>? onSaved;
  final FormFieldSetter<String>? onChanged;
  final ValueChanged<String>? onFieldSubmitted;
  final Widget? prefixIcon;
  final Widget? suffixIcon;
  final EdgeInsetsGeometry? margin;
  final TextInputType? keyboardType;
  final TextInputAction? textInputAction;
  final int? maxLines;
  final String? validationName;
  final String? errorText;
  final Color? fillColor;
  final bool filled;
  final bool revealablePassword;
  final BorderRadius? borderRadius;
  const AppTextFormField({
    super.key,
    this.controller,
    this.labelText,
    this.hintText,
    this.obscureText,
    this.autofocus,
    this.enabled,
    this.autocorrect,
    this.autovalidate,
    this.validator,
    this.onSaved,
    this.onChanged,
    this.onFieldSubmitted,
    this.prefixIcon,
    this.suffixIcon,
    this.margin,
    this.keyboardType,
    this.maxLines,
    this.validationName,
    this.errorText,
    this.initialValue,
    this.textInputAction,
    this.fillColor,
    this.filled = true,
    this.revealablePassword = false,
    this.borderRadius,
  });

  @override
  State<AppTextFormField> createState() => _AppTextFormFieldState();
}

class _AppTextFormFieldState extends State<AppTextFormField> {
  bool obscureText = false;
  @override
  void initState() {
    obscureText = widget.obscureText ?? false;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: widget.margin,
      child: TextFormField(
        controller: widget.controller,
        onChanged: widget.onChanged,
        onSaved: widget.onSaved,
        onFieldSubmitted: widget.onFieldSubmitted,
        validator: widget.validator,
        keyboardType: widget.keyboardType,
        textInputAction: widget.textInputAction,
        obscureText: obscureText,
        maxLines: widget.obscureText ?? false ? 1 : widget.maxLines,
        initialValue: widget.initialValue,
        decoration: InputDecoration(
          filled: widget.filled,
          fillColor: widget.fillColor ?? Colors.transparent,
          labelText: widget.labelText,
          prefixIcon: widget.prefixIcon,
          suffixIcon: widget.revealablePassword
              ? IconButton(
                  onPressed: () {
                    setState(() {
                      obscureText = !obscureText;
                    });
                  },
                  icon: Icon(
                    !obscureText ? Icons.visibility_off : Icons.visibility,
                    color: Theme.of(context).primaryColor,
                  ),
                )
              : widget.suffixIcon,
          alignLabelWithHint: true,
          errorText: widget.errorText,
          hintText: widget.hintText,
          hintStyle: TextStyle(
            color: Theme.of(context).hintColor,
          ),
          // مستخدم عند حالة عادية
          border: OutlineInputBorder(
            borderRadius: widget.borderRadius ?? BorderRadius.circular(12),
            borderSide: BorderSide(
              color: Get.theme.primaryColor.withOpacity(0.3),
            ),
          ),
          // مستخدم عندما يكون الحقل مفعل ولكن بدون تركيز
          enabledBorder: OutlineInputBorder(
            borderRadius: widget.borderRadius ?? BorderRadius.circular(12),
            borderSide: BorderSide(
              color: Get.theme.primaryColor.withOpacity(0.3),
            ),
          ),
          // مستخدم عندما يكون الحقل مركز عليه (في وضع الكتابة)
          focusedBorder: OutlineInputBorder(
            borderRadius: widget.borderRadius ?? BorderRadius.circular(12),
            borderSide: BorderSide(
              color: Get.theme.primaryColor,
              width: 1.5,
            ),
          ),
          // منع تغير اللون عند التركيز (فوكس)
          focusColor: Colors.transparent,
          // منع تأثير التحويم
          hoverColor: Colors.transparent,
          contentPadding: const EdgeInsets.symmetric(
            vertical: 16,
            horizontal: 16,
          ),
        ),
      ),
    );
  }
}

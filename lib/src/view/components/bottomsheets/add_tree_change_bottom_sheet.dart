import 'package:almashal/src/controllers/interactive_family_tree_controller.dart';
import 'package:almashal/src/core/utils/common_functions.dart';
import 'package:almashal/src/data/models/family_tree_node.dart';
import 'package:almashal/src/view/components/bottomsheets/custom_bottom_sheet.dart';
import 'package:almashal/src/view/components/buttons/custom_filled_button.dart';
import 'package:almashal/src/view/components/form_fields/app_text_form_field.dart';
import 'package:flutter/material.dart';

class AddTreeChangeRequest extends StatelessWidget {
  const AddTreeChangeRequest({
    super.key,
    required this.actionType,
    required this.node,
  });
  final int actionType;
  final FamilyTreeNode node;
  @override
  Widget build(BuildContext context) {
    List<Map<String, dynamic>> actions = [
      {
        "title": "تقديم طلب حذف ",
        "field_label": "سبب الحذف",
      },
      {
        "title": "تقديم طلب اضافة ابن ل",
        "field_label": "تفاصيل بيانات الابن",
      },
      {
        "title": "تقديم طلب تعديل ",
        "field_label": "تفاصيل التعديلات",
      },
    ];
    var formKey = GlobalKey<FormState>();
    String note = "";
    return CustomBottomSheet(
      title: actions[actionType]['title'] + node.name,
      body: Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Form(
              key: formKey,
              child: AppTextFormField(
                labelText: actions[actionType]['field_label'],
                maxLines: 3,
                validator: (value) {
                  if (value?.isEmpty ?? true) {
                    return 'هذا الحقل مطلوب';
                  }
                  return null;
                },
                onSaved: (newValue) {
                  note = newValue ?? "";
                },
              ),
            ),
            SizedBox(
              width: double.maxFinite,
              child: CustomFilledButton(
                margin: const EdgeInsets.only(top: 32),
                label: 'ارسال الطلب',
                onTap: () async {
                  if (formKey.currentState?.validate() ?? false) {
                    formKey.currentState?.save();
                    CommonFunctions.unfocusNodes();
                    await InteractiveFamilyTreeController.instance
                        .addChangeRequest(
                      note: note,
                      requestType: actionType,
                      familyTreeNodeId: node.id,
                    );
                    print('after wait');
                  }
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

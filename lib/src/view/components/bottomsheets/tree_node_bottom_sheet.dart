import 'package:almashal/src/core/routes/app_pages.dart';
import 'package:almashal/src/core/utils/common_functions.dart';
import 'package:almashal/src/data/models/family_tree_node.dart';
import 'package:almashal/src/data/services/auth_service.dart';
import 'package:almashal/src/view/components/bottomsheets/add_tree_change_bottom_sheet.dart';
import 'package:almashal/src/view/components/image/custom_cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class TreeNodeBottomSheet extends StatelessWidget {
  const TreeNodeBottomSheet({
    super.key,
    required this.node,
  });
  final FamilyTreeNode node;
  @override
  Widget build(BuildContext context) {
    var name = node.name;
    if (node.parent != null && node.gender == 1) {
      name += " بن ${node.parent?.name ?? ""}";
    }

    if (node.parent != null && node.gender == 2) {
      name += " بنت ${node.parent?.name ?? ""}";
    }

    // if (node.nickName != null) {
    //   name += " (${node!.nickName})";
    // }
    if (node.alive != 1 && node.gender == 1) {
      name += " (رحمه الله)";
    }

    if (node.alive != 1 && node.gender == 2) {
      name += " (رحمها الله)";
    }

    return Container(
      width: double.maxFinite,
      constraints: BoxConstraints(
        maxHeight: Get.height * 0.9,
        minHeight: 100,
      ),
      decoration: const BoxDecoration(
        color: Colors.transparent,
      ),
      child: Stack(
        clipBehavior: Clip.none,
        fit: StackFit.loose,
        children: [
          Container(
            height: double.maxFinite,
            constraints: BoxConstraints(
              maxHeight: Get.height * 0.9,
              minHeight: 100,
            ),
            decoration: const BoxDecoration(
              color: Colors.white,
              borderRadius: BorderRadius.vertical(
                top: Radius.circular(16),
              ),
            ),
          ),
          Positioned(
            right: 0,
            left: 0,
            top: -30,
            bottom: 0,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.start,
              children: [
                Container(
                  padding: const EdgeInsets.all(1),
                  decoration: BoxDecoration(
                    border: Border.all(
                      width: 2,
                      color: node.gender == 1
                          ? Theme.of(context).primaryColor
                          : Colors.pink,
                      strokeAlign: BorderSide.strokeAlignOutside,
                    ),
                    borderRadius: BorderRadius.circular(50),
                  ),
                  child: CustomCachedNetworkImage(
                    imageUrl: node.image ?? '',
                    thumbImageUrl: node.thumbImageUrl ?? '',
                    height: 70,
                    width: 70,
                    fit: BoxFit.cover,
                    withPreview: true,
                    borderRadius: BorderRadius.circular(
                      100,
                    ),
                  ),
                ),
                const SizedBox(
                  height: 8,
                ),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(
                    name,
                    style: const TextStyle(
                      fontSize: 17,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
                Expanded(
                  child: SingleChildScrollView(
                    child: Padding(
                      padding: const EdgeInsets.all(16.0),
                      child: Column(
                        children: [
                          TreeTextView(
                            title: 'نبذه:',
                            subtitle: node.overview,
                          ),
                          TreeTextView(
                            title: 'الكنية:',
                            subtitle: node.nickName,
                          ),
                          // TreeTextView(
                          //   title: 'على قيد الحياة؟',
                          //   subtitle: node.alive == 1 ? "نعم" : "لا",
                          //   subtitleColor: node.alive == 1 ? null : Colors.red,
                          // ),
                          TreeTextView(
                            title: 'الوظيفة:',
                            subtitle: node.job,
                          ),
                          TreeTextView(
                            title: 'عنوان الاقامة:',
                            subtitle: node.address,
                          ),
                          TreeTextView(
                            title: 'تاريخ الميلاد:',
                            subtitle: node.birthDate,
                          ),
                          TreeTextView(
                            title: 'مكان الميلاد:',
                            subtitle: node.birthPlace,
                          ),
                          if (node.alive != 1)
                            TreeTextView(
                              title: 'تاريخ الوفاه:',
                              subtitle: node.deathDate,
                            ),
                          if (node.alive != 1)
                            TreeTextView(
                              title: 'مكان الوفاه:',
                              subtitle: node.deathPlace,
                            ),
                        ],
                      ),
                    ),
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Column(
                    children: [
                      // الصف الأول: زر الملف الشخصي وزر إضافة ابن
                      Row(
                        children: [
                          Expanded(
                            child: FilledButton(
                              style: ButtonStyle(
                                backgroundColor: WidgetStateProperty.all(
                                  Colors.purple,
                                ),
                              ),
                              onPressed: () {
                                Get.back();
                                if (AuthService.instance.isAuthenticated()) {
                                  if (node.familyMemberId != null) {
                                    Get.toNamed(
                                      '/profile',
                                      arguments: {
                                        'userId': node.familyMemberId.toString()
                                      },
                                    );
                                  }
                                } else {
                                  Get.snackbar(
                                    'تنبيه',
                                    'يرجى تسجيل الدخول للمتابعة',
                                    snackPosition: SnackPosition.TOP,
                                    backgroundColor:
                                        Colors.red.withOpacity(0.8),
                                    colorText: Colors.white,
                                    duration: const Duration(seconds: 3),
                                  );
                                }
                              },
                              child: const Text("عرض الملف الشخصي"),
                            ),
                          ),
                          const SizedBox(
                            width: 4,
                          ),
                          Expanded(
                            child: FilledButton(
                              style: ButtonStyle(
                                backgroundColor: WidgetStateProperty.all(
                                  Colors.blue,
                                ),
                              ),
                              onPressed: () {
                                int value = 1;
                                if (!AuthService.instance.isAuthenticated()) {
                                  CommonFunctions.showErrorMessage(
                                      'يرجى تسجيل الدخول للمتابعة');
                                  Get.toNamed(Routes.LOGIN_PAGE);
                                } else {
                                  Get.bottomSheet(
                                    AddTreeChangeRequest(
                                      actionType: value,
                                      node: node,
                                    ),
                                  );
                                }
                              },
                              child: const Text("اضافة ابن"),
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 8), // فاصل بين الصفين

                      // الصف الثاني: زر طلب تعديل وزر طلب حذف
                      Row(
                        children: [
                          Expanded(
                            child: FilledButton(
                              style: ButtonStyle(
                                backgroundColor: WidgetStateProperty.all(
                                  Colors.green,
                                ),
                              ),
                              onPressed: () {
                                int value = 2;
                                if (!AuthService.instance.isAuthenticated()) {
                                  CommonFunctions.showErrorMessage(
                                      'يرجى تسجيل الدخول للمتابعة');
                                  Get.toNamed(Routes.LOGIN_PAGE);
                                } else {
                                  Get.bottomSheet(
                                    AddTreeChangeRequest(
                                      actionType: value,
                                      node: node,
                                    ),
                                  );
                                }
                              },
                              child: const Text("طلب تعديل"),
                            ),
                          ),
                          const SizedBox(
                            width: 4,
                          ),
                          Expanded(
                            child: FilledButton(
                              style: ButtonStyle(
                                backgroundColor: WidgetStateProperty.all(
                                  Colors.red,
                                ),
                              ),
                              onPressed: () {
                                int value = 0;
                                if (!AuthService.instance.isAuthenticated()) {
                                  CommonFunctions.showErrorMessage(
                                      'يرجى تسجيل الدخول للمتابعة');
                                  Get.toNamed(Routes.LOGIN_PAGE);
                                } else {
                                  Get.bottomSheet(
                                    AddTreeChangeRequest(
                                      actionType: value,
                                      node: node,
                                    ),
                                  );
                                }
                              },
                              child: const Text("طلب حذف"),
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                )
              ],
            ),
          ),
          Positioned(
            top: 0,
            left: 8,
            child: IconButton(
              onPressed: () => Get.back(),
              icon: const Icon(Icons.close),
            ),
          ),
        ],
      ),
    );
  }
}

class TreeTextView extends StatelessWidget {
  const TreeTextView({
    super.key,
    required this.title,
    this.subtitle,
    this.subtitleColor,
  });

  final String title;
  final String? subtitle;
  final Color? subtitleColor;

  @override
  Widget build(BuildContext context) {
    if (subtitle == null) {
      return const SizedBox();
    }
    return SizedBox(
      width: double.maxFinite,
      child: Column(
        mainAxisSize: MainAxisSize.min,
        mainAxisAlignment: MainAxisAlignment.start,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            title,
            textAlign: TextAlign.start,
            style: TextStyle(
              fontWeight: FontWeight.w600,
              color: Theme.of(context).primaryColor,
              fontSize: 15,
            ),
          ),
          const SizedBox(
            height: 4,
          ),
          Text(
            subtitle ?? "",
            textAlign: TextAlign.start,
            style: TextStyle(
              fontSize: 14,
              color: subtitleColor ?? Colors.black54,
            ),
          ),
          Divider(
            height: 24,
            thickness: 0.4,
            color: Theme.of(context).primaryColor,
            endIndent: 50,
          ),
        ],
      ),
    );
  }
}

import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

import '../../../core/values/assets.dart';

class CustomBottomSheet extends StatelessWidget {
  final String? title;
  final Widget body;
  final bool withExpaned;
  const CustomBottomSheet({
    super.key,
    this.title,
    required this.body,
    this.withExpaned = false,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      constraints: BoxConstraints(
        maxHeight: Get.size.height * 0.9,
        minHeight: 50,
      ),
      decoration: const BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(20),
          topRight: Radius.circular(20),
        ),
      ),
      child: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // title
            Container(
              padding: const EdgeInsets.all(16),
              width: double.maxFinite,
              child: Row(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  SvgPicture.asset(
                    Assets.vectorLogo,
                    width: 40,
                  ),
                  if (title != null) ...[
                    const SizedBox(width: 8),
                    Text(
                      title!,
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.w600,
                        color: Get.theme.primaryColor,
                      ),
                    ),
                  ],
                ],
              ),
            ),
            const Divider(
              height: 1,
              indent: 16,
              endIndent: 16,
            ),
            if (withExpaned)
              Expanded(
                child: body,
              )
            else
              body
          ],
        ),
      ),
    );
  }
}

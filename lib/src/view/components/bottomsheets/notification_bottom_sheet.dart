import 'package:almashal/src/data/services/messaging_service.dart';
import 'package:almashal/src/view/components/bottomsheets/custom_bottom_sheet.dart';
import 'package:almashal/src/view/components/buttons/custom_filled_button.dart';
import 'package:flutter/material.dart';

class NotificationBottomSheet extends StatelessWidget {
  const NotificationBottomSheet({
    super.key,
    required this.title,
    required this.body,
    this.data,
  });

  final String? title;
  final String? body;
  final Map<String, dynamic>? data;

  @override
  Widget build(BuildContext context) {
    return CustomBottomSheet(
      title: title,
      body: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          children: [
            Text(
              body!,
            ),
            SizedBox(
              width: double.maxFinite,
              child: CustomFilledButton(
                margin: const EdgeInsets.only(top: 16),
                label: 'فتح',
                onTap: () async {
                  await MessagingService.instance.goToScreen(data);
                },
              ),
            ),
          ],
        ),
      ),
    );
  }
}

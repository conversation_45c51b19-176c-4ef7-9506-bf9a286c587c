import 'package:almashal/src/data/models/vms/message_vm.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../data/services/auth_service.dart';
import '../buttons/custom_filled_button.dart';
import '../form_fields/app_text_form_field.dart';
import 'custom_bottom_sheet.dart';

class ContactBottomSheet extends StatelessWidget {
  const ContactBottomSheet({super.key});

  @override
  Widget build(BuildContext context) {
    MessageVM messageVM = MessageVM();
    var formKey = GlobalKey<FormState>();
    return CustomBottomSheet(
      title: 'تواصل معنا',
      body: Form(
        key: formKey,
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            children: [
              AppTextFormField(
                labelText: 'اسمك الكريم',
                keyboardType: TextInputType.emailAddress,
                validator: (value) {
                  if (value != null) {
                    if (value.isEmpty) {
                      return 'هذا الحقل مطلوب';
                    }
                  }
                  return null;
                },
                onSaved: (value) {
                  if (value != null) messageVM.name = value;
                },
              ),
              AppTextFormField(
                margin: const EdgeInsets.only(
                  top: 16,
                ),
                labelText: 'رقم جوالك',
                keyboardType: TextInputType.emailAddress,
                validator: (value) {
                  if (value != null) {
                    if (value.isEmpty) {
                      return 'هذا الحقل مطلوب';
                    }
                  }
                  return null;
                },
                onSaved: (value) {
                  if (value != null) messageVM.mobile = value;
                },
              ),
              AppTextFormField(
                margin: const EdgeInsets.only(
                  top: 16,
                ),
                labelText: 'بريدك الإلكتروني',
                keyboardType: TextInputType.emailAddress,
                validator: (value) {
                  if (value != null) {
                    if (value.isEmpty) {
                      return 'هذا الحقل مطلوب';
                    } else if (!value.isEmail) {
                      return "البريد الالكتروني الذي ادخلته غير صحيح";
                    }
                  }
                  return null;
                },
                onSaved: (value) {
                  if (value != null) messageVM.email = value;
                },
              ),
              AppTextFormField(
                margin: const EdgeInsets.only(
                  top: 16,
                ),
                labelText: 'الموضوع',
                keyboardType: TextInputType.text,
                validator: (value) {
                  if (value != null) {
                    if (value.isEmpty) {
                      return 'هذا الحقل مطلوب';
                    }
                  }
                  return null;
                },
                onSaved: (value) {
                  if (value != null) messageVM.subject = value;
                },
              ),
              AppTextFormField(
                margin: const EdgeInsets.only(
                  top: 16,
                ),
                labelText: 'الرسالة',
                maxLines: 4,
                keyboardType: TextInputType.text,
                validator: (value) {
                  if (value != null) {
                    if (value.isEmpty) {
                      return 'هذا الحقل مطلوب';
                    }
                  }
                  return null;
                },
                onSaved: (value) {
                  if (value != null) messageVM.message = value;
                },
              ),
              SizedBox(
                width: double.maxFinite,
                child: CustomFilledButton(
                  margin: const EdgeInsets.only(top: 32),
                  label: 'ارسال',
                  onTap: () async {
                    if (formKey.currentState?.validate() ?? false) {
                      formKey.currentState?.save();
                      await AuthService.instance.contact(messageVM);
                      print('after wait');
                    }
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

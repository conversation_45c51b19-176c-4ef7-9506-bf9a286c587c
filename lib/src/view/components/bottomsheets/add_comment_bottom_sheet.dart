import 'package:almashal/src/controllers/comments_controller.dart';
import 'package:almashal/src/data/models/comment.dart';
import 'package:almashal/src/view/components/bottomsheets/custom_bottom_sheet.dart';
import 'package:almashal/src/view/components/buttons/custom_filled_button.dart';
import 'package:almashal/src/view/components/cards/comment_card.dart';
import 'package:almashal/src/view/components/form_fields/app_text_form_field.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class AddCommentBottomSheet extends StatelessWidget {
  const AddCommentBottomSheet({
    super.key,
    required this.type,
    required this.id,
    this.parent,
  });
  final String type;
  final int id;
  final Comment? parent;
  @override
  Widget build(BuildContext context) {
    final formKey = GlobalKey<FormState>();
    String body = "";
    return CustomBottomSheet(
      title: parent != null ? 'اضافة رد' : 'اضافة تعليق',
      body: Form(
        key: formK<PERSON>,
        child: Padding(
          padding: const EdgeInsets.only(right: 24, left: 24, bottom: 24),
          child: Column(
            children: [
              if (parent != null)
                Container(
                  width: double.maxFinite,
                  decoration: BoxDecoration(
                    color: Get.theme.primaryColor.withOpacity(0.2),
                    borderRadius:
                        BorderRadius.vertical(top: Radius.circular(12)),
                  ),
                  padding: const EdgeInsets.all(16.0),
                  child: Text(
                    parent!.body,
                    textAlign: TextAlign.start,
                    style: TextStyle(
                      color: Get.theme.primaryColor,
                      fontWeight: FontWeight.bold,
                      fontSize: 16,
                    ),
                  ),
                ),
              AppTextFormField(
                labelText: 'نص التعليق',
                maxLines: 3,
                keyboardType: TextInputType.text,
                borderRadius:
                    BorderRadius.vertical(bottom: Radius.circular(12)),
                validator: (value) {
                  if (value != null) {
                    if (value.isEmpty) {
                      return 'هذا الحقل مطلوب';
                    }
                  }
                  return null;
                },
                onSaved: (value) {
                  if (value != null) body = value;
                },
              ),
              SizedBox(
                width: double.maxFinite,
                child: CustomFilledButton(
                  margin: const EdgeInsets.only(top: 32),
                  label: 'اضافة ',
                  onTap: () async {
                    if (formKey.currentState?.validate() ?? false) {
                      formKey.currentState?.save();
                      var controller =
                          Get.find<CommentsController>(tag: "${type}_$id");
                      await controller.addComment({
                        'parent_id': parent?.id,
                        'body': body,
                        'commentable_type': type,
                        'commentable_id': id,
                      });
                    }
                  },
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

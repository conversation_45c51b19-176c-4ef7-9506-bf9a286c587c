import 'package:almashal/src/core/routes/app_pages.dart';
import 'package:almashal/src/core/utils/common_functions.dart';
import 'package:almashal/src/data/models/comment.dart';
import 'package:almashal/src/data/services/auth_service.dart';
import 'package:almashal/src/view/components/bottomsheets/add_comment_bottom_sheet.dart';
import 'package:almashal/src/view/components/image/custom_cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:almashal/src/view/components/list/replies_list.dart';
import 'package:almashal/src/core/theme/app_colors.dart';
import 'package:almashal/src/core/theme/app_dimensions.dart';
import 'package:flutter/services.dart';

class CommentCard extends StatefulWidget {
  const CommentCard({
    super.key,
    required this.comment,
    this.onLike,
    this.onReply,
    this.onDelete,
  });
  final Comment comment;
  final VoidCallback? onLike;
  final VoidCallback? onReply;
  final VoidCallback? onDelete;

  @override
  State<CommentCard> createState() => _CommentCardState();
}

class _CommentCardState extends State<CommentCard> {
  bool liked = false;
  int likesCount = 0;

  @override
  void initState() {
    liked = widget.comment.liked;
    likesCount = widget.comment.likesCount;
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        Row(
          crossAxisAlignment: CrossAxisAlignment.center,
          children: [
            // Enhanced profile image with border
            Container(
              decoration: BoxDecoration(
                shape: BoxShape.circle,
                border: Border.all(color: AppColors.primary, width: 2),
                boxShadow: [
                  BoxShadow(
                    color: AppColors.primary.withOpacity(0.2),
                    blurRadius: 6,
                    spreadRadius: 1,
                  ),
                ],
              ),
              child: ClipRRect(
                borderRadius:
                    BorderRadius.circular(AppDimensions.borderRadiusCircular),
                child: CustomCachedNetworkImage(
                  imageUrl: widget.comment.user?.image ?? "",
                  width: AppDimensions.avatarSizeMedium,
                  height: AppDimensions.avatarSizeMedium,
                  fit: BoxFit.cover,
                ),
              ),
            ),
            const SizedBox(width: AppDimensions.small),
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  widget.comment.user!.displayName,
                  style: const TextStyle(
                    fontSize: AppDimensions.fontSizeMedium,
                    fontWeight: FontWeight.bold,
                    color: AppColors.textPrimary,
                  ),
                ),
                SizedBox(
                  height: 4,
                ),
                Text(
                  widget.comment.createdAt,
                  style: const TextStyle(
                    fontSize: AppDimensions.fontSizeSmall,
                    color: AppColors.textSecondary,
                    fontStyle: FontStyle.italic,
                    letterSpacing: 0.3,
                    height: 1.4,
                  ),
                  overflow: TextOverflow.ellipsis,
                ),
              ],
            ),
          ],
        ),
        Container(
          margin: const EdgeInsets.only(top: AppDimensions.tiny, right: 48),
          width: double.maxFinite,
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(AppDimensions.borderRadiusLarge),
              bottomLeft: Radius.circular(AppDimensions.borderRadiusLarge),
              bottomRight: Radius.circular(AppDimensions.borderRadiusLarge),
            ),
            boxShadow: [
              BoxShadow(
                color: AppColors.primary.withOpacity(0.15),
                spreadRadius: 2,
                blurRadius: 8,
                offset: const Offset(0, 3),
              ),
            ],
            gradient: LinearGradient(
              begin: Alignment.topRight,
              end: Alignment.bottomLeft,
              colors: [
                Colors.white,
                AppColors.surfaceLight,
              ],
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.start,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(
                    child: Padding(
                      padding: const EdgeInsetsDirectional.only(
                        top: AppDimensions.medium,
                        start: AppDimensions.medium,
                      ),
                      child: Text(
                        widget.comment.body,
                        textAlign: TextAlign.right,
                        style: const TextStyle(
                          fontSize: AppDimensions.fontSizeMedium,
                          color: AppColors.textPrimary,
                          height: 1.4,
                        ),
                      ),
                    ),
                  ),
                  if (AuthService.instance.isAuthenticated() &&
                      widget.comment.user!.id ==
                          AuthService.instance.userData.value!.user.id)
                    PopupMenuButton(itemBuilder: (context) {
                      return [
                        PopupMenuItem(
                          child: Row(
                            children: [
                              Icon(
                                Icons.delete,
                                color: AppColors.error,
                                size: 18,
                              ),
                              SizedBox(
                                width: 4,
                              ),
                              Text("حذف"),
                            ],
                          ),
                          onTap: () {
                            if (widget.onDelete != null) {
                              HapticFeedback.lightImpact();
                              widget.onDelete!();
                            }
                          },
                        )
                      ];
                    }),
                ],
              ),
              const SizedBox(height: AppDimensions.small),
              Padding(
                padding: const EdgeInsets.only(
                  bottom: AppDimensions.small,
                  right: AppDimensions.medium,
                  left: AppDimensions.medium,
                ),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.start,
                  children: [
                    // زر الإعجاب
                    Material(
                      color: Colors.transparent,
                      child: InkWell(
                        onTap: !AuthService.instance.isAuthenticated()
                            ? null
                            : () {
                                setState(() {
                                  if (widget.onLike != null) {
                                    widget.onLike!();
                                  }
                                  if (liked) {
                                    likesCount--;
                                  } else {
                                    likesCount++;
                                  }
                                  liked = !liked;
                                });
                              },
                        borderRadius: BorderRadius.circular(
                            AppDimensions.borderRadiusMedium),
                        child: Padding(
                          padding: const EdgeInsets.all(AppDimensions.tiny),
                          child: Row(
                            mainAxisSize: MainAxisSize.min,
                            children: [
                              Icon(
                                liked ? Icons.favorite : Icons.favorite_border,
                                color: liked
                                    ? Colors.red
                                    : AppColors.textSecondary,
                                size: 18,
                              ),
                              const SizedBox(width: 3),
                              Text(
                                '${likesCount} من الاعجابات',
                                style: TextStyle(
                                  fontSize: AppDimensions.fontSizeSmall,
                                  color: AppColors.textSecondary,
                                  fontWeight: FontWeight.normal,
                                ),
                              ),
                            ],
                          ),
                        ),
                      ),
                    ),

                    // المسافة بين زر الإعجاب وزر الردود
                    const SizedBox(width: 10),

                    // زر الردود (للتعليقات الرئيسية فقط)
                    if (widget.comment.parentId == null)
                      Padding(
                        padding: const EdgeInsets.all(AppDimensions.tiny),
                        child: Row(
                          mainAxisSize: MainAxisSize.min,
                          children: [
                            Icon(
                              Icons.chat_bubble_outline,
                              color: AppColors.secondary,
                              size: 18,
                            ),
                            const SizedBox(width: AppDimensions.tiny),
                            Text(
                              '${widget.comment.repliesCount} من الردود ',
                              style: const TextStyle(
                                fontSize: AppDimensions.fontSizeSmall,
                                color: AppColors.secondary,
                              ),
                            ),
                          ],
                        ),
                      ),
                    if (widget.comment.parentId == null)
                      Material(
                        color: Colors.transparent,
                        child: InkWell(
                          onTap: () {
                            if (!AuthService.instance.isAuthenticated()) {
                              CommonFunctions.showErrorMessage(
                                  'يرجى تسجيل الدخول للمتابعة');
                              Get.toNamed(Routes.LOGIN_PAGE);
                            } else {
                              Get.bottomSheet(
                                AddCommentBottomSheet(
                                  type: widget.comment.commentableType,
                                  id: widget.comment.commentableId,
                                  parent: widget.comment,
                                ),
                              );
                            }
                          },
                          borderRadius: BorderRadius.circular(
                              AppDimensions.borderRadiusMedium),
                          child: Padding(
                            padding: const EdgeInsets.all(AppDimensions.tiny),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                Text(
                                  'إضافة رد',
                                  style: const TextStyle(
                                    fontSize: AppDimensions.fontSizeSmall,
                                    color: AppColors.secondary,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                      ),
                  ],
                ),
              )
            ],
          ),
        ),
        if (widget.comment.replies?.isNotEmpty ?? false)
          Padding(
            padding: const EdgeInsets.only(
              top: AppDimensions.medium,
              right: 48,
            ),
            child: RepliesList(
              replies: widget.comment.replies!,
              type: widget.comment.commentableType,
              id: widget.comment.commentableId,
            ),
          ),
      ],
    );
  }
}

import 'package:almashal/src/core/values/app_styles.dart';
import 'package:almashal/src/view/components/image/custom_cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';


class CustomFirstCard extends StatelessWidget {
  final String text;
  final String imageUrl;
  final VoidCallback? onTap;
  final EdgeInsetsGeometry? margin;
  const CustomFirstCard({
    super.key,
    required this.text,
    required this.imageUrl,
    this.onTap,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      color: Colors.white,
      child: Column(
        children: [
          Padding(
            padding: const EdgeInsets.only(top: 25.0),
            child: Stack(
              clipBehavior: Clip.none,
              children: [
                AspectRatio(
                  aspectRatio: 16 / 8.4,
                  child: Container(
                    decoration: AppStyles.cardBoxdecoration,
                    width: double.maxFinite,
                    child: Material(
                      borderRadius: AppStyles.cardBorderRadius,
                      color: Colors.transparent,
                      child: InkWell(
                        borderRadius: AppStyles.cardBorderRadius,
                        onTap: onTap,
                        child: const Align(
                          alignment: Alignment.bottomCenter,
                          child: Padding(
                            padding: EdgeInsets.all(8.0),
                            child: SizedBox(),
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                Positioned(
                  top: -25,
                  right: 0,
                  left: 0,
                  child: FractionallySizedBox(
                    widthFactor: 0.85,
                    child: Column(
                      children: [
                        AspectRatio(
                          aspectRatio: 16 / 9,
                          child: Material(
                            color: Colors.transparent,
                            child: InkWell(
                              onTap: onTap,
                              child: CustomCachedNetworkImage(
                                imageUrl: imageUrl,
                                fit: BoxFit.cover,
                                borderRadius: AppStyles.cardBorderRadius,
                              ),
                            ),
                          ),
                        ),
                        Padding(
                          padding: const EdgeInsets.only(top: 8.0),
                          child: Text(
                            text,
                            maxLines: 1,
                            overflow: TextOverflow.ellipsis,
                            style: TextStyle(
                              color: Get.theme.primaryColor,
                              fontSize: 16,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

import 'package:almashal/src/core/values/app_styles.dart';
import 'package:almashal/src/view/components/image/custom_cached_network_image.dart';
import 'package:flutter/material.dart';

class AlbumCard extends StatelessWidget {
  final String title;
  final String image;
  final VoidCallback onTap;
  final EdgeInsetsGeometry? margin;
  const AlbumCard({
    super.key,
    required this.title,
    required this.image,
    required this.onTap,
    this.margin,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      margin: margin,
      child: ClipRRect(
        borderRadius: AppStyles.cardBorderRadius,
        child: InkWell(
          onTap: onTap,
          child: Stack(
            children: [
              CustomCachedNetworkImage(
                imageUrl: image,
                width: double.maxFinite,
                height: double.maxFinite,
                fit: BoxFit.cover,
              ),
              Container(
                height: double.maxFinite,
                width: double.maxFinite,
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      Colors.black,
                      Colors.black.withOpacity(0.0),
                    ],
                    begin: Alignment.bottomCenter,
                    end: Alignment.topCenter,
                  ),
                ),
                child: Align(
                  alignment: Alignment.bottomCenter,
                  child: Padding(
                    padding: const EdgeInsets.all(8.0),
                    child: Text(
                      title,
                      style: const TextStyle(color: Colors.white),
                    ),
                  ),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}

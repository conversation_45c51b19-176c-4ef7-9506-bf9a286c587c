import 'package:almashal/src/core/values/assets.dart';
import 'package:almashal/src/core/values/colors.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/svg.dart';
import 'package:get/get.dart';

import '../../../core/values/app_styles.dart';

class HomeCard extends StatelessWidget {
  const HomeCard({
    super.key,
    required this.title,
    required this.svgIcon,
    required this.onTap,
  });
  final String title;
  final String svgIcon;
  final VoidCallback onTap;
  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 130,
      child: AspectRatio(
        aspectRatio: 118 / 155,
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: onTap,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                Expanded(
                  child: Material(
                    color: Colors.transparent,
                    child: InkWell(
                      onTap: onTap,
                      child: Container(
                        width: 150,
                        decoration: const BoxDecoration(
                            image: DecorationImage(
                                image: AssetImage(Assets.homeCardBg))),
                        child: FractionallySizedBox(
                          widthFactor: 0.4,
                          heightFactor: 0.4,
                          child: SvgPicture.asset(
                            svgIcon,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ),
                ),
                Container(
                  margin: const EdgeInsets.only(top: 4),
                  width: double.maxFinite,
                  padding: const EdgeInsets.all(3.0),
                  decoration: BoxDecoration(
                      color: Get.theme.primaryColor,
                      borderRadius: AppStyles.cardBorderRadius,
                      border:
                          Border.all(color: AppColors.borderColor, width: 1)),
                  child: Text(
                    title,
                    style: AppStyles.cardTitle.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

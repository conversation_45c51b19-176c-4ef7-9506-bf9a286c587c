import 'package:almashal/src/core/values/assets.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../core/values/app_styles.dart';

class CustomFilledButton extends StatefulWidget {
  final Future<void> Function() onTap;
  final String label;
  final EdgeInsetsGeometry? margin;
  final EdgeInsetsGeometry? padding;
  final Color? color;
  const CustomFilledButton({
    super.key,
    required this.onTap,
    required this.label,
    this.margin,
    this.color,
    this.padding,
  });

  @override
  State<CustomFilledButton> createState() => _CustomFilledButtonState();
}

class _CustomFilledButtonState extends State<CustomFilledButton> {
  bool isLoading = false;
  @override
  Widget build(BuildContext context) {
    return Container(
      margin: widget.margin,
      decoration: BoxDecoration(
        borderRadius: AppStyles.buttonBorderRadius,
        color: widget.color ?? Get.theme.primaryColor,
        image: const DecorationImage(
          image: AssetImage(
            Assets.appbarBg,
          ),
          fit: BoxFit.cover,
        ),
      ),
      child: Material(
        borderRadius: AppStyles.buttonBorderRadius,
        color: Colors.transparent,
        child: InkWell(
          borderRadius: AppStyles.buttonBorderRadius,
          onTap: isLoading
              ? null
              : () async {
                  setState(() {
                    isLoading = true;
                  });
                  await widget.onTap();
                  setState(() {
                    isLoading = false;
                  });
                },
          child: Padding(
            padding: widget.padding ??
                const EdgeInsets.symmetric(horizontal: 32, vertical: 12),
            child: Text(
              isLoading ? "يرجى الانتظار ،،،" : widget.label,
              style: AppStyles.buttonText,
              textAlign: TextAlign.center,
            ),
          ),
        ),
      ),
    );
  }
}

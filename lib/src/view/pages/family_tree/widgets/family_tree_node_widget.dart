import 'package:almashal/src/controllers/interactive_family_tree_controller.dart';
import 'package:almashal/src/data/models/family_tree_node.dart';
import 'package:almashal/src/view/components/bottomsheets/tree_node_bottom_sheet.dart';
import 'package:almashal/src/view/components/image/custom_cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'dart:math' as math;

class FamilyTreeNodeWidget extends StatefulWidget {
  const FamilyTreeNodeWidget({
    super.key,
    required this.node,
    this.onNodeTap,
  });
  final FamilyTreeNode node;
  final Function(String? userId)? onNodeTap;

  @override
  State<FamilyTreeNodeWidget> createState() => _FamilyTreeNodeWidgetState();
}

class _FamilyTreeNodeWidgetState extends State<FamilyTreeNodeWidget>
    with SingleTickerProviderStateMixin {
  // متحكم الرسوم المتحركة
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _pulseAnimation;
  late Animation<double> _rotationAnimation;

  // مرجع للمتحكم
  final InteractiveFamilyTreeController controller =
      InteractiveFamilyTreeController.instance;

  // اللون الأساسي للتطبيق (البني الفاتح)
  static const Color appColor = Color(0xFFBE9E7F);

  @override
  void initState() {
    super.initState();

    // إنشاء متحكم للرسوم المتحركة
    _animationController = AnimationController(
      vsync: this,
      duration: const Duration(milliseconds: 600),
    );

    // تعريف رسوم متحركة للتكبير والتصغير
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.85).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.easeInOutBack,
      ),
    );

    // تعريف رسوم متحركة للنبض
    _pulseAnimation = Tween<double>(begin: 1.0, end: 1.12).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: Curves.elasticInOut,
      ),
    );

    // رسم متحرك للدوران
    _rotationAnimation = Tween<double>(begin: 0, end: 0.05).animate(
      CurvedAnimation(
        parent: _animationController,
        curve: const Interval(0.0, 0.5, curve: Curves.easeInOut),
      ),
    );

    // ضبط الحالة الأولية
    if (controller.collapsedNodes.contains(widget.node.id)) {
      _animationController.value = 1.0;
    } else {
      _animationController.value = 0.0;
    }
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    // تحقق ما إذا كان هذا هو العقدة الجذر
    bool isRoot = widget.node.parent == null;

    final bool hasImage = widget.node.thumbImageUrl != null &&
        widget.node.thumbImageUrl!.isNotEmpty;

    // التحقق مما إذا كان لديه أطفال
    final bool hasChildren =
        widget.node.children != null && widget.node.children!.isNotEmpty;

    // التحقق من حالة الطي
    final bool isCollapsed = controller.collapsedNodes.contains(widget.node.id);

    // تحديث حالة الرسوم المتحركة إذا تغيرت
    if (isCollapsed &&
        _animationController.status != AnimationStatus.completed) {
      _animationController.forward();
    } else if (!isCollapsed &&
        _animationController.status != AnimationStatus.dismissed) {
      _animationController.reverse();
    }

    // أبعاد النود
    final double nodeWidth = isRoot ? 120.0 : 110.0;
    final double nodeHeight = isRoot ? 165.0 : 150.0;
    final double nameHeight = isRoot ? 40.0 : 35.0;

    // عدد الأطفال المباشرين والفروع
    final int childrenCount = controller.countChildren(widget.node);

    return GetBuilder<InteractiveFamilyTreeController>(
      builder: (_) {
        return GestureDetector(
      onTap: () {
            // عرض النافذة المنبثقة دائمًا عند النقر على العقدة
        Get.bottomSheet(
          TreeNodeBottomSheet(
                node: widget.node,
          ),
          isDismissible: false,
          isScrollControlled: true,
        );
      },
          onLongPress: hasChildren
              ? () {
                  // تبديل حالة طي/عرض النود عند الضغط المطول
                  controller.toggleNodeCollapse(widget.node.id);

                  // تطبيق تأثير نبض عند الضغط المطول
                  _animationController.reset();
                  _animationController.forward().then((_) {
                    if (!controller.collapsedNodes.contains(widget.node.id)) {
                      _animationController.reverse();
                    }
                  });
                }
              : null,
          child: AnimatedBuilder(
            animation: _animationController,
            builder: (context, child) {
              return Transform.scale(
                scale: isCollapsed ? 0.95 : 1.0,
                child: SizedBox(
                  width: nodeWidth,
                  height: nodeHeight,
                  child: Stack(
                    children: [
                      // الخلفية الرئيسية المتكاملة مع الأقواس والمستطيل
                      CustomPaint(
                        size: Size(nodeWidth, nodeHeight),
                        painter: NodeShapePainter(
                          color: appColor,
                          nameHeight: nameHeight,
                          isRoot: isRoot,
                        ),
                      ),

                      // النص داخل المستطيل السفلي
                      Positioned(
                        bottom: 0,
                        left: 0,
                        right: 0,
                        child: SizedBox(
                          height: nameHeight,
                          child: Center(
                            child: Padding(
                              padding:
                                  const EdgeInsets.symmetric(horizontal: 6),
                              child: Text(
                                widget.node.name,
                                textAlign: TextAlign.center,
                                style: TextStyle(
                                  color: Colors.white,
                                  fontSize: isRoot ? 14 : 12,
                                  fontWeight: FontWeight.bold,
                                  shadows: [
                                    Shadow(
                                      color: Colors.black.withOpacity(0.3),
                                      blurRadius: 2,
                                      offset: const Offset(0, 1),
                                    ),
                                  ],
                                ),
                                maxLines: 2,
                                overflow: TextOverflow.ellipsis,
                              ),
                            ),
                          ),
                        ),
                      ),

                      // دائرة البروفايل
                      Positioned(
                        top: isRoot ? 20 : 17,
                        left: (nodeWidth - (isRoot ? 95 : 85)) / 2,
      child: Container(
                          width: isRoot ? 95 : 85,
                          height: isRoot ? 95 : 85,
          decoration: BoxDecoration(
            color: Colors.white,
                            shape: BoxShape.circle,
                            boxShadow: [
                              BoxShadow(
                                color: appColor.withOpacity(0.2),
                                blurRadius: 5,
                                spreadRadius: 1,
                              ),
                            ],
            border: Border.all(
                              color: appColor,
              width: 2,
                            ),
                          ),
                          child: Padding(
                            padding: const EdgeInsets.all(3),
                            child: Container(
                              decoration: BoxDecoration(
                                color: Colors.white,
                                shape: BoxShape.circle,
                                border: Border.all(
                                  color: appColor.withOpacity(0.3),
                                  width: 1.5,
                                ),
                              ),
                              child: Padding(
                                padding: const EdgeInsets.all(3),
                                child: Container(
                                  decoration: const BoxDecoration(
                                    color: Colors.white,
                                    shape: BoxShape.circle,
                                  ),
                                  child: ClipOval(
                                    child: hasImage
                                        ? CustomCachedNetworkImage(
                                            imageUrl:
                                                widget.node.thumbImageUrl!,
                                            fit: BoxFit.cover,
                                          )
                                        : Icon(
                                            widget.node.gender == 1
                                                ? Icons.person
                                                : Icons.person_outline,
                                            size: isRoot ? 42 : 37,
                                            color: appColor,
                                          ),
                                  ),
                                ),
                              ),
                            ),
                          ),
                        ),
                      ),

                      // مؤشر الطي/العرض للنودات التي لديها أطفال
                      if (hasChildren)
                        Positioned(
                          left: 3,
                          bottom: nameHeight + 4,
                          child: Container(
                            width: 22,
                            height: 22,
                            decoration: BoxDecoration(
                              gradient: LinearGradient(
                                begin: Alignment.topLeft,
                                end: Alignment.bottomRight,
                                colors: isCollapsed
                                    ? [
                                        appColor.withOpacity(0.9), // بني فاتح
                                        appColor.withOpacity(0.7), // بني غامق
                                      ]
                                    : [
                                        appColor.withOpacity(0.9), // بني فاتح
                                        appColor.withOpacity(0.7), // بني غامق
                                      ],
                              ),
                              shape: BoxShape.circle,
                              boxShadow: [
                                BoxShadow(
                                  color: appColor.withOpacity(0.3),
                                  blurRadius: 4,
                                  spreadRadius: 0,
                                ),
                              ],
                              border: Border.all(
                                color: Colors.white,
                                width: 1.5,
                              ),
          ),
          child: Center(
                              child: Icon(
                                isCollapsed ? Icons.add : Icons.remove,
                                color: Colors.white,
                                size: 14,
                              ),
                            ),
                          ),
                        ),

                      // عدد الأفراد في حالة كانت هناك فروع مخفية
                      if (hasChildren && isCollapsed)
                        Positioned(
                          right: 3,
                          bottom: nameHeight + 4,
                          child: Container(
                            padding: const EdgeInsets.symmetric(
                                horizontal: 6, vertical: 2),
                            decoration: BoxDecoration(
                              color: appColor,
                              borderRadius: BorderRadius.circular(10),
                              boxShadow: [
                                BoxShadow(
                                  color: appColor.withOpacity(0.3),
                                  blurRadius: 3,
                                  spreadRadius: 0,
                                  offset: const Offset(0, 1),
                                ),
                              ],
                              border: Border.all(
                                color: Colors.white,
                                width: 1.0,
                              ),
                            ),
                            child: Row(
                              mainAxisSize: MainAxisSize.min,
                              children: [
                                const Icon(
                                  Icons.people,
                                  color: Colors.white,
                                  size: 10,
                                ),
                                const SizedBox(width: 2),
                                Text(
                                  "${controller.countChildren(widget.node)}",
              style: const TextStyle(
                                    color: Colors.white,
                                    fontSize: 10,
                fontWeight: FontWeight.bold,
                                  ),
                                ),
                              ],
                            ),
                          ),
                        ),
                    ],
                  ),
                ),
              );
            },
          ),
        );
      },
    );
  }
}

// رسام النود الجديد بشكل متكامل بين الأقواس والمستطيل
class NodeShapePainter extends CustomPainter {
  final Color color;
  final double nameHeight;
  final bool isRoot;

  NodeShapePainter({
    required this.color,
    required this.nameHeight,
    required this.isRoot,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final double width = size.width;
    final double height = size.height;
    final double imageSize = isRoot ? 95.0 : 85.0;
    final double imageYPos = isRoot ? 20.0 : 17.0;
    final double imageRadius = imageSize / 2;
    final double nameTop = height - nameHeight;

    // الألوان والفرش
    final fillPaint = Paint()
      ..color = Colors.white
      ..style = PaintingStyle.fill;

    final strokePaint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 2.5
      ..strokeCap = StrokeCap.round;

    final innerStrokePaint = Paint()
      ..color = color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 1.5
      ..strokeCap = StrokeCap.round;

    final nameFillPaint = Paint()
      ..color = color
      ..style = PaintingStyle.fill;

    // رسم خلفية النود (بيضاء)
    final bgRect = RRect.fromRectAndCorners(
      Rect.fromLTWH(0, 0, width, height),
      topLeft: const Radius.circular(15),
      topRight: const Radius.circular(15),
      bottomLeft: const Radius.circular(12),
      bottomRight: const Radius.circular(12),
    );
    canvas.drawRRect(bgRect, fillPaint);

    // رسم منطقة الاسم (بنية)
    final nameRect = RRect.fromRectAndCorners(
      Rect.fromLTWH(0, nameTop, width, nameHeight),
      bottomLeft: const Radius.circular(12),
      bottomRight: const Radius.circular(12),
    );
    canvas.drawRRect(nameRect, nameFillPaint);

    // حساب نقاط الاتصال بين الأقواس والمستطيل
    final double centerX = width / 2;
    final double centerY = imageYPos + imageRadius;

    // رسم الأقواس متصلة بالمستطيل (السميك الخارجي)
    final path = Path();

    // نقطة البداية (الجانب الأيسر من المستطيل)
    path.moveTo(0, nameTop - 1);

    // نقاط التحكم للقوس الأيسر
    final leftControlPoint1 = Offset(width * 0.1, nameTop - 20);
    final leftControlPoint2 = Offset(width * 0.15, centerY - 5);

    // رسم القوس الأيسر باستخدام منحنى بيزير الثلاثي
    path.cubicTo(
      leftControlPoint1.dx,
      leftControlPoint1.dy,
      leftControlPoint2.dx,
      leftControlPoint2.dy,
      width * 0.2,
      centerY,
    );

    // قوس حول الدائرة
    path.arcToPoint(
      Offset(width * 0.8, centerY),
      radius: Radius.circular(imageRadius + 12),
      clockwise: false,
    );

    // نقاط التحكم للقوس الأيمن
    final rightControlPoint1 = Offset(width * 0.85, centerY - 5);
    final rightControlPoint2 = Offset(width * 0.9, nameTop - 20);

    // رسم القوس الأيمن باستخدام منحنى بيزير الثلاثي
    path.cubicTo(
      rightControlPoint1.dx,
      rightControlPoint1.dy,
      rightControlPoint2.dx,
      rightControlPoint2.dy,
      width,
      nameTop - 1,
    );

    canvas.drawPath(path, strokePaint);

    // رسم الأقواس الداخلية (الأنحف)
    final innerPath = Path();

    // نقطة البداية للقوس الداخلي
    innerPath.moveTo(width * 0.15, nameTop - 1);

    // نقاط التحكم للقوس الداخلي الأيسر
    final innerLeftControlPoint1 = Offset(width * 0.2, nameTop - 15);
    final innerLeftControlPoint2 = Offset(width * 0.25, centerY - 3);

    // رسم القوس الداخلي الأيسر
    innerPath.cubicTo(
      innerLeftControlPoint1.dx,
      innerLeftControlPoint1.dy,
      innerLeftControlPoint2.dx,
      innerLeftControlPoint2.dy,
      width * 0.3,
      centerY,
    );

    // قوس داخلي حول الدائرة
    innerPath.arcToPoint(
      Offset(width * 0.7, centerY),
      radius: Radius.circular(imageRadius + 8),
      clockwise: false,
    );

    // نقاط التحكم للقوس الداخلي الأيمن
    final innerRightControlPoint1 = Offset(width * 0.75, centerY - 3);
    final innerRightControlPoint2 = Offset(width * 0.8, nameTop - 15);

    // رسم القوس الداخلي الأيمن
    innerPath.cubicTo(
      innerRightControlPoint1.dx,
      innerRightControlPoint1.dy,
      innerRightControlPoint2.dx,
      innerRightControlPoint2.dy,
      width * 0.85,
      nameTop - 1,
    );

    canvas.drawPath(innerPath, innerStrokePaint);

    // رسم الطبقة الخارجية العلوية للدائرة
    final outerArcPath = Path();

    // نقطة البداية على الجانب الأيسر من الدائرة
    final startAngle = -math.pi;
    final endAngle = 0.0;
    final rect = Rect.fromCircle(
      center: Offset(width / 2, imageYPos + imageRadius),
      radius: imageRadius + 12,
    );

    outerArcPath.addArc(rect, startAngle, math.pi);

    // رسم الطبقة الخارجية بخط سميك
    final outerArcPaint = Paint()
      ..color = strokePaint.color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 6.0;

    canvas.drawPath(outerArcPath, outerArcPaint);

    // رسم الطبقة الداخلية
    final innerArcPath = Path();
    final innerRect = Rect.fromCircle(
      center: Offset(width / 2, imageYPos + imageRadius),
      radius: imageRadius + 6,
    );

    innerArcPath.addArc(innerRect, startAngle, math.pi);

    final innerArcPaint = Paint()
      ..color = strokePaint.color
      ..style = PaintingStyle.stroke
      ..strokeWidth = 3.0;

    canvas.drawPath(innerArcPath, innerArcPaint);
  }

  @override
  bool shouldRepaint(CustomPainter oldDelegate) => false;
}

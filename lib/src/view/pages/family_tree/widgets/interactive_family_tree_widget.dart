import 'dart:math';

import 'package:almashal/src/controllers/interactive_family_tree_controller.dart';
import 'package:almashal/src/data/enums/page_loading_status.dart';
import 'package:almashal/src/view/components/errors/loading.dart';
import 'package:almashal/src/view/components/errors/no_content.dart';
import 'package:almashal/src/view/components/errors/no_internet.dart';
import 'package:almashal/src/view/components/errors/server_error.dart';
import 'package:almashal/src/view/pages/family_tree/widgets/family_tree_node_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:graphview/GraphView.dart' hide Node;
import 'package:graphview/GraphView.dart' as graph show Node;

// لون موحد للتطبيق
const Color appColor = Color(0xFFBE9E7F);

// رسام مخصص للحواف بشكل منحني جذاب
class EnhancedFamilyTreeEdgeRenderer extends EdgeRenderer {
  // إعدادات الخط
  final double strokeWidth;
  final Color primaryColor;
  final Color accentColor;
  final bool useShadow;
  final bool useGradient;

  // إعدادات السهم
  final double arrowSize;
  final bool showArrow;

  // تأثيرات إضافية
  final bool useDottedLine;
  final bool useAnimatedDash;

  EnhancedFamilyTreeEdgeRenderer({
    this.strokeWidth = 2.0,
    this.primaryColor = appColor,
    this.accentColor = Colors.white,
    this.useShadow = true,
    this.useGradient = true,
    this.arrowSize = 0.0, // لا نستخدم السهم في شجرة العائلة
    this.showArrow = false,
    this.useDottedLine = false,
    this.useAnimatedDash = false,
  });

  @override
  void render(Canvas canvas, Graph graph, Paint paint) {
    // رسم جميع الخطوط أولاً (خلف)
    for (var edge in graph.edges) {
      final sourceNodeSize = _getNodeSize(edge.source);
      final destNodeSize = _getNodeSize(edge.destination);

      // حساب نقاط البداية والنهاية (أسفل/أعلى النود)
      final sourceCenter = Offset(
        edge.source.position.dx + sourceNodeSize.width / 2,
        edge.source.position.dy +
            (edge.source.position.dy > edge.destination.position.dy
                ? 0
                : sourceNodeSize.height), // الجهة المناسبة حسب الاتجاه
      );

      final destCenter = Offset(
        edge.destination.position.dx + destNodeSize.width / 2,
        edge.destination.position.dy +
            (edge.destination.position.dy > edge.source.position.dy
                ? 0
                : destNodeSize.height), // الجهة المناسبة حسب الاتجاه
      );

      // رسم الخط الأساسي بتأثير ظل
      _drawEnhancedConnection(canvas, sourceCenter, destCenter);
    }
  }

  // رسم خط العلاقة المحسّن
  void _drawEnhancedConnection(Canvas canvas, Offset source, Offset dest) {
    // المسافة بين النقطتين
    final distance = (dest - source).distance;

    // نقطة المنتصف للتحكم
    final midPoint = Offset(
      (source.dx + dest.dx) / 2,
      (source.dy + dest.dy) / 2,
    );

    // نقاط تحكم بيزير للمنحنى
    final controlPoint1 = Offset(
      source.dx + (dest.dx - source.dx) * 0.1,
      midPoint.dy + (source.dy < dest.dy ? -distance * 0.1 : distance * 0.1),
    );

    final controlPoint2 = Offset(
      dest.dx - (dest.dx - source.dx) * 0.1,
      midPoint.dy + (source.dy < dest.dy ? -distance * 0.1 : distance * 0.1),
    );

    // مسار المنحنى
    final path = Path();
    path.moveTo(source.dx, source.dy);

    // منحنى بيزير ثلاثي أكثر انسيابية
    path.cubicTo(
      controlPoint1.dx,
      controlPoint1.dy,
      controlPoint2.dx,
      controlPoint2.dy,
      dest.dx,
      dest.dy,
    );

    // رسم ظل للخط
    if (useShadow) {
      final shadowPaint = Paint()
        ..color = Colors.black.withOpacity(0.2)
        ..style = PaintingStyle.stroke
        ..strokeWidth = strokeWidth + 3
        ..strokeCap = StrokeCap.round
        ..maskFilter = const MaskFilter.blur(BlurStyle.normal, 3);

      canvas.drawPath(path, shadowPaint);
    }

    // تحديد خصائص الرسم
    final linePaint = Paint()
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth
      ..strokeCap = StrokeCap.round;

    // استخدام تدرج لوني
    if (useGradient) {
      // تدرج لوني للخط
      final gradient = LinearGradient(
        colors: [
          primaryColor,
          primaryColor.withOpacity(0.7),
          accentColor.withOpacity(0.3),
          primaryColor.withOpacity(0.7),
          primaryColor,
        ],
        stops: const [0.0, 0.3, 0.5, 0.7, 1.0],
      ).createShader(Rect.fromPoints(source, dest));

      linePaint.shader = gradient;
    } else {
      linePaint.color = primaryColor;
    }

    // رسم الخط بالتأثيرات
    if (useDottedLine) {
      _drawDashedPath(canvas, path, linePaint);
    } else {
      canvas.drawPath(path, linePaint);
    }

    // رسم خط رفيع مضيء في المنتصف
    final highlightPaint = Paint()
      ..color = accentColor.withOpacity(0.6)
      ..style = PaintingStyle.stroke
      ..strokeWidth = strokeWidth * 0.3
      ..strokeCap = StrokeCap.round;

    canvas.drawPath(path, highlightPaint);

    // رسم نقاط صغيرة على طول المسار
    if (!useDottedLine) {
      // نقطة ناعمة عند بداية الخط
      final startDotPaint = Paint()
        ..color = primaryColor
        ..style = PaintingStyle.fill;
      canvas.drawCircle(source, strokeWidth / 2, startDotPaint);

      // نقطة ناعمة عند نهاية الخط
      final endDotPaint = Paint()
        ..color = primaryColor
        ..style = PaintingStyle.fill;
      canvas.drawCircle(dest, strokeWidth / 2, endDotPaint);
    }
  }

  // رسم خط متقطع (نقطي)
  void _drawDashedPath(Canvas canvas, Path path, Paint paint) {
    final dashLength = strokeWidth * 2;
    final gapLength = strokeWidth;
    final distance = dashLength + gapLength;

    // رسم المسار بشكل متقطع
    final pathMetrics = path.computeMetrics();
    for (var metric in pathMetrics) {
      double startDistance = 0;
      while (startDistance < metric.length) {
        final dashEndDistance = startDistance + dashLength;
        if (dashEndDistance > metric.length) {
          // آخر جزء قد يكون أقصر
          canvas.drawPath(
            metric.extractPath(startDistance, metric.length),
            paint,
          );
          break;
        } else {
          // رسم نقطة (جزء من الخط)
          canvas.drawPath(
            metric.extractPath(startDistance, dashEndDistance),
            paint,
          );
          // تجاوز المسافة البيضاء (الفراغ)
          startDistance = dashEndDistance + gapLength;
        }
      }
    }
  }

  // تحديد حجم النود من خلال استقراء تصميم النود
  Size _getNodeSize(graph.Node node) {
    // حجم افتراضي للنود (يمكن تعديله)
    final bool isRoot = node.key?.value is int &&
        InteractiveFamilyTreeController.instance.treeNodes
            .any((n) => n.id == node.key?.value && n.parent == null);

    // تحديث الحجم ليتوافق مع التعديلات الجديدة في FamilyTreeNodeWidget
    return Size(isRoot ? 120.0 : 110.0, isRoot ? 165.0 : 150.0);
  }
}

class InteractiveFamilyTreeWidget extends StatefulWidget {
  const InteractiveFamilyTreeWidget({super.key});

  @override
  State<InteractiveFamilyTreeWidget> createState() =>
      _InteractiveFamilyTreeWidgetState();
}

class _InteractiveFamilyTreeWidgetState
    extends State<InteractiveFamilyTreeWidget> with TickerProviderStateMixin {
  late TransformationController transformationController;
  final Map<int, AnimationController> _foldAnimationControllers = {};
  final Map<int, Animation<double>> _foldAnimations = {};

  @override
  void initState() {
    super.initState();
    transformationController = TransformationController();

    // عرض الشجرة بعد أن تصبح البيانات جاهزة
    WidgetsBinding.instance.addPostFrameCallback((_) {
      // تأخير قصير لضمان اكتمال بناء العقد قبل التمركز
      Future.delayed(const Duration(milliseconds: 200), () {
        final controller = Get.find<InteractiveFamilyTreeController>();

        // تعديل إعدادات بناء الشجرة لضمان مسافات مناسبة
        controller.builder.siblingSeparation =
            130; // زيادة المسافة بين الأشقاء (أفقياً)
        controller.builder.levelSeparation =
            120; // زيادة المسافة بين المستويات (رأسياً)
        controller.builder.subtreeSeparation =
            160; // زيادة المسافة بين الأشجار الفرعية

        if (controller.treeNodes.isNotEmpty) {
          _centerOnRoot(transformationController, controller);
        }
      });
    });
  }

  @override
  void dispose() {
    transformationController.dispose();
    // تحرير جميع متحكمات الرسوم المتحركة
    _foldAnimationControllers.forEach((_, controller) {
      controller.dispose();
    });
    super.dispose();
  }

  // إنشاء أو الحصول على متحكم الرسوم المتحركة للنود
  AnimationController _getAnimationController(int nodeId) {
    if (!_foldAnimationControllers.containsKey(nodeId)) {
      _foldAnimationControllers[nodeId] = AnimationController(
        vsync: this,
        duration: const Duration(milliseconds: 400),
      );

      _foldAnimations[nodeId] = CurvedAnimation(
        parent: _foldAnimationControllers[nodeId]!,
        curve: Curves.easeInOutCubic,
      );
    }
    return _foldAnimationControllers[nodeId]!;
  }

  @override
  Widget build(BuildContext context) {
    return GetBuilder<InteractiveFamilyTreeController>(
      builder: (controller) {
        return Obx(() {
        if (controller.pageLoadingStatus.value == PageLoadingStatus.loading) {
          return const Loading();
        } else if (controller.pageLoadingStatus.value ==
            PageLoadingStatus.networkError) {
          return const NoInternet();
        } else if (controller.pageLoadingStatus.value ==
            PageLoadingStatus.failed) {
          return const ServerError();
          } else if (controller.pageLoadingStatus.value ==
                  PageLoadingStatus.done &&
              controller.treeNodes.isEmpty) {
            return NoContent(
              message: 'لا يوجد بيانات، حاول مرة أخرى لاحقاً',
            );
          } else if (controller.pageLoadingStatus.value ==
                  PageLoadingStatus.done &&
              controller.treeNodes.isNotEmpty) {
            return _buildTreeView(
                controller, context, transformationController);
          }
          return Container();
        });
      },
    );
  }

  Widget _buildTreeView(InteractiveFamilyTreeController controller,
      BuildContext context, TransformationController transformationController) {
    return Stack(
      children: [
        SizedBox(
          width: double.infinity,
          height: double.infinity,
          child: InteractiveViewer(
            constrained: false,
            transformationController: transformationController,
            boundaryMargin: const EdgeInsets.all(double.infinity),
            minScale: 0.1,
            maxScale: 2.5,
            child: AnimatedBuilder(
              animation:
                  Listenable.merge(_foldAnimationControllers.values.toList()),
              builder: (context, _) {
                return GraphView(
              graph: controller.graph,
              algorithm: BuchheimWalkerAlgorithm(
                    controller.builder,
                    EnhancedFamilyTreeEdgeRenderer(
                      primaryColor: appColor,
                      strokeWidth: 2.5,
                      useShadow: true,
                      useGradient: true,
                    ),
                  ),
              paint: Paint()
                    ..color = Colors.green
                ..strokeWidth = 1
                ..style = PaintingStyle.stroke,
                  builder: (graph.Node node) {
                    var id = node.key?.value as int;
                    var nodeData = controller.nodesAsMap[id.toString()];
                    if (nodeData == null) return Container();

                    // التحقق إذا كان هذا النود مطوي أو مخفي
                    bool isCollapsed = controller.collapsedNodes.contains(id);
                    bool isHidden = controller.isNodeHidden(id);

                    // تحقق إذا كان هذا هو النود الرئيسي (الجذر)
                    bool isRoot = nodeData.parent == null;

                    // تحقق مما إذا كان للنود أطفال
                    bool hasChildren = nodeData.children != null &&
                        nodeData.children!.isNotEmpty;

                    // إذا كان النود مخفياً بالكامل، استخدم انتقال للإخفاء التدريجي
                    if (isHidden) {
                      return FadeTransition(
                        opacity: Tween<double>(begin: 1.0, end: 0.0)
                            .animate(CurvedAnimation(
                          parent: _getAnimationController(id),
                          curve: Interval(0.0, 0.5, curve: Curves.easeOut),
                        )),
                        child: SizeTransition(
                          sizeFactor: Tween<double>(begin: 1.0, end: 0.0)
                              .animate(CurvedAnimation(
                            parent: _getAnimationController(id),
                            curve:
                                Interval(0.5, 1.0, curve: Curves.easeOutBack),
                          )),
                          child: Container(width: 10, height: 10),
                        ),
                      );
                    }

                    // الحصول على متحكم الرسوم المتحركة للنود
                    final animationController = _getAnimationController(id);

                    // ضبط حالة الرسوم المتحركة بناءً على حالة الطي
                    if (isCollapsed &&
                        animationController.status !=
                            AnimationStatus.completed) {
                      animationController.forward();
                    } else if (!isCollapsed &&
                        animationController.status !=
                            AnimationStatus.dismissed) {
                      animationController.reverse();
                    }

                    // حالة طي/توسيع النود
                    return AnimatedContainer(
                      duration: const Duration(milliseconds: 350),
                      curve: Curves.easeInOutCubic,
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(10),
                        boxShadow: isCollapsed && hasChildren
                            ? [
                                BoxShadow(
                                  color: appColor.withOpacity(0.3),
                                  blurRadius: 10,
                                  spreadRadius: 2,
                                )
                              ]
                            : [],
                      ),
                      child: GestureDetector(
                        onTap: hasChildren
                            ? () {
                                // التبديل بين حالات الطي والتوسيع
                                // تمكين الضغط على النود الرئيسي أيضاً إذا كان له أطفال
                                if (controller.canToggleNode(id)) {
                                  controller.toggleNodeCollapse(id);
                                }
                              }
                            : null,
                        child: Transform.scale(
                          scale: hasChildren
                              ? Tween<double>(begin: 1.0, end: 1.05).evaluate(
                                  _foldAnimations[id] ??
                                      const AlwaysStoppedAnimation(0.0))
                              : 1.0,
                          child: FamilyTreeNodeWidget(
                            node: nodeData,
                            onNodeTap: _navigateToProfile,
                          ),
                        ),
                      ),
                    );
                  },
                );
              },
            ),
          ),
        ),

        // أزرار التحكم في التكبير والتصغير
        Positioned(
          bottom: 20,
          right: 20,
          child: Column(
            children: [
              FloatingActionButton(
                heroTag: 'zoom_in',
                mini: true,
                backgroundColor: appColor,
                foregroundColor: Colors.white,
                child: const Icon(Icons.add),
                onPressed: () {
                  // زيادة التكبير بمقدار 0.1
                  final scale =
                      transformationController.value.getMaxScaleOnAxis();
                  transformationController.value = Matrix4.identity()
                    ..scale(scale + 0.1);
                },
              ),
              const SizedBox(height: 8),
              FloatingActionButton(
                heroTag: 'zoom_out',
                mini: true,
                backgroundColor: appColor,
                foregroundColor: Colors.white,
                child: const Icon(Icons.remove),
                onPressed: () {
                  // تقليل التكبير بمقدار 0.1
                  final scale =
                      transformationController.value.getMaxScaleOnAxis();
                  transformationController.value = Matrix4.identity()
                    ..scale(scale - 0.1 > 0.1 ? scale - 0.1 : 0.1);
                },
              ),
              const SizedBox(height: 8),
              FloatingActionButton(
                heroTag: 'reset_view',
                mini: true,
                backgroundColor: appColor,
                foregroundColor: Colors.white,
                child: const Icon(Icons.center_focus_weak),
                onPressed: () {
                  // إعادة تمركز الشجرة
                  _centerOnRoot(transformationController, controller);
                },
              ),
            ],
          ),
        ),

        // زر إرشادات استخدام الشجرة
        Positioned(
          top: 20,
          left: 20,
          child: FloatingActionButton(
            heroTag: 'show_instructions',
            mini: true,
            backgroundColor: appColor,
            foregroundColor: Colors.white,
            child: const Icon(Icons.help_outline),
            onPressed: () {
              // عرض رسالة إرشادية للمستخدم
              Get.snackbar(
                'إرشادات استخدام الشجرة التفاعلية',
                '',
                titleText: const Text(
                  'إرشادات استخدام الشجرة التفاعلية',
                  style: TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                    fontSize: 16,
                  ),
                ),
                messageText: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: const [
                    SizedBox(height: 8),
                    Text(
                      '• اضغط على أي نود لعرض معلوماته وزيارة ملفه الشخصي',
                      style: TextStyle(color: Colors.white),
                    ),
                    SizedBox(height: 4),
                    Text(
                      '• اضغط ضغطة طويلة على أي نود لإخفاء/إظهار عدد فروعه',
                      style: TextStyle(color: Colors.white),
                    ),
                  ],
                ),
                snackPosition: SnackPosition.BOTTOM,
                backgroundColor: appColor.withOpacity(0.9),
                colorText: Colors.white,
                duration: const Duration(seconds: 6),
                margin: const EdgeInsets.all(10),
                borderRadius: 10,
                icon: const Icon(Icons.info_outline, color: Colors.white),
              );
            },
          ),
        ),
      ],
    );
  }

  // تمركز العرض على العقدة الرئيسية (الجذر)
  void _centerOnRoot(TransformationController controller,
      InteractiveFamilyTreeController treeController) {
    try {
      // اختر مستوى تكبير مناسب حسب عمق الشجرة
      double scale = 0.7;
      if (treeController.treeNodes.length > 30) {
        scale = 0.5;
      } else if (treeController.treeNodes.length > 50) {
        scale = 0.3;
      }

      // تحديد القيم الافتراضية
      double dx = -Get.width * 0.5 / scale;
      double dy = -Get.height * 0.5 / scale;

      // إذا أمكن، ابحث عن العقدة الرئيسية
      for (var node in treeController.treeNodes) {
        if (node.parent == null) {
          // العقدة الجذر وجدت - حساب التحويل المطلوب
          dx = -node.id * 100.0; // قيمة تقريبية
          dy = -node.id * 50.0; // قيمة تقريبية
          break;
        }
      }

      // تطبيق التحويل المحسوب
      controller.value = Matrix4.identity()
        ..translate(dx, dy)
        ..scale(scale);
    } catch (e) {
      // في حالة حدوث استثناء، استخدم قيم افتراضية آمنة
      controller.value = Matrix4.identity()..scale(0.7);
    }
  }

  // دالة للانتقال إلى الملف الشخصي
  void _navigateToProfile(String? userId) {
    if (userId != null && userId.isNotEmpty) {
      try {
        Get.toNamed('/profile', arguments: {'userId': userId});
      } catch (e) {
        Get.snackbar(
          'خطأ',
          'لا يمكن عرض الملف الشخصي لهذا المستخدم',
          snackPosition: SnackPosition.BOTTOM,
        );
      }
    }
  }
}

import 'package:almashal/src/controllers/family_tree_controller.dart';
import 'package:almashal/src/core/values/app_config.dart';
import 'package:almashal/src/data/enums/page_loading_status.dart';
import 'package:almashal/src/view/components/errors/loading.dart';
import 'package:almashal/src/view/components/errors/no_content.dart';
import 'package:almashal/src/view/components/errors/no_internet.dart';
import 'package:almashal/src/view/components/errors/server_error.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';
import 'package:photo_view/photo_view.dart';

class PrintedFamilyTreeWidget extends GetView<FamilyTreeController> {
  const PrintedFamilyTreeWidget({
    super.key,
  });

  @override
  Widget build(BuildContext context) {
    return Obx(() {
      if (controller.pageLoadingStatus.value == PageLoadingStatus.loading) {
        return const Loading();
      } else if (controller.pageLoadingStatus.value ==
          PageLoadingStatus.networkError) {
        return const NoInternet();
      } else if (controller.pageLoadingStatus.value ==
          PageLoadingStatus.failed) {
        return const ServerError();
      } else {
        if (controller.image.value.isEmpty) {
          return const NoContent();
        }
        return Center(
          child: Stack(
            children: [
              PhotoView(
                backgroundDecoration: const BoxDecoration(color: Colors.white),
                loadingBuilder: (context, event) {
                  return Stack(
                    alignment: Alignment.center,
                    children: [
                      SizedBox(
                        width: double.maxFinite,
                        height: double.maxFinite,
                        child: Image.network(
                          AppConfig.thumbBaseUrl + controller.image.value,
                          width: double.maxFinite,
                          fit: BoxFit.contain,
                        ),
                      ),
                      SpinKitRipple(
                        color: Get.theme.primaryColor,
                        size: 50,
                      ),
                    ],
                  );
                },
                minScale: PhotoViewComputedScale.contained * 1,
                maxScale: PhotoViewComputedScale.covered * 10,
                imageProvider: CachedNetworkImageProvider(
                  AppConfig.imageBaseUrl + controller.image.value,
                ),
              ),
              Positioned(
                bottom: 42,
                left: 10,
                child: Obx(() => Row(
                      children: [
                        Container(
                          padding: EdgeInsets.all(4),
                          decoration: BoxDecoration(
                            color: Get.theme.primaryColor.withOpacity(0.9),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            controller.fileDownloading.value
                                ? 'جاري تحميل الملف ...'.tr
                                : 'تحميل نسخة الطباعة'.tr,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 10,
                            ),
                          ),
                        ),
                        const SizedBox(
                          width: 2,
                        ),
                        FloatingActionButton(
                          mini: true,
                          backgroundColor:
                              Get.theme.primaryColor.withOpacity(0.9),
                          onPressed: () {
                            controller.downloadFamilyTree();
                          },
                          child: controller.fileDownloading.value
                              ? Padding(
                                  padding: const EdgeInsets.all(10.0),
                                  child: const CircularProgressIndicator(
                                    color: Colors.white,
                                    strokeWidth: 2,
                                  ),
                                )
                              : const Icon(
                                  Icons.download_rounded,
                                  color: Colors.white,
                                ),
                        ),
                      ],
                    )),
              ),
            ],
          ),
        );
      }
    });
  }
}

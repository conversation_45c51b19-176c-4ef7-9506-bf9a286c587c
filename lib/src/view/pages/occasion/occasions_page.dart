import 'package:almashal/src/controllers/occasions_controller.dart';
import 'package:almashal/src/core/routes/app_pages.dart';
import 'package:almashal/src/core/values/app_config.dart';
import 'package:almashal/src/core/values/app_constants.dart';
import 'package:almashal/src/data/models/responses/occasion_count_response.dart';
import 'package:almashal/src/view/components/form_fields/search_form_field.dart';
import 'package:almashal/src/view/components/list_tiles/news_list_tile.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../data/enums/page_loading_status.dart';
import '../../components/cards/custom_first_card.dart';
import '../../components/errors/loading.dart';
import '../../components/errors/no_content.dart';
import '../../components/errors/no_internet.dart';
import '../../components/errors/server_error.dart';
import '../../components/layouts/master_page.dart';
import '../../components/loadings/loadmore.dart';

class OccasionsPage extends StatefulWidget {
  const OccasionsPage({super.key});

  @override
  State<OccasionsPage> createState() => _OccasionsPageState();
}

class _OccasionsPageState extends State<OccasionsPage> {
  int type = 0;

  @override
  Widget build(BuildContext context) {
    return MasterPage(
      title: 'المناسبات',
      isSearchable: true,
      onSearchSubmitted: (value) {},
      onSearchToggled: (value) {
        for (var element in AppConstants.occasionTypes) {
          var element2 = element;
          var controller =
              Get.find<OccasionsController>(tag: "type_${element2['key']}");
          controller.showSearchField.value = value;
          if (!value) {
            controller.clearSearch();
          }
        }
      },
      showSearchTextField: false,
      body: FutureBuilder<OccasionCountResponse>(
          future: AppConfig.apiProvider.getOccasionCount(),
          builder: (context, snapshot) {
            if (snapshot.connectionState == ConnectionState.waiting) {
              return const Loading();
            } else if (snapshot.hasError) {
              return const ServerError();
            } else if (snapshot.data == null) {
              return const NoContent();
            }

            var data = snapshot.data!;
            List<Map<String, dynamic>> occasionTypes =
                AppConstants.occasionTypes;
            if (data.futureCount == 0) {
              occasionTypes.sort((a, b) => a['key'].compareTo(b['key']));
            }
            return DefaultTabController(
              length: occasionTypes.length,
              child: Column(
                children: [
                  TabBar(
                    labelStyle: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      fontFamily:
                          GoogleFonts.tajawalTextTheme().titleSmall?.fontFamily,
                    ),
                    onTap: (index) {
                      type = occasionTypes[index]['key'];
                    },
                    unselectedLabelStyle: TextStyle(
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                      fontFamily:
                          GoogleFonts.tajawalTextTheme().titleSmall?.fontFamily,
                    ),
                    tabs: List.generate(occasionTypes.length, (index) {
                      return Tab(
                        text: occasionTypes[index]['title'],
                      );
                    }),
                  ),
                  Expanded(
                    child: TabBarView(
                      children: List.generate(
                        occasionTypes.length,
                        (index) {
                          Get.put<OccasionsController>(
                            OccasionsController(
                              type: Rx<int>(occasionTypes[index]['key']),
                            ),
                            tag: 'type_${occasionTypes[index]['key']}',
                          );
                          return OccasionListView(
                            type: occasionTypes[index]['key'],
                          );
                        },
                      ),
                    ),
                  ),
                ],
              ),
            );
          }),
    );
  }
}

class OccasionListView extends StatelessWidget {
  const OccasionListView({
    super.key,
    required this.type,
    this.withSearchField = false,
  });

  final int type;
  final bool withSearchField;

  @override
  Widget build(BuildContext context) {
    var controller = Get.put<OccasionsController>(
      OccasionsController(
        type: Rx<int>(type),
      ),
      tag: 'type_$type',
    );
    return Obx(() => Column(
          children: [
            if (controller.showSearchField.value)
              SearchFormField(
                controller: TextEditingController.fromValue(
                    TextEditingValue(text: controller.searchTerm.value ?? '')),
                onSearchSubmitted: (value) {
                  if (value != null && (value.isNotEmpty)) {
                    controller.search(value);
                  }
                },
              ),
            Expanded(
              child: Obx(
                () {
                  if (controller.pageLoadingStatus.value ==
                      PageLoadingStatus.loading) {
                    return const Loading();
                  } else if (controller.pageLoadingStatus.value ==
                      PageLoadingStatus.networkError) {
                    return const NoInternet();
                  } else if (controller.pageLoadingStatus.value ==
                      PageLoadingStatus.failed) {
                    return const ServerError();
                  } else {
                    var occasions = controller.occasions.value;
                    if (occasions.isEmpty) {
                      String message = "عذراً، لا يوجد أخبار حالياً.";
                      if (controller.searchTerm.value?.isNotEmpty ?? false) {
                        message =
                            "عذراً، لا يوجد نتائج مطابقة لكلمة البحث  '${controller.searchTerm.value}'.";
                      }
                      return NoContent(
                        message: message,
                      );
                    }
                    return ListView.builder(
                      itemCount: occasions.length,
                      padding: const EdgeInsets.all(16),
                      itemBuilder: (BuildContext context, int index) {
                        if (index == 0) {
                          return CustomFirstCard(
                            imageUrl:
                                AppConfig.thumbBaseUrl + occasions[index].image,
                            text: occasions[index].title,
                            onTap: () {
                              Get.toNamed(Routes.OCCASION_DETAIL_PAGE,
                                  arguments: occasions[index]);
                            },
                          );
                        }
                        return Column(
                          children: [
                            NewsListTile(
                              imageUrl: AppConfig.thumbBaseUrl +
                                  occasions[index].image,
                              text: occasions[index].title,
                              onTap: () {
                                Get.toNamed(Routes.OCCASION_DETAIL_PAGE,
                                    arguments: occasions[index]);
                              },
                              date: occasions[index].date,
                              margin: const EdgeInsets.only(top: 16),
                            ),
                            if (index == occasions.length - 1 &&
                                !controller.cantLoadMore())
                              LoadMore(onLoadMore: () {
                                controller.loadMore();
                              })
                          ],
                        );
                      },
                    );
                  }
                },
              ),
            ),
          ],
        ));
  }
}

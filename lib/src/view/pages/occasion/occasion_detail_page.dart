import 'package:almashal/src/core/utils/common_functions.dart';
import 'package:almashal/src/core/utils/record_visit_function.dart';
import 'package:almashal/src/core/values/app_config.dart';
import 'package:almashal/src/data/models/occasion.dart';
import 'package:almashal/src/view/components/fabs/add_comment_floating_action_button.dart';
import 'package:almashal/src/view/components/html/html_view_component.dart';
import 'package:almashal/src/view/components/image/custom_cached_network_image.dart';
import 'package:almashal/src/view/components/list/comment_list.dart';
import 'package:almashal/src/view/components/widgets/visit_count_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../components/image/attached_images_bar.dart';
import '../../components/image/attached_youtube_videos_bar.dart';
import '../../components/layouts/master_page.dart';

class OccasionDetailPage extends StatelessWidget {
  const OccasionDetailPage({super.key});

  @override
  Widget build(BuildContext context) {
    Occasion occasion = Get.arguments;

    // Record the visit
    WidgetsBinding.instance.addPostFrameCallback((_) {
      RecordVisitFunction.occasion(occasion.id);
    });

    return MasterPage(
      title: occasion.title,
      shareText:
          '${occasion.title}\n${occasion.date}\n\n${CommonFunctions.htmlToShareableText(occasion.content)}',
      shareLink: occasion.shareLink,
      floatingActionButton: AddCommentFloatingActionButton(
        type: 'occasion',
        id: occasion.id,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.only(bottom: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(
                top: 12.0,
                right: 16,
                left: 16,
                bottom: 12.0,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    occasion.title,
                    style: TextStyle(
                      fontSize: 20,
                      fontWeight: FontWeight.w500,
                      color: Get.theme.primaryColor,
                    ),
                  ),
                  Padding(
                    padding: const EdgeInsets.only(
                      top: 8.0,
                      bottom: 8,
                    ),
                    child: Row(
                      children: [
                        Text(
                          occasion.date,
                          style: TextStyle(
                            color: Get.theme.colorScheme.secondary,
                          ),
                        ),
                        const Spacer(),
                        VisitCountWidget(
                          visitsCount: occasion.visitsCount,
                        ),
                      ],
                    ),
                  ),
                  CustomCachedNetworkImage(
                    imageUrl: AppConfig.imageBaseUrl + occasion.image,
                    thumbImageUrl: AppConfig.thumbBaseUrl + occasion.image,
                    width: double.maxFinite,
                    fit: BoxFit.fitWidth,
                    withPreview: true,
                  ),
                  HtmlViewComponent(html: occasion.content),
                  if (occasion.images.isNotEmpty)
                    AttachedImagesBar(images: occasion.images),
                  if (occasion.youtubeVideos.isNotEmpty)
                    AttachedYoutubeVideosBar(
                        youtubeVideos: occasion.youtubeVideos),
                  CommentList(
                    key: key,
                    type: 'occasion',
                    id: occasion.id,
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}

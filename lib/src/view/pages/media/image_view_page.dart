import 'package:almashal/src/core/values/colors.dart';
import 'package:almashal/src/view/components/html/html_view_component.dart';
import 'package:almashal/src/view/components/image/attached_images_bar.dart';
import 'package:almashal/src/view/components/image/custom_cached_network_image.dart';
import 'package:almashal/src/view/components/layouts/master_page.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ImageViewPage extends StatelessWidget {
  const ImageViewPage({super.key});

  @override
  Widget build(BuildContext context) {
    Map<String, dynamic> map = Get.arguments as Map<String, dynamic>;
    String imageUrl = map['media'];
    String? thumbImageUrl = map['thumb'];
    String? title = map['title'];
    String? detail = map['detail'];
    List<String>? images = map['images'];
    return MasterPage(
      title: title ?? 'عرض الصورة',
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisSize: MainAxisSize.min,
            children: [
              if (title != null && title.isNotEmpty && detail != null)
                Container(
                  padding: const EdgeInsets.only(
                    bottom: 8,
                  ),
                  width: double.maxFinite,
                  // decoration: BoxDecoration(
                  //   color: Get.theme.primaryColor.withOpacity(0.5),
                  //   borderRadius: BorderRadius.circular(8),
                  // ),
                  child: Text(
                    title,
                    style: TextStyle(
                      color: AppColors.primaryColorDark,
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                    ),
                    textAlign: TextAlign.start,
                  ),
                ),
              CustomCachedNetworkImage(
                imageUrl: imageUrl,
                thumbImageUrl: thumbImageUrl,
                width: double.maxFinite,
                withPreview: true,
              ),
              if (detail != null)
                HtmlViewComponent(
                  html: detail,
                ),
              if (images?.isNotEmpty ?? false)
                AttachedImagesBar(images: images ?? []),
            ],
          ),
        ),
      ),
    );
  }
}

import 'package:almashal/src/core/values/colors.dart';
import 'package:almashal/src/view/components/html/html_view_component.dart';
import 'package:almashal/src/view/components/layouts/master_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';
import 'package:youtube_player_flutter/youtube_player_flutter.dart';

class VideoViewPage extends StatelessWidget {
  const VideoViewPage({super.key});

  @override
  Widget build(BuildContext context) {
    Map<String, dynamic> map = Get.arguments as Map<String, dynamic>;
    String videoId = map['media'];
    String? title = map['title'];
    String? detail = map['detail'];
    return YoutubePlayerBuilder(
      onExitFullScreen: () {
        SystemChrome.setEnabledSystemUIMode(SystemUiMode.edgeToEdge);
      },
      builder: (context, player) {
        return MasterPage(
          title: title ?? 'عرض الفيديو',
          body: Column(
            crossAxisAlignment: CrossAxisAlignment.center,
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              if (title != null && title.isNotEmpty && detail != null)
                Container(
                  // margin: EdgeInsets.all(8),
                  padding: EdgeInsets.all(16),
                  width: double.maxFinite,
                  // decoration: BoxDecoration(
                  //   color: Get.theme.primaryColor.withOpacity(0.5),
                  //   borderRadius: BorderRadius.circular(8),
                  // ),
                  child: Text(
                    title,
                    style: TextStyle(
                      color: AppColors.primaryColorDark,
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                    ),
                    textAlign: TextAlign.start,
                  ),
                ),
              Container(
                color: Colors.white,
                child: Center(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 8.0),
                    child: ClipRRect(
                      borderRadius: BorderRadius.circular(8),
                      child: player,
                    ),
                  ),
                ),
              ),
              if (detail != null)
                Expanded(
                  child: SingleChildScrollView(
                    padding: const EdgeInsets.all(16),
                    child: HtmlViewComponent(
                      html: detail,
                    ),
                  ),
                ),
            ],
          ),
        );
      },
      player: YoutubePlayer(
        controller: YoutubePlayerController(
          initialVideoId: videoId,
          flags: const YoutubePlayerFlags(
            autoPlay: true,
            mute: false,
          ),
        ),
      ),
    );
  }
}

import 'package:almashal/src/controllers/committees_controller.dart';
import 'package:almashal/src/data/enums/page_loading_status.dart';
import 'package:almashal/src/view/components/errors/loading.dart';
import 'package:almashal/src/view/components/errors/no_content.dart';
import 'package:almashal/src/view/components/errors/no_internet.dart';
import 'package:almashal/src/view/components/errors/server_error.dart';
import 'package:almashal/src/view/components/html/html_view_component.dart';
import 'package:almashal/src/view/components/layouts/master_page.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class CommitteesPage extends GetView<CommitteesController> {
  const CommitteesPage({super.key});

  @override
  Widget build(BuildContext context) {
    return MasterPage(
      title: 'اللجان',
      body: Obx(
        () {
          if (controller.pageLoadingStatus.value == PageLoadingStatus.loading) {
            return const Loading();
          } else if (controller.pageLoadingStatus.value ==
              PageLoadingStatus.networkError) {
            return const NoInternet();
          } else if (controller.pageLoadingStatus.value ==
              PageLoadingStatus.failed) {
            return const ServerError();
          } else {
            var committees = controller.committees.value;
            if (committees.isEmpty) {
              return const NoContent();
            }
            return ListView.builder(
              itemCount: committees.length,
              padding: const EdgeInsets.only(top: 8),
              itemBuilder: (BuildContext context, int index) {
                return Column(
                  children: [
                    Theme(
                      data: Theme.of(context)
                          .copyWith(dividerColor: Colors.transparent),
                      child: ExpansionTile(
                        childrenPadding:
                            const EdgeInsets.symmetric(horizontal: 16.0),
                        tilePadding: const EdgeInsets.all(8),
                        title: Text(
                          committees[index].name,
                          style: const TextStyle(
                            fontSize: 20,
                            fontWeight: FontWeight.w600,
                          ),
                        ),
                        children: [
                          HtmlViewComponent(
                            html: committees[index].content,
                          )
                        ],
                      ),
                    ),
                    if (index != committees.length - 1)
                      Divider(
                        thickness: 0.2,
                        color: Get.theme.primaryColor,
                      )
                  ],
                );
              },
            );
          }
        },
      ),
    );
  }
}

import 'package:almashal/src/core/routes/app_pages.dart';
import 'package:almashal/src/core/values/assets.dart';
import 'package:almashal/src/data/services/auth_service.dart';
import 'package:almashal/src/data/services/storage_service.dart' as storage;
import 'package:almashal/src/view/components/buttons/custom_filled_button.dart';
import 'package:almashal/src/view/components/form_fields/app_text_form_field.dart';
import 'package:almashal/src/view/components/form_fields/custom_phone_form_field.dart';
import 'package:almashal/src/view/components/layouts/master_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:phone_form_field/phone_form_field.dart';

class LoginPage extends StatefulWidget {
  const LoginPage({super.key});

  @override
  State<LoginPage> createState() => _LoginPageState();
}

class _LoginPageState extends State<LoginPage> {
  final formKey = GlobalKey<FormState>();
  final PhoneController phoneController =
      PhoneController(initialValue: PhoneNumber(isoCode: IsoCode.SA, nsn: ""));
  final RxBool isPasswordVisible = false.obs;
  String password = '';
  final storage.StorageService _storageService = storage.StorageService();

  @override
  void initState() {
    super.initState();
    // التحقق من وجود رقم جوال مُعتمد حديثاً
    _checkForApprovedMobile();
  }

  // التحقق من وجود رقم جوال معتمد من طلب تسجيل جديد
  Future<void> _checkForApprovedMobile() async {
    String? approvedMobile =
        await _storageService.getData('temp_approved_mobile');
    if (approvedMobile != null && approvedMobile.isNotEmpty) {
      try {
        // محاولة تحليل رقم الهاتف بالصيغة الدولية
        PhoneNumber phoneNumber = await PhoneNumber.parse(approvedMobile);
        setState(() {
          phoneController.value = phoneNumber;
        });

        // عرض رسالة للمستخدم
        Future.delayed(const Duration(milliseconds: 500), () {
          if (mounted) {
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text(
                    'تم الموافقة على طلب التسجيل الخاص بك. يرجى إدخال كلمة المرور للدخول.'),
                backgroundColor: Colors.green,
                duration: Duration(seconds: 5),
              ),
            );
          }
        });

        // حذف المعلومات المؤقتة بعد استخدامها
        _storageService.deleteData('temp_approved_mobile');
      } catch (e) {
        // في حالة عدم القدرة على تحليل الرقم
        print('Error parsing phone number: $e');
      }
    }
  }

  @override
  void dispose() {
    phoneController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    String mobile = '';
    String countryCode = '';

    return MasterPage(
      title: 'تسجيل الدخول',
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.transparent,
              Theme.of(context).primaryColor.withOpacity(0.08),
            ],
          ),
        ),
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 32),
            child: Form(
              key: formKey,
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                crossAxisAlignment: CrossAxisAlignment.center,
                children: [
                  // Logo with animation
                  SvgPicture.asset(
                    Assets.vectorLogo,
                    width: 180,
                  ).animate().fadeIn(duration: 600.ms).scale(
                      begin: const Offset(0.8, 0.8), end: const Offset(1, 1)),
                  const SizedBox(height: 40),

                  // Welcome text with animation
                  Text(
                    'مرحباً بك مجدداً',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                          fontWeight: FontWeight.bold,
                          color: Theme.of(context).primaryColor,
                        ),
                    textAlign: TextAlign.center,
                  ).animate().fadeIn(delay: 300.ms, duration: 500.ms),
                  const SizedBox(height: 16),

                  // Description with animation
                  Text(
                    'يرجى تسجيل الدخول للوصول إلى حسابك',
                    style: Theme.of(context).textTheme.bodyMedium,
                    textAlign: TextAlign.center,
                  ).animate().fadeIn(delay: 400.ms, duration: 500.ms),
                  const SizedBox(height: 40),

                  // Login form container with animation
                  Container(
                    decoration: BoxDecoration(
                      color: Colors.transparent,
                      borderRadius: BorderRadius.circular(24),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.03),
                          blurRadius: 15,
                          offset: const Offset(0, 5),
                          spreadRadius: 0,
                        ),
                      ],
                      border: Border.all(
                        color: Theme.of(context).primaryColor.withOpacity(0.1),
                        width: 1.5,
                      ),
                    ),
                    padding: const EdgeInsets.all(24),
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // Mobile field - استخدام PhoneFormField
                        CustomPhoneFormField(
                          labelText: 'رقم الجوال',
                          hintText: 'أدخل رقم الجوال',
                          controller: phoneController,
                          validator: (PhoneNumber? phone) {
                            if (phone == null || !phone.isValid()) {
                              return 'يرجى إدخال رقم جوال صحيح';
                            }
                            return null;
                          },
                          prefixIcon: Icon(
                            Icons.phone_android_rounded,
                            color: Get.theme.primaryColor,
                          ),
                          onSaved: (value) {
                            if (value != null) {
                              // حفظ الرقم ومفتاح الدولة بشكل منفصل
                              countryCode = '+${value.countryCode}';
                              mobile = value
                                  .nsn; // رقم الهاتف الوطني بدون مفتاح الدولة
                            }
                            return null;
                          },
                          filled: true,
                        ),

                        const SizedBox(height: 20),

                        // Password field with icon button
                        Obx(() {
                          return AppTextFormField(
                            labelText: 'كلمة المرور',
                            hintText: 'أدخل كلمة المرور',
                            obscureText: !isPasswordVisible.value,
                            fillColor: Colors.transparent,
                            prefixIcon: Icon(
                              Icons.lock_rounded,
                              color: Theme.of(context).primaryColor,
                            ),
                            maxLines: 1,
                            revealablePassword: true,
                            validator: (value) {
                              if (value == null || value.isEmpty) {
                                return 'يرجى إدخال كلمة المرور';
                              }
                              return null;
                            },
                            onSaved: (value) {
                              if (value != null) {
                                password = value;
                              }
                            },
                          );
                        }),

                        // Forgot password
                        Align(
                          alignment: Alignment.centerLeft,
                          child: TextButton(
                            onPressed: () {
                              Get.toNamed(Routes.FORGET_PASSWORD_PAGE);
                            },
                            style: TextButton.styleFrom(
                              padding: const EdgeInsets.symmetric(vertical: 12),
                            ),
                            child: Text(
                              'نسيت كلمة المرور؟',
                              style: TextStyle(
                                color: Theme.of(context).primaryColor,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ),
                      ],
                    ),
                  ).animate().fadeIn(delay: 500.ms, duration: 500.ms).slideY(
                      begin: 0.2,
                      end: 0,
                      delay: 500.ms,
                      duration: 500.ms,
                      curve: Curves.easeOutQuad),

                  const SizedBox(height: 40),

                  // Login button with animation
                  SizedBox(
                    width: double.infinity,
                    child: CustomFilledButton(
                      label: 'تسجيل الدخول',
                      onTap: () async {
                        if (formKey.currentState?.validate() ?? false) {
                          formKey.currentState?.save();
                          // إرسال مفتاح الدولة ورقم الهاتف بشكل منفصل للخدمة
                          var phone = mobile;
                          await AuthService.instance.login(phone, password);
                        }
                      },
                      margin: EdgeInsets.zero,
                    ),
                  ).animate().fadeIn(delay: 700.ms, duration: 500.ms),

                  const SizedBox(height: 24),

                  // Check registration status option
                  Container(
                    padding: const EdgeInsets.symmetric(
                        vertical: 16, horizontal: 20),
                    decoration: BoxDecoration(
                      color: Colors.transparent,
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                          color:
                              Theme.of(context).primaryColor.withOpacity(0.1),
                          width: 1.5),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.03),
                          blurRadius: 10,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: Column(
                      children: [
                        Text(
                          'هل قمت بالتسجيل وتنتظر الموافقة؟',
                          style:
                              Theme.of(context).textTheme.bodyMedium?.copyWith(
                                    fontWeight: FontWeight.bold,
                                  ),
                          textAlign: TextAlign.center,
                        ),
                        const SizedBox(height: 8),
                        InkWell(
                          onTap: () {
                            Get.toNamed(Routes.REGISTRATION_PENDING_PAGE);
                          },
                          child: Text(
                            'التحقق من حالة الطلب',
                            style: TextStyle(
                              color: Theme.of(context).primaryColor,
                              fontWeight: FontWeight.bold,
                              decoration: TextDecoration.underline,
                            ),
                          ),
                        ),
                      ],
                    ),
                  ).animate().fadeIn(delay: 800.ms, duration: 500.ms),

                  const SizedBox(height: 24),

                  // Register option
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        'ليس لديك حساب؟',
                        style: Theme.of(context).textTheme.bodyMedium,
                      ),
                      TextButton(
                        onPressed: () {
                          Get.toNamed(Routes.REGISTER_PAGE);
                        },
                        child: Text(
                          'تسجيل جديد',
                          style: TextStyle(
                            color: Theme.of(context).primaryColor,
                            fontWeight: FontWeight.bold,
                          ),
                        ),
                      ),
                    ],
                  ).animate().fadeIn(delay: 900.ms, duration: 500.ms),
                ],
              ),
            ),
          ),
        ),
      ),
    );
  }
}

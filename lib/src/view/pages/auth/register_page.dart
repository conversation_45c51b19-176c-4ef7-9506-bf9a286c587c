import 'package:almashal/src/core/routes/app_pages.dart';
import 'package:almashal/src/core/values/assets.dart';
import 'package:almashal/src/data/models/vms/user_vm.dart';
import 'package:almashal/src/data/services/auth_service.dart';
import 'package:almashal/src/data/services/error_service.dart';
import 'package:almashal/src/view/components/buttons/custom_filled_button.dart';
import 'package:almashal/src/view/components/form_fields/app_text_form_field.dart';
import 'package:almashal/src/view/components/form_fields/custom_phone_form_field.dart';
import 'package:almashal/src/view/components/layouts/master_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:phone_form_field/phone_form_field.dart';

class RegisterPage extends StatelessWidget {
  const RegisterPage({super.key});

  @override
  Widget build(BuildContext context) {
    var formKey = GlobalKey<FormState>();
    UserVM data = UserVM();
    Map<String, dynamic> errors = {};
    final RxBool isPasswordVisible = false.obs;
    final RxBool isConfirmPasswordVisible = false.obs;
    // متغير مؤقت لتخزين كلمة المرور للتحقق من المطابقة
    final RxString tempPassword = ''.obs;

    return MasterPage(
      title: 'تسجيل حساب جديد',
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.transparent,
              Theme.of(context).primaryColor.withOpacity(0.08),
            ],
          ),
        ),
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.symmetric(horizontal: 24, vertical: 32),
            child: Obx(
              () {
                if (!ErrorBag.instance.cleared.value) {
                  errors = ErrorBag.instance.getErrors();
                }
                return Form(
                  key: formKey,
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.center,
                    children: [
                      // Logo with animation
                      SvgPicture.asset(
                        Assets.vectorLogo,
                        width: 180,
                      ).animate().fadeIn(duration: 600.ms).scale(
                          begin: const Offset(0.8, 0.8),
                          end: const Offset(1, 1)),
                      const SizedBox(height: 40),

                      // Welcome text with animation
                      Text(
                        'مرحباً بك في تطبيق عائلة المشعل',
                        style: Theme.of(context).textTheme.titleLarge?.copyWith(
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).primaryColor,
                            ),
                        textAlign: TextAlign.center,
                      ).animate().fadeIn(delay: 300.ms, duration: 500.ms),
                      const SizedBox(height: 16),

                      // Description with animation
                      Text(
                        'يرجى إدخال بياناتك للانضمام إلى التطبيق. سيتم إرسال طلبك للمشرف للموافقة عليه.',
                        style: Theme.of(context).textTheme.bodyMedium,
                        textAlign: TextAlign.center,
                      ).animate().fadeIn(delay: 400.ms, duration: 500.ms),
                      const SizedBox(height: 32),

                      // Form fields container with animation
                      Container(
                        decoration: BoxDecoration(
                          color: Colors.transparent,
                          borderRadius: BorderRadius.circular(24),
                          boxShadow: [
                            BoxShadow(
                              color: Colors.black.withOpacity(0.03),
                              blurRadius: 15,
                              offset: const Offset(0, 5),
                              spreadRadius: 0,
                            ),
                          ],
                          border: Border.all(
                            color:
                                Theme.of(context).primaryColor.withOpacity(0.1),
                            width: 1.5,
                          ),
                        ),
                        padding: const EdgeInsets.all(24),
                        child: Column(
                          crossAxisAlignment: CrossAxisAlignment.start,
                          children: [
                            // عنوان معلومات الحساب مع تصميم جديد
                            Container(
                              decoration: BoxDecoration(
                                color: Theme.of(context)
                                    .primaryColor
                                    .withOpacity(0.1),
                                borderRadius: BorderRadius.circular(12),
                              ),
                              padding: const EdgeInsets.symmetric(
                                  vertical: 8, horizontal: 16),
                              child: Row(
                                children: [
                                  Icon(
                                    Icons.person_rounded,
                                    color: Theme.of(context).primaryColor,
                                    size: 20,
                                  ),
                                  const SizedBox(width: 8),
                                  Text(
                                    'معلومات الحساب',
                                    style: Theme.of(context)
                                        .textTheme
                                        .titleMedium
                                        ?.copyWith(
                                          fontWeight: FontWeight.bold,
                                          color: Theme.of(context).primaryColor,
                                        ),
                                  ),
                                ],
                              ),
                            ),
                            const SizedBox(height: 20),

                            // الاسم الكامل
                            AppTextFormField(
                              labelText: 'الاسم الكامل',
                              hintText: 'أدخل اسمك الكامل',
                              fillColor: Colors.transparent,
                              errorText: errors['first_name']?[0],
                              keyboardType: TextInputType.name,
                              prefixIcon: Icon(
                                Icons.person_outline,
                                color: Theme.of(context).primaryColor,
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'يرجى إدخال اسمك الكامل';
                                }
                                if (value.trim().split(' ').length < 2) {
                                  return 'يرجى إدخال الاسم الكامل (الأول والأخير)';
                                }
                                return null;
                              },
                              onSaved: (value) {
                                if (value != null) {
                                  var names = value.trim().split(' ');
                                  data.firstName = names.first;
                                  if (names.length > 2) {
                                    data.middleName = names[1];
                                    data.lastName = names.sublist(2).join(' ');
                                  } else if (names.length == 2) {
                                    data.lastName = names.last;
                                    data.middleName = '';
                                  }
                                }
                              },
                            ),
                            const SizedBox(height: 20),

                            // رقم الجوال - استخدام PhoneFormField
                            CustomPhoneFormField(
                              labelText: 'رقم الجوال',
                              hintText: "ادخل رقم الجوال",
                              errorText: errors['mobile']?[0],
                              prefixIcon: Icon(
                                Icons.phone_android_rounded,
                                color: Get.theme.primaryColor,
                              ),
                              initialValue: PhoneNumber(
                                  isoCode: IsoCode.SA,
                                  nsn: ''), // افتراضي اليمن

                              validator: (PhoneNumber? phone) {
                                if (phone == null || !phone.isValid()) {
                                  return 'يرجى إدخال رقم جوال صحيح';
                                }
                                return null;
                              },
                              onSaved: (value) {
                                if (value != null) {
                                  // حفظ الرقم كاملاً مع مفتاح الدولة في حقل mobile فقط
                                  data.mobile = value.nsn;
                                  data.countryCode = value.countryCode;
                                }
                              },
                              filled: true,
                            ),
                            const SizedBox(height: 20),

                            // كلمة المرور
                            AppTextFormField(
                              labelText: 'كلمة المرور',
                              hintText: 'أدخل كلمة المرور',
                              fillColor: Colors.transparent,
                              errorText: errors['password']?[0],
                              obscureText: true,
                              revealablePassword: true,
                              prefixIcon: Icon(
                                Icons.lock_outline,
                                color: Theme.of(context).primaryColor,
                              ),
                              onChanged: (value) {
                                // حفظ كلمة المرور في المتغير المؤقت عند كتابتها
                                tempPassword.value = value ?? '';
                              },
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'يرجى إدخال كلمة المرور';
                                }
                                if (value.length < 6) {
                                  return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                                }
                                return null;
                              },
                              onSaved: (value) {
                                if (value != null) data.password = value;
                              },
                            ),

                            const SizedBox(height: 16),

                            // تأكيد كلمة المرور
                            AppTextFormField(
                              labelText: 'تأكيد كلمة المرور',
                              hintText: 'أعد إدخال كلمة المرور',
                              fillColor: Colors.transparent,
                              errorText: errors['password_confirmation']?[0],
                              obscureText: true,
                              revealablePassword: true,
                              prefixIcon: Icon(
                                Icons.lock_outline,
                                color: Theme.of(context).primaryColor,
                              ),
                              validator: (value) {
                                if (value == null || value.isEmpty) {
                                  return 'يرجى تأكيد كلمة المرور';
                                }

                                // استخدام المتغير المؤقت للمقارنة بدلاً من data.password
                                if (value != tempPassword.value) {
                                  return 'كلمة المرور غير متطابقة';
                                }
                                return null;
                              },
                              onSaved: (value) {
                                if (value != null)
                                  data.passwordConfirmation = value;
                              },
                            ),

                            // البنود والشروط مع تصميم محسن
                            Container(
                              margin: const EdgeInsets.only(top: 16),
                              padding: const EdgeInsets.all(16),
                              decoration: BoxDecoration(
                                color: Colors.blue.withOpacity(0.05),
                                borderRadius: BorderRadius.circular(12),
                                border: Border.all(
                                  color: Colors.blue.shade100,
                                ),
                              ),
                              child: Column(
                                crossAxisAlignment: CrossAxisAlignment.start,
                                children: [
                                  Row(
                                    crossAxisAlignment:
                                        CrossAxisAlignment.start,
                                    children: [
                                      Icon(
                                        Icons.info_outline,
                                        color: Colors.blue,
                                        size: 18,
                                      ),
                                      const SizedBox(width: 8),
                                      Expanded(
                                        child: Text(
                                          'بالضغط على زر "تسجيل" أنت توافق على البنود والشروط وسياسة الخصوصية الخاصة بالتطبيق.',
                                          style: Theme.of(context)
                                              .textTheme
                                              .bodySmall,
                                        ),
                                      ),
                                    ],
                                  ),
                                  // Align(
                                  //   alignment: Alignment.centerLeft,
                                  //   child: TextButton(
                                  //     onPressed: () {
                                  //       launchUrl(Uri.parse(
                                  //           'https://almashal.org/privacy-policy'));
                                  //     },
                                  //     style: TextButton.styleFrom(
                                  //       padding: EdgeInsets.zero,
                                  //       tapTargetSize:
                                  //           MaterialTapTargetSize.shrinkWrap,
                                  //     ),
                                  //     child: Text(
                                  //       'قراءة سياسة الخصوصية',
                                  //       style: TextStyle(
                                  //         color: Colors.blue,
                                  //         fontWeight: FontWeight.bold,
                                  //         decoration: TextDecoration.underline,
                                  //       ),
                                  //     ),
                                  //   ),
                                  // ),
                                ],
                              ),
                            ),
                          ],
                        ),
                      )
                          .animate()
                          .fadeIn(delay: 500.ms, duration: 500.ms)
                          .slideY(
                              begin: 0.2,
                              end: 0,
                              delay: 500.ms,
                              duration: 500.ms,
                              curve: Curves.easeOutQuad),

                      const SizedBox(height: 32),

                      // Register button with animation
                      SizedBox(
                        width: double.infinity,
                        child: CustomFilledButton(
                          label: 'تسجيل',
                          onTap: () async {
                            if (formKey.currentState?.validate() ?? false) {
                              formKey.currentState?.save();
                              await AuthService.instance.register(data);
                            }
                          },
                          margin: EdgeInsets.zero,
                        ),
                      ).animate().fadeIn(delay: 700.ms, duration: 500.ms),

                      const SizedBox(height: 24),

                      // Login option with animation
                      Row(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Text(
                            'لديك حساب بالفعل؟',
                            style: Theme.of(context).textTheme.bodyMedium,
                          ),
                          TextButton(
                            onPressed: () {
                              Get.toNamed(Routes.LOGIN_PAGE);
                            },
                            child: Text(
                              'تسجيل الدخول',
                              style: TextStyle(
                                color: Theme.of(context).primaryColor,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ).animate().fadeIn(delay: 800.ms, duration: 500.ms),
                    ],
                  ),
                );
              },
            ),
          ),
        ),
      ),
    );
  }
}

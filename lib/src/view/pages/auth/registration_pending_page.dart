import 'package:almashal/src/core/routes/app_pages.dart';
import 'package:almashal/src/core/values/assets.dart';
import 'package:almashal/src/data/services/auth_service.dart';
import 'package:almashal/src/view/components/buttons/custom_filled_button.dart';
import 'package:almashal/src/view/components/layouts/master_page.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:flutter_animate/flutter_animate.dart';
import 'package:phone_form_field/phone_form_field.dart';

class RegistrationPendingPage extends StatefulWidget {
  const RegistrationPendingPage({super.key});

  @override
  State<RegistrationPendingPage> createState() =>
      _RegistrationPendingPageState();
}

class _RegistrationPendingPageState extends State<RegistrationPendingPage> {
  final _formKey = GlobalKey<FormState>();
  final RxBool _isChecking = false.obs;
  String _mobile = '';

  @override
  Widget build(BuildContext context) {
    return MasterPage(
      title: 'طلب التسجيل قيد المراجعة',
      body: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            begin: Alignment.topCenter,
            end: Alignment.bottomCenter,
            colors: [
              Colors.transparent,
              Theme.of(context).primaryColor.withOpacity(0.08),
            ],
          ),
        ),
        child: Center(
          child: SingleChildScrollView(
            padding: const EdgeInsets.all(24),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                // Logo with animation
                SvgPicture.asset(
                  Assets.vectorLogo,
                  width: 180,
                ).animate().fadeIn(duration: 600.ms).scale(
                    begin: const Offset(0.8, 0.8), end: const Offset(1, 1)),
                const SizedBox(height: 40),

                // Animated waiting icon
                Container(
                  width: 160,
                  height: 160,
                  decoration: BoxDecoration(
                    color: Colors.transparent,
                    shape: BoxShape.circle,
                    boxShadow: [
                      BoxShadow(
                        color: Theme.of(context).primaryColor.withOpacity(0.1),
                        blurRadius: 20,
                        spreadRadius: 3,
                      ),
                    ],
                  ),
                  child: Center(
                    child: Icon(
                      Icons.hourglass_top_rounded,
                      size: 70,
                      color: Theme.of(context).primaryColor,
                    ),
                  ),
                ).animate().fadeIn(delay: 300.ms, duration: 700.ms).slideY(
                    begin: 0.3,
                    end: 0,
                    delay: 300.ms,
                    duration: 700.ms,
                    curve: Curves.easeOutQuad),

                const SizedBox(height: 40),

                // Status title with animation
                Text(
                  'طلب التسجيل قيد المراجعة',
                  style: Theme.of(context).textTheme.titleLarge?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).primaryColor,
                      ),
                  textAlign: TextAlign.center,
                ).animate().fadeIn(delay: 500.ms, duration: 500.ms),

                const SizedBox(height: 16),

                // Info about notification with animation
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.08),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: Colors.blue.withOpacity(0.2)),
                  ),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.notifications_active,
                            size: 22,
                            color: Colors.blue,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              'ستصلك إشعارات تحديثات حالة الطلب',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Colors.blue,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'سنرسل لك إشعارًا فور قبول الطلب. يمكنك بعدها تسجيل الدخول باستخدام رقم الجوال وكلمة المرور التي قمت بإدخالها أثناء التسجيل.',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ],
                  ),
                ).animate().fadeIn(delay: 700.ms, duration: 500.ms),

                const SizedBox(height: 32),

                // Check status form with animation
                Container(
                  decoration: BoxDecoration(
                    color: Colors.transparent,
                    borderRadius: BorderRadius.circular(24),
                    boxShadow: [
                      BoxShadow(
                        color: Colors.black.withOpacity(0.03),
                        blurRadius: 15,
                        offset: const Offset(0, 5),
                        spreadRadius: 0,
                      ),
                    ],
                    border: Border.all(
                      color: Theme.of(context).primaryColor.withOpacity(0.1),
                      width: 1.5,
                    ),
                  ),
                  padding: const EdgeInsets.all(24),
                  child: Form(
                    key: _formKey,
                    child: Column(
                      crossAxisAlignment: CrossAxisAlignment.start,
                      children: [
                        // عنوان التحقق من حالة الطلب
                        Container(
                          decoration: BoxDecoration(
                            color:
                                Theme.of(context).primaryColor.withOpacity(0.1),
                            borderRadius: BorderRadius.circular(12),
                          ),
                          padding: const EdgeInsets.symmetric(
                              vertical: 8, horizontal: 16),
                          child: Row(
                            children: [
                              Text(
                                'تحقق من حالة طلبك',
                                style: Theme.of(context)
                                    .textTheme
                                    .titleMedium
                                    ?.copyWith(
                                      fontWeight: FontWeight.bold,
                                      color: Theme.of(context).primaryColor,
                                    ),
                              ),
                            ],
                          ),
                        ),
                        const SizedBox(height: 20),

                        // استخدام PhoneFormField بدلاً من AppTextFormField
                        PhoneFormField(
                          initialValue: PhoneNumber(
                              isoCode: IsoCode.SA, nsn: ''), // افتراضي اليمن

                          decoration: InputDecoration(
                            labelText: 'رقم الجوال',
                            hintText: 'أدخل رقم الجوال الذي سجلت به',
                            fillColor: Colors.transparent,
                            prefixIcon: Icon(
                              Icons.phone_android,
                              color: Theme.of(context).primaryColor,
                            ),
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          validator: (PhoneNumber? phone) {
                            if (phone == null || !phone.isValid()) {
                              return 'يرجى إدخال رقم جوال صحيح';
                            }
                            return null;
                          },
                          enabled: true,
                          textAlign: TextAlign.right,
                          onSaved: (value) {
                            if (value != null) {
                              // استخراج رقم الهاتف مع مفتاح الدولة
                              _mobile = value.nsn;
                            }
                          },
                        ),
                        const SizedBox(height: 24),

                        Obx(() => SizedBox(
                              width: double.infinity,
                              child: CustomFilledButton(
                                label: _isChecking.value
                                    ? 'جاري التحقق...'
                                    : 'تحقق من حالة الطلب',
                                onTap: _isChecking.value
                                    ? () async {} // دالة فارغة عند التحقق
                                    : () async {
                                        if (_formKey.currentState?.validate() ??
                                            false) {
                                          _formKey.currentState?.save();
                                          _isChecking.value = true;

                                          try {
                                            await AuthService.instance
                                                .checkRegistrationStatus(
                                                    _mobile);
                                          } finally {
                                            _isChecking.value = false;
                                          }
                                        }
                                      },
                                margin: EdgeInsets.zero,
                              ),
                            )),
                      ],
                    ),
                  ),
                ).animate().fadeIn(delay: 800.ms, duration: 500.ms),

                const SizedBox(height: 32),

                // Contact for inquiries with animation
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: const Color.fromARGB(255, 141, 195, 143)
                        .withOpacity(0.08),
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(color: Theme.of(context).primaryColor),
                  ),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.support_agent,
                            size: 22,
                            color: Colors.green,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              'للاستفسارات، يرجى التواصل',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).primaryColor,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'يمكنك التواصل مع مسؤول التطبيق عبر البريد الإلكتروني أو رقم الهاتف المذكورين في موقع العائلة.',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ],
                  ),
                ).animate().fadeIn(delay: 900.ms, duration: 500.ms),

                const SizedBox(height: 32),

                // Back to login button with animation
                CustomFilledButton(
                  label: 'العودة لتسجيل الدخول',
                  onTap: () async {
                    await Get.offAllNamed(Routes.LOGIN_PAGE);
                  },
                  color: Theme.of(context).primaryColor,
                  margin: EdgeInsets.zero,
                ).animate().fadeIn(delay: 1000.ms, duration: 500.ms),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

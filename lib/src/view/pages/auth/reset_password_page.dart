import 'package:almashal/src/core/values/assets.dart';
import 'package:almashal/src/data/services/auth_service.dart';
import 'package:almashal/src/view/components/layouts/master_page.dart';
import 'package:feather_icons/feather_icons.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';

import '../../components/buttons/custom_filled_button.dart';
import '../../components/form_fields/app_text_form_field.dart';

class ResetPasswordPage extends StatelessWidget {
  const ResetPasswordPage({super.key});

  @override
  Widget build(BuildContext context) {
    var formKey = GlobalKey<FormState>();
    String mobile = Get.arguments;
    String newPassword = '';
    String newPasswordConfirmation = '';
    String resetPasswordCode = '';
    final RxBool isPasswordVisible = false.obs;
    final RxBool isConfirmPasswordVisible = false.obs;

    return MasterPage(
      title: 'إعادة تعيين كلمة المرور',
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Form(
            key: formKey,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SvgPicture.asset(
                  Assets.vectorLogo,
                  width: 200,
                ),
                const SizedBox(height: 40),

                // رسالة توضيحية
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.blue.withOpacity(0.3)),
                  ),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.info_outline,
                            size: 22,
                            color: Theme.of(context).primaryColor,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              'إعادة تعيين كلمة المرور',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).primaryColor,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'تم إرسال رمز التحقق إلى رقم الجوال ${mobile.replaceAll('+966', '0')}. أدخل الرمز وكلمة المرور الجديدة لإكمال العملية.',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 30),

                // رمز التحقق
                AppTextFormField(
                  labelText: 'رمز التحقق',
                  hintText: 'أدخل رمز التحقق المرسل إلى جوالك',
                  prefixIcon: Icon(
                    Icons.security_rounded,
                    color: Get.theme.primaryColor,
                  ),
                  keyboardType: TextInputType.number,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال رمز التحقق';
                    }
                    return null;
                  },
                  onSaved: (value) {
                    if (value != null) resetPasswordCode = value;
                  },
                ),

                const SizedBox(height: 16),

                // كلمة المرور الجديدة
                AppTextFormField(
                  labelText: 'كلمة المرور الجديدة',
                  hintText: 'أدخل كلمة المرور الجديدة',
                  onChanged: (value) {
                    if (value != null) {
                      newPassword = value;
                    }
                  },
                  prefixIcon: Icon(
                    Icons.lock_rounded,
                    color: Get.theme.primaryColor,
                  ),
                  obscureText: true,
                  revealablePassword: true,
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى إدخال كلمة المرور الجديدة';
                    }
                    if (value.length < 6) {
                      return 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
                    }
                    return null;
                  },
                  onSaved: (value) {
                    if (value != null) newPassword = value;
                  },
                ),

                const SizedBox(height: 16),

                // تأكيد كلمة المرور الجديدة
                AppTextFormField(
                  labelText: 'تأكيد كلمة المرور الجديدة',
                  hintText: 'أعد إدخال كلمة المرور الجديدة',
                  prefixIcon: Icon(
                    Icons.lock_rounded,
                    color: Get.theme.primaryColor,
                  ),
                  obscureText: true,
                  revealablePassword: true,
                  onChanged: (value) {
                    if (value != null) {
                      newPasswordConfirmation = value;
                    }
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'يرجى تأكيد كلمة المرور الجديدة';
                    }
                    if (value != newPassword) {
                      return 'كلمة المرور غير متطابقة';
                    }
                    return null;
                  },
                  onSaved: (value) {
                    if (value != null) newPasswordConfirmation = value;
                  },
                ),

                // زر إعادة تعيين
                SizedBox(
                  width: double.maxFinite,
                  child: CustomFilledButton(
                    margin: const EdgeInsets.only(top: 32),
                    label: 'إعادة تعيين كلمة المرور',
                    onTap: () async {
                      if (formKey.currentState?.validate() ?? false) {
                        formKey.currentState?.save();
                        await AuthService.instance.resetPassword(
                          mobile,
                          resetPasswordCode,
                          newPassword,
                          newPasswordConfirmation,
                        );
                      }
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

import 'package:almashal/src/core/values/assets.dart';
import 'package:almashal/src/data/services/auth_service.dart';
import 'package:almashal/src/view/components/form_fields/custom_phone_form_field.dart';
import 'package:almashal/src/view/components/layouts/master_page.dart';
import 'package:feather_icons/feather_icons.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import 'package:phone_form_field/phone_form_field.dart';

import '../../components/buttons/custom_filled_button.dart';
import '../../components/form_fields/app_text_form_field.dart';

class ForgetPasswordPage extends StatelessWidget {
  const ForgetPasswordPage({super.key});

  @override
  Widget build(BuildContext context) {
    var formKey = GlobalKey<FormState>();
    String mobile = '';
    return MasterPage(
      title: 'استعادة كلمة المرور',
      body: Center(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Form(
            key: formKey,
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              crossAxisAlignment: CrossAxisAlignment.center,
              children: [
                SvgPicture.asset(
                  Assets.vectorLogo,
                  width: 200,
                ),
                const SizedBox(height: 50),

                // رسالة توضيحية
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.blue.withOpacity(0.1),
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(color: Colors.blue.withOpacity(0.3)),
                  ),
                  child: Column(
                    children: [
                      Row(
                        children: [
                          Icon(
                            Icons.info_outline,
                            size: 22,
                            color: Theme.of(context).primaryColor,
                          ),
                          const SizedBox(width: 12),
                          Expanded(
                            child: Text(
                              'استعادة كلمة المرور',
                              style: TextStyle(
                                fontWeight: FontWeight.bold,
                                color: Theme.of(context).primaryColor,
                              ),
                            ),
                          ),
                        ],
                      ),
                      const SizedBox(height: 8),
                      Text(
                        'أدخل رقم الجوال المسجل به في التطبيق، وسيتم إرسال رمز التحقق إليك عبر رسالة للواتس اب.',
                        style: Theme.of(context).textTheme.bodySmall,
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 30),

                // رقم الجوال - استخدام PhoneFormField
                CustomPhoneFormField(
                  initialValue: PhoneNumber(
                      isoCode: IsoCode.SA, nsn: ''), // افتراضي اليمن

                  validator: (PhoneNumber? phone) {
                    if (phone == null || !phone.isValid()) {
                      return 'يرجى إدخال رقم جوال صحيح';
                    }
                    return null;
                  },
                  onSaved: (value) {
                    if (value != null) {
                      mobile = value.nsn;
                    }
                  },
                  filled: true,
                ),

                // login button
                SizedBox(
                  width: double.maxFinite,
                  child: CustomFilledButton(
                    margin: const EdgeInsets.only(top: 32),
                    label: 'إرسال رمز التحقق',
                    onTap: () async {
                      if (formKey.currentState?.validate() ?? false) {
                        formKey.currentState?.save();
                        await AuthService.instance.forgetPassword(mobile);
                      }
                    },
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

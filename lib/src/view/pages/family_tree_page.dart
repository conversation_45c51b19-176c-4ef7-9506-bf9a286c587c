import 'package:almashal/src/view/components/layouts/master_page.dart';
import 'package:almashal/src/view/pages/family_tree/widgets/interactive_family_tree_widget.dart';
import 'package:almashal/src/view/pages/family_tree/widgets/printed_family_tree_widget.dart';
import 'package:flutter/material.dart';
import 'package:google_fonts/google_fonts.dart';
import 'package:get/get.dart';

class FamilyTreePage extends StatefulWidget {
  const FamilyTreePage({super.key});

  @override
  State<FamilyTreePage> createState() => _FamilyTreePageState();
}

class _FamilyTreePageState extends State<FamilyTreePage> {
  late int currentTap;
  
  @override
  void initState() {
    super.initState();
    // التحقق من وجود معلمة التبويب الأولي
    final arguments = Get.arguments as Map<String, dynamic>?;
    currentTap = arguments != null && arguments.containsKey('initialTab') 
        ? arguments['initialTab'] as int 
        : 0;
  }
  
  @override
  Widget build(BuildContext context) {
    return MasterPage(
      title: 'شجرة العائلة',
      // floatingActionButton: Obx(() => currentTap == 0 &&
      //         FamilyTreeController.instance.pageLoadingStatus.value ==
      //             PageLoadingStatus.done
      //     ? FloatingActionButton(
      //         onPressed: () {
      //           Share.share(FamilyTreeController.instance.shareText.value);
      //         },
      //         mini: true,
      //         child: const Icon(
      //           FeatherIcons.download,
      //         ),
      //       )
      //     : Container()),
      body: DefaultTabController(
        length: 2,
        initialIndex: currentTap,
        child: Column(
          children: [
            TabBar(
                labelStyle: TextStyle(
                  fontSize: 16,
                  fontWeight: FontWeight.bold,
                  fontFamily:
                      GoogleFonts.tajawalTextTheme().titleSmall?.fontFamily,
                ),
                onTap: (value) {
                  setState(() {
                    currentTap = value;
                  });
                },
                tabs: const [
                  Tab(
                    text: "الشجرة المطبوعة",
                  ),
                  Tab(
                    text: "الشجرة التفاعلية",
                  ),
                ]),
            Expanded(
              child: TabBarView(
                physics: const NeverScrollableScrollPhysics(),
                children: [
                  const PrintedFamilyTreeWidget(),
                  const InteractiveFamilyTreeWidget(),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}

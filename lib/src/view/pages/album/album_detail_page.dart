import 'package:almashal/src/core/routes/app_pages.dart';
import 'package:almashal/src/core/utils/record_visit_function.dart';
import 'package:almashal/src/core/values/app_config.dart';
import 'package:almashal/src/core/values/assets.dart';
import 'package:almashal/src/view/components/image/custom_cached_network_image.dart';
import 'package:almashal/src/view/components/list_tiles/custom_icon_list_tile.dart';
import 'package:almashal/src/view/components/list_tiles/custom_image_list_tile.dart';
import 'package:almashal/src/view/components/widgets/visit_count_widget.dart';
import 'package:flutter/material.dart';
import 'package:flutter_svg/flutter_svg.dart';
import 'package:get/get.dart';
import '../../../data/models/album.dart';
import '../../../data/models/media.dart';
import '../../components/layouts/master_page.dart';

class AlbumDetailPage extends StatelessWidget {
  const AlbumDetailPage({super.key});

  @override
  Widget build(BuildContext context) {
    Album album = Get.arguments;

    // Record the visit
    WidgetsBinding.instance.addPostFrameCallback((_) {
      RecordVisitFunction.album(album.id);
    });

    return MasterPage(
      title: album.title,
      // shareText: CommonFunctions.htmlToShareableText(album.),
      appBarActions: [
        Padding(
          padding: const EdgeInsets.only(right: 16.0),
          child: VisitCountWidget(
            visitsCount: album.visitsCount,
            textColor: Colors.white,
            
          ),
        ),
      ],
      body: album.viewType == 1
          ? GridView.builder(
              itemCount: album.media.length,
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 3,
                crossAxisSpacing: 2,
                mainAxisSpacing: 2,
              ),
              itemBuilder: (BuildContext context, int index) {
                return AlbumGridItem(
                  media: album.media[index],
                );
              },
            )
          : ListView.builder(
              padding: const EdgeInsets.all(16),
              itemCount: album.media.length,
              itemBuilder: (BuildContext context, int index) {
                return AlbumMediaListTitle(
                  media: album.media[index],
                );
              },
            ),
    );
  }
}

class AlbumMediaListTitle extends StatelessWidget {
  const AlbumMediaListTitle({
    super.key,
    required this.media,
  });
  final Media media;
  @override
  Widget build(BuildContext context) {
    return media.type == 3
        ? CustomIconListTile(
            svgAsset: Assets.pdfFile,
            title: media.title ?? "",
            onTap: () {
              Get.toNamed(
                Routes.PDF_VIEW_PAGE,
                arguments: {
                  "media": media.media,
                  "title": media.title,
                  "detail": media.detail,
                },
              );
            })
        : CustomImageListTile(
            imageUrl: media.type == 2
                ? "https://img.youtube.com/vi_webp/${media.media}/default.webp"
                : AppConfig.thumbBaseUrl + media.media,
            title: media.title ?? "",
            onTap: () {
              if (media.type == 1) {
                Get.toNamed(
                  Routes.IMAGE_VIEW_PAGE,
                  arguments: {
                    "media": AppConfig.imageBaseUrl + media.media,
                    "title": media.title,
                    "detail": media.detail,
                    "images": media.images,
                  },
                );
              } else if (media.type == 2) {
                Get.toNamed(
                  Routes.VIDEO_VIEW_PAGE,
                  arguments: {
                    "media": media.media,
                    "title": media.title,
                    "detail": media.detail,
                  },
                );
              }
            },
            margin: const EdgeInsets.only(
              bottom: 16,
            ),
          );
  }
}

class AlbumGridItem extends StatelessWidget {
  final Media media;
  const AlbumGridItem({
    super.key,
    required this.media,
  });

  @override
  Widget build(BuildContext context) {
    if (media.type == 1) {
      // image
      return InkWell(
        onTap: () {
          Get.toNamed(
            Routes.IMAGE_VIEW_PAGE,
            arguments: {
              "media": AppConfig.imageBaseUrl + media.media,
              "thumb": AppConfig.thumbBaseUrl + media.media,
              "title": media.title,
              "detail": media.detail,
              "images": media.images,
            },
          );
        },
        child: CustomCachedNetworkImage(
          imageUrl: AppConfig.thumbBaseUrl + media.media,
          fit: BoxFit.cover,
          borderRadius: BorderRadius.zero,
        ),
      );
    } else if (media.type == 2) {
      // youtube video
      return InkWell(
        onTap: () {
          Get.toNamed(
            Routes.VIDEO_VIEW_PAGE,
            arguments: {
              "media": media.media,
              "title": media.title,
              "detail": media.detail,
            },
          );
        },
        child: Stack(
          alignment: Alignment.center,
          children: [
            Image.network(
              "https://img.youtube.com/vi_webp/${media.media}/default.webp",
              height: double.maxFinite,
              fit: BoxFit.cover,
            ),
            SvgPicture.asset(
              Assets.play,
              colorFilter: ColorFilter.mode(
                Colors.white,
                BlendMode.srcIn,
              ),
            ),
          ],
        ),
      );
    } else {
      // pdf file
      return InkWell(
        onTap: () {
          Get.toNamed(
            Routes.PDF_VIEW_PAGE,
            arguments: {
              "media": media.media,
              "title": media.title,
              "detail": media.detail,
            },
          );
        },
        child: Stack(
          children: [
            Padding(
              padding: const EdgeInsets.all(16.0),
              child: SvgPicture.asset(
                Assets.pdfFile,
                height: double.maxFinite,
                colorFilter: ColorFilter.mode(
                  Get.theme.primaryColor,
                  BlendMode.srcIn,
                ),
              ),
            ),
          ],
        ),
      );
    }
  }
}

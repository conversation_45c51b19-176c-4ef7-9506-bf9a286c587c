import 'dart:math';

import 'package:almashal/src/custom_packages/marquee/marquee.dart';
import 'package:almashal/src/data/services/auth_service.dart';
import 'package:almashal/src/data/services/messaging_service.dart';
import 'package:almashal/src/view/components/bottomsheets/custom_bottom_sheet.dart';
import 'package:almashal/src/view/components/layouts/custom_drawer.dart';
import 'package:almashal/src/view/components/layouts/social_media_links_components.dart';
import 'package:app_links/app_links.dart';
import 'package:confetti/confetti.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:lottie/lottie.dart';

import '../../core/routes/app_pages.dart';
import '../../core/values/assets.dart';
import '../components/buttons/custom_filled_button.dart';
import '../components/cards/home_card.dart';
import '../components/layouts/master_page.dart';

class HomePage extends StatefulWidget {
  const HomePage({super.key});

  @override
  State<HomePage> createState() => _HomePageState();
}

class _HomePageState extends State<HomePage> with TickerProviderStateMixin {
  late final AnimationController _controller;
  late ConfettiController confettiController;
  bool hideAnimation = false;
  @override
  void dispose() {
    _controller.dispose();
    confettiController.dispose();
    super.dispose();
  }

  @override
  void initState() {
    super.initState();
    confettiController =
        ConfettiController(duration: const Duration(seconds: 3));
    _controller = AnimationController(vsync: this);
    MessagingService().init().then((value) => Get.put(value));
    final appLinks = AppLinks(); // AppLinks is singleton

// Subscribe to all events (initial link and further)
    appLinks.uriLinkStream.listen((uri) {
      print('appLink');
      print(uri);
    });
  }

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        MasterPage(
          drawer: const CustomDrawer(),
          withBackground: true,
          title: "أسرة المشعل",
          body: Center(
            child: Column(
              children: [
                Obx(() {
                  if (AuthService.instance.slideTex.value == null) {
                    return SizedBox();
                  }
                  return InkWell(
                    onTap: AuthService.instance.slideCoffetteEnabled.value
                        ? () {
                            confettiController.play();
                          }
                        : null,
                    child: Container(
                      width: double.maxFinite,
                      decoration: BoxDecoration(
                        color: Get.theme.primaryColor.withOpacity(0.3),
                      ),
                      height: 50,
                      child: Marquee(
                          text: AuthService.instance.slideTex.value ?? "",
                          style: TextStyle(
                            color: Get.theme.primaryColor,
                            fontSize: 16,
                            fontWeight: FontWeight.bold,
                          ),
                          blankSpace: 100.0),
                    ),
                  );
                }),
                Expanded(
                  child: SingleChildScrollView(
                    child: Column(
                      children: [
                        SizedBox(
                          height: 32,
                        ),
                        Stack(
                          alignment: Alignment.center,
                          children: [
                            Container(
                              width: 350.0,
                              height: 500.0,
                              decoration: const BoxDecoration(
                                // color: Colors.orange,
                                shape: BoxShape.circle,
                              ),
                            ),
                            Positioned(
                              child: HomeCard(
                                title: 'شجرة العائلة',
                                svgIcon: Assets.tree,
                                onTap: () {
                                  Get.toNamed(Routes.FAMILY_TREE_PAGE);
                                },
                              ),
                            ),
                            Positioned(
                              top: 0,
                              child: HomeCard(
                                title: 'النسب',
                                svgIcon: Assets.family,
                                onTap: () {
                                  Get.toNamed(Routes.PARENT_PAGE);
                                },
                              ),
                            ),
                            Positioned(
                              bottom: 0,
                              child: HomeCard(
                                title: 'المناسبات',
                                svgIcon: Assets.calendar,
                                onTap: () {
                                  Get.toNamed(Routes.OCCASIONS_PAGE);
                                },
                              ),
                            ),
                            Positioned(
                              right: 0,
                              top: 100,
                              child: HomeCard(
                                title: 'الأخبار',
                                svgIcon: Assets.news,
                                onTap: () {
                                  Get.toNamed(Routes.NEWS_PAGE);
                                },
                              ),
                            ),
                            Positioned(
                              left: 0,
                              top: 100,
                              child: HomeCard(
                                title: 'من الذاكرة',
                                svgIcon: Assets.images,
                                onTap: () {
                                  Get.toNamed(Routes.MEMORY_PAGE);
                                },
                              ),
                            ),
                            Positioned(
                              right: 0,
                              bottom: 100,
                              child: HomeCard(
                                title: 'اللجان',
                                svgIcon: Assets.commitee,
                                onTap: () {
                                  Get.toNamed(Routes.COMMITTEES_PAGE);
                                },
                              ),
                            ),
                            Positioned(
                              left: 0,
                              bottom: 100,
                              child: HomeCard(
                                title: 'جائزة التفوق',
                                svgIcon: Assets.medal,
                                onTap: () {
                                  Get.toNamed(Routes.EXCELLENCE_AWARDS_PAGE);
                                },
                              ),
                            ),
                          ],
                        ),
                        Container(
                          constraints: BoxConstraints(
                            maxWidth: 350,
                            minWidth: 120,
                          ),
                          width: double.maxFinite,
                          child: Obx(
                            () => CustomFilledButton(
                              label: AuthService.instance.userData.value != null
                                  ? 'تسجيل الخروج'
                                  : 'تسجيل الدخول',
                              onTap: AuthService.instance.userData.value != null
                                  ? () async {
                                      Get.bottomSheet(
                                        CustomBottomSheet(
                                          title: "تسجيل الخروج",
                                          body: Padding(
                                            padding: const EdgeInsets.all(16.0),
                                            child: Column(
                                              children: [
                                                const Text(
                                                    "هل انت متأكد من تسجيل الخروج؟"),
                                                Padding(
                                                  padding:
                                                      const EdgeInsets.only(
                                                          top: 32.0),
                                                  child: Row(
                                                    children: [
                                                      Expanded(
                                                        child:
                                                            CustomFilledButton(
                                                          padding:
                                                              const EdgeInsets
                                                                  .symmetric(
                                                            horizontal: 16,
                                                            vertical: 4,
                                                          ),
                                                          onTap: () async {
                                                            await AuthService
                                                                .instance
                                                                .logout();
                                                          },
                                                          label: 'نعم',
                                                        ),
                                                      ),
                                                      const SizedBox(
                                                        width: 16,
                                                      ),
                                                      Expanded(
                                                        child:
                                                            CustomFilledButton(
                                                          padding:
                                                              const EdgeInsets
                                                                  .symmetric(
                                                            horizontal: 16,
                                                            vertical: 4,
                                                          ),
                                                          color:
                                                              Colors.redAccent,
                                                          onTap: () async {
                                                            Get.back();
                                                          },
                                                          label: 'لا',
                                                        ),
                                                      )
                                                    ],
                                                  ),
                                                )
                                              ],
                                            ),
                                          ),
                                        ),
                                        isScrollControlled: true,
                                      );
                                    }
                                  : () async {
                                      Get.toNamed(Routes.LOGIN_PAGE);
                                    },
                              margin: const EdgeInsets.only(
                                bottom: 16,
                                top: 64,
                              ),
                            ),
                          ),
                        ),
                        const SocialMediaLinksComponent(
                          isInHome: true,
                        ),
                      ],
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
        Align(
          alignment: Alignment.topCenter,
          child: ConfettiWidget(
            confettiController: confettiController,
            blastDirection: pi / 2,
            maxBlastForce: 5, // set a lower max blast force
            minBlastForce: 2, // set a lower min blast force
            emissionFrequency: 0.5,
            numberOfParticles: 10, // a lot of particles at once
            gravity: 0.1,
            shouldLoop: false,
          ),
        ),
      ],
    );
  }
}

import 'package:almashal/src/controllers/excellence_award_controller.dart';
import 'package:almashal/src/data/enums/page_loading_status.dart';
import 'package:almashal/src/view/components/errors/loading.dart';
import 'package:almashal/src/view/components/errors/no_content.dart';
import 'package:almashal/src/view/components/errors/no_internet.dart';
import 'package:almashal/src/view/components/errors/server_error.dart';
import 'package:almashal/src/view/components/layouts/master_page.dart';
import 'package:almashal/src/view/components/list_tiles/excellence_award_list_tile.dart';
import 'package:flutter/widgets.dart';
import 'package:get/get.dart';

class ExcellenceAwardsPage extends GetView<ExcellenceAwardController> {
  const ExcellenceAwardsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return MasterPage(
      title: 'جائزة التفوق',
      isSearchable: true,
      onSearchSubmitted: (value) {
        if (value != null) {
          controller.search(value);
        }
      },
      onSearchToggled: (value) {
        if (!value) {
          controller.clearSearch();
        }
      },
      body: Obx(
        () {
          if (controller.pageLoadingStatus.value == PageLoadingStatus.loading) {
            return const Loading();
          }
          if (controller.pageLoadingStatus.value ==
              PageLoadingStatus.networkError) {
            return const NoInternet();
          }
          if (controller.pageLoadingStatus.value == PageLoadingStatus.failed) {
            return const ServerError();
          }
          var awards = controller.excellenceAwards.value;
          if (awards.isEmpty) {
            String message = "عذراً، لا يوجد أخبار حالياً.";
            if (controller.searchTerm.value?.isNotEmpty ?? false) {
              message =
                  "عذراً، لا يوجد نتائج مطابقة لكلمة البحث  '${controller.searchTerm.value}'.";
            }
            return NoContent(
              message: message,
            );
          }
          return ListView.builder(
            itemCount: awards.length,
            padding: const EdgeInsets.all(
              16,
            ),
            itemBuilder: (BuildContext context, int index) {
              return AwardListTitle(
                award: awards[index],
                last: index == awards.length - 1,
              );
            },
          );
        },
      ),
    );
  }
}

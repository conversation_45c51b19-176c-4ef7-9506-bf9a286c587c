import 'package:almashal/src/core/theme/app_colors.dart';
import 'package:almashal/src/core/utils/record_visit_function.dart';
import 'package:almashal/src/data/models/excellence_award.dart';
import 'package:almashal/src/view/components/fabs/add_comment_floating_action_button.dart';
import 'package:almashal/src/view/components/html/html_view_component.dart';
import 'package:almashal/src/view/components/layouts/master_page.dart';
import 'package:almashal/src/view/components/list/comment_list.dart';
import 'package:almashal/src/view/components/widgets/visit_count_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class ExcellenceAwardDetailPage extends StatelessWidget {
  const ExcellenceAwardDetailPage({super.key});

  @override
  Widget build(BuildContext context) {
    ExcellenceAward award = Get.arguments;
    GlobalKey key = GlobalKey();

    // Record the visit
    WidgetsBinding.instance.addPostFrameCallback((_) {
      RecordVisitFunction.award(award.id);
    });

    return MasterPage(
      title: award.title,
      floatingActionButton: AddCommentFloatingActionButton(
        type: 'award',
        id: award.id,
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.only(bottom: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Padding(
              padding: const EdgeInsets.only(
                top: 12.0,
                right: 16,
                left: 16,
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Visit count
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          award.title,
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                            color: AppColors.secondary
                          ),
                        ),
                      ),
                      VisitCountWidget(
                        visitsCount: award.visitsCount,
                      ),
                    ],
                  ),
                  HtmlViewComponent(
                    html: award.content,
                  ),
                  CommentList(
                    key: key,
                    type: 'award',
                    id: award.id,
                  ),
                ],
              ),
            )
          ],
        ),
      ),
    );
  }
}

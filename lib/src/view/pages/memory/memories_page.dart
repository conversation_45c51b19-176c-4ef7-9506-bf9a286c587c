import 'package:almashal/src/controllers/albums_controller.dart';
import 'package:almashal/src/core/values/app_config.dart';
import 'package:almashal/src/view/components/cards/custom_first_card.dart';
import 'package:almashal/src/view/components/loadings/loadmore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../core/routes/app_pages.dart';
import '../../../data/enums/page_loading_status.dart';
import '../../components/cards/album_card.dart';
import '../../components/errors/loading.dart';
import '../../components/errors/no_content.dart';
import '../../components/errors/no_internet.dart';
import '../../components/errors/server_error.dart';
import '../../components/layouts/master_page.dart';

class MemoriesPage extends GetView<AlbumsController> {
  const MemoriesPage({super.key});

  @override
  Widget build(BuildContext context) {
    return MasterPage(
      title: 'من الذاكرة',
      isSearchable: true,
      onSearchSubmitted: (value) {
        if (value != null) {
          controller.search(value);
        }
      },
      onSearchToggled: (value) {
        if (!value) {
          controller.clearSearch();
        }
      },
      body: Obx(
        () {
          if (controller.pageLoadingStatus.value == PageLoadingStatus.loading) {
            return const Loading();
          } else if (controller.pageLoadingStatus.value ==
              PageLoadingStatus.networkError) {
            return const NoInternet();
          } else if (controller.pageLoadingStatus.value ==
              PageLoadingStatus.failed) {
            return const ServerError();
          } else {
            var albums = controller.albums.value;
            if (albums.isEmpty) {
              String message = "عذراً، لا يوجد أخبار حالياً.";
              if (controller.searchTerm.value?.isNotEmpty ?? false) {
                message =
                    "عذراً، لا يوجد نتائج مطابقة لكلمة البحث  '${controller.searchTerm.value}'.";
              }
              return NoContent(
                message: message,
              );
            }
            return ListView(
              padding: const EdgeInsets.all(16),
              children: [
                CustomFirstCard(
                  text: albums[0].title,
                  imageUrl: AppConfig.thumbBaseUrl + albums[0].image,
                  onTap: () {
                    Get.toNamed(Routes.ALBUM_DETAIL_PAGE, arguments: albums[0]);
                  },
                ),
                GridView.builder(
                  padding: const EdgeInsets.only(top: 16),
                  shrinkWrap: true,
                  itemCount: albums.length - 1,
                  physics: const NeverScrollableScrollPhysics(),
                  itemBuilder: (BuildContext context, int index) {
                    return AlbumCard(
                      title: albums[index + 1].title,
                      image: AppConfig.thumbBaseUrl + albums[index + 1].image,
                      onTap: () {
                        Get.toNamed(Routes.ALBUM_DETAIL_PAGE,
                            arguments: albums[index + 1]);
                      },
                    );
                  },
                  gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                    crossAxisCount: 2,
                    crossAxisSpacing: 16,
                    mainAxisSpacing: 16,
                    childAspectRatio: 1,
                  ),
                ),
                if (!controller.cantLoadMore())
                  Center(
                    child: LoadMore(onLoadMore: () {
                      controller.loadMore();
                    }),
                  )
              ],
            );
          }
        },
      ),
    );
  }
}

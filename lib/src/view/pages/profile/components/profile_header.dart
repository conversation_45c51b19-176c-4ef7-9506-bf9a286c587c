import 'package:flutter/material.dart';
import '../../../../core/theme/profile_theme.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:feather_icons/feather_icons.dart';

class ProfileHeader extends StatelessWidget {
  final String coverImageUrl;
  final String profileImageUrl;
  final String name;
  final String title;
  final VoidCallback? onEditProfile;
  final VoidCallback? onChangeCoverImage;
  final bool isVisitor;

  const ProfileHeader({
    super.key,
    required this.coverImageUrl,
    required this.profileImageUrl,
    required this.name,
    required this.title,
    this.onEditProfile,
    this.onChangeCoverImage,
    this.isVisitor = false,
  });

  // Helper method to get cover image provider with error handling
  ImageProvider _getCoverImageProvider(String url, BuildContext context) {
    try {
      if (url.isEmpty || url == 'https://example.com/cover.jpg') {
        // Return a default asset image if URL is empty or the example placeholder
        return const AssetImage('assets/images/background.png');
      }
      if (!url.startsWith('http')) {
        url = 'https://almashalfamily.com/uploads/$url';
      }
      return CachedNetworkImageProvider(url);
    } catch (e) {
      // Return a default asset image on error
      return const AssetImage('assets/images/background.png');
    }
  }

  @override
  Widget build(BuildContext context) {
    return SizedBox(
      height: 280,
      child: Stack(
        clipBehavior: Clip.none,
        children: [
          // صورة الغلاف
          Positioned.fill(
            child: Container(
              decoration: BoxDecoration(
                color: ProfileTheme.primaryColor,
                image: DecorationImage(
                  image: _getCoverImageProvider(coverImageUrl, context),
                  fit: BoxFit.cover,
                  colorFilter: ColorFilter.mode(
                    Colors.black.withOpacity(0.4),
                    BlendMode.darken,
                  ),
                ),
              ),
            ),
          ),

          // زر الكاميرا لتغيير صورة الغلاف
          if (!isVisitor && onChangeCoverImage != null)
            Positioned(
              top: 16,
              right: 16,
              child: CircleAvatar(
                backgroundColor: Colors.white.withOpacity(0.8),
                radius: 20,
                child: IconButton(
                  icon: const Icon(FeatherIcons.camera, size: 18),
                  color: ProfileTheme.primaryColor,
                  onPressed: onChangeCoverImage,
                  tooltip: 'تغيير صورة الغلاف',
                ),
              ),
            ),

          // الصورة الشخصية والمعلومات
          Positioned(
            bottom: 0,
            left: 0,
            right: 0,
            child: Container(
              padding: const EdgeInsets.all(16),
              child: Column(
                children: [
                  // الصورة الشخصية
                  Container(
                    width: 100,
                    height: 100,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      border: Border.all(color: Colors.white, width: 4),
                      boxShadow: [
                        BoxShadow(
                          color: Colors.black.withOpacity(0.2),
                          blurRadius: 10,
                          offset: const Offset(0, 5),
                        ),
                      ],
                    ),
                    child: ClipOval(
                      child: CachedNetworkImage(
                        imageUrl: profileImageUrl,
                        fit: BoxFit.cover,
                        placeholder: (context, url) => Container(
                          color: ProfileTheme.primaryColor.withOpacity(0.5),
                        ),
                        errorWidget: (context, url, error) => Container(
                          color: ProfileTheme.primaryColor.withOpacity(0.5),
                          child: const Icon(Icons.person,
                              size: 50, color: Colors.white),
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 12),
                  // الاسم
                  Text(
                    name,
                    style: const TextStyle(
                      fontSize: 24,
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      shadows: [
                        Shadow(
                          offset: Offset(0, 2),
                          blurRadius: 4,
                          color: Color.fromARGB(50, 0, 0, 0),
                        ),
                      ],
                    ),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 4),
                  // العنوان الوظيفي
                  Text(
                    title,
                    style: const TextStyle(
                      fontSize: 16,
                      color: Colors.white70,
                      fontWeight: FontWeight.w500,
                    ),
                    textAlign: TextAlign.center,
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }
}

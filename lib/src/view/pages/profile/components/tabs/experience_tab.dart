import 'package:almashal/src/view/pages/profile/widget/empty_state.dart';
import 'package:almashal/src/view/pages/profile/widget/loading_indicator.dart';
import 'package:datetime_picker_formfield_new/datetime_picker_formfield.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:almashal/src/controllers/profile/experiences_controller.dart';
import 'package:almashal/src/data/models/profile/experience.dart';
import 'package:intl/intl.dart';

class ExperienceTab extends StatefulWidget {
  final bool isOwner;
  final String userId;

  const ExperienceTab({
    super.key,
    required this.isOwner,
    required this.userId,
  });

  @override
  State<ExperienceTab> createState() => _ExperienceTabState();
}

class _ExperienceTabState extends State<ExperienceTab> {
  late final ExperiencesController _controller;
  final _formKey = GlobalKey<FormState>();

  @override
  void initState() {
    super.initState();
    _controller = Get.find<ExperiencesController>();
    _controller.setUserId(widget.userId);
    // _controller.loadExperiences();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: RefreshIndicator(
        onRefresh: _controller.loadExperiences,
        child: Obx(() {
          if (_controller.isLoading.value) {
            return const LoadingIndicator();
          }

          if (_controller.experiencesList.isEmpty) {
            return EmptyState(
              message: 'لا توجد خبرات مضافة',
              onRefresh: _controller.loadExperiences,
            );
          }

          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: _controller.experiencesList.length,
            itemBuilder: (context, index) {
              final experience = _controller.experiencesList[index];
              return _buildExperienceCard(experience);
            },
          );
        }),
      ),
      floatingActionButton: widget.isOwner
          ? FloatingActionButton(
              onPressed: () => _showAddEditDialog(),
              tooltip: 'إضافة خبرة',
              child: const Icon(Icons.add),
            )
          : null,
    );
  }

  Widget _buildExperienceCard(Experience experience) {
    final theme = Theme.of(context);
    return Card(
      margin: const EdgeInsets.only(bottom: 16),
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        experience.title,
                        style: theme.textTheme.titleLarge?.copyWith(
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        experience.organization,
                        style: theme.textTheme.titleMedium?.copyWith(
                          color: theme.colorScheme.primary,
                        ),
                      ),
                    ],
                  ),
                ),
                if (widget.isOwner)
                  PopupMenuButton<String>(
                    onSelected: (value) {
                      switch (value) {
                        case 'edit':
                          _showAddEditDialog(experience);
                          break;
                        case 'delete':
                          _confirmDelete(experience);
                          break;
                      }
                    },
                    itemBuilder: (context) => [
                      const PopupMenuItem(
                        value: 'edit',
                        child: Row(
                          children: [
                            Icon(Icons.edit),
                            SizedBox(width: 8),
                            Text('تعديل'),
                          ],
                        ),
                      ),
                      PopupMenuItem(
                        value: 'delete',
                        child: Row(
                          children: [
                            Icon(Icons.delete, color: theme.colorScheme.error),
                            const SizedBox(width: 8),
                            Text(
                              'حذف',
                              style: TextStyle(color: theme.colorScheme.error),
                            ),
                          ],
                        ),
                      ),
                    ],
                  ),
              ],
            ),
            if (experience.location != null) ...[
              const SizedBox(height: 8),
              Row(
                children: [
                  Icon(Icons.location_on_outlined,
                      size: 16, color: theme.colorScheme.secondary),
                  const SizedBox(width: 8),
                  Text(
                    experience.location!,
                    style: theme.textTheme.bodyMedium,
                  ),
                ],
              ),
            ],
            const SizedBox(height: 12),
            Text(
              experience.description,
              style: theme.textTheme.bodyMedium,
            ),
            const SizedBox(height: 12),
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
              decoration: BoxDecoration(
                color: theme.colorScheme.secondaryContainer,
                borderRadius: BorderRadius.circular(16),
              ),
              child: Text(
                experience.typeLabel,
                style: theme.textTheme.labelMedium?.copyWith(
                  color: Colors.white,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _showAddEditDialog([Experience? experience]) async {
    final isEditing = experience != null;
    final titleController = TextEditingController(text: experience?.title);
    final organizationController =
        TextEditingController(text: experience?.organization);
    final locationController =
        TextEditingController(text: experience?.location);
    final descriptionController =
        TextEditingController(text: experience?.description);
    String type = experience?.type ?? 'job';

    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Form(
            key: _formKey,
            autovalidateMode: AutovalidateMode.onUserInteraction,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  isEditing ? 'تعديل خبرة' : 'إضافة خبرة',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                ),
                const SizedBox(height: 24),
                TextFormField(
                  controller: titleController,
                  decoration: InputDecoration(
                    labelText: 'المسمى الوظيفي',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    prefixIcon: const Icon(Icons.work),
                    filled: true,
                    fillColor:
                        Theme.of(context).colorScheme.surfaceContainerHighest,
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'الرجاء إدخال المسمى الوظيفي';
                    }
                    if (value.length > 100) {
                      return 'المسمى الوظيفي طويل جداً';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: organizationController,
                  decoration: InputDecoration(
                    labelText: 'اسم المؤسسة',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    prefixIcon: const Icon(Icons.business),
                    filled: true,
                    fillColor:
                        Theme.of(context).colorScheme.surfaceContainerHighest,
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'الرجاء إدخال اسم المؤسسة';
                    }
                    if (value.length > 100) {
                      return 'اسم المؤسسة طويل جداً';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: locationController,
                  decoration: InputDecoration(
                    labelText: 'الموقع',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    prefixIcon: const Icon(Icons.location_on),
                    filled: true,
                    fillColor:
                        Theme.of(context).colorScheme.surfaceContainerHighest,
                  ),
                  validator: (value) {
                    if (value != null && value.length > 100) {
                      return 'الموقع طويل جداً';
                    }
                    return null;
                  },
                ),
                // const SizedBox(height: 16),
                // DateTimeField(
                //   format: DateFormat('yyyy/MM/dd'),
                //   onShowPicker: (context, currentValue) {
                //     return showDatePicker(
                //       context: context,
                //       initialDate: currentValue ?? DateTime.now(),
                //       firstDate: DateTime(1900),
                //       lastDate: DateTime.now(),
                //     );
                //   },
                // ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: descriptionController,
                  maxLines: 3,
                  decoration: InputDecoration(
                    labelText: 'الوصف',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    prefixIcon: const Icon(Icons.description),
                    filled: true,
                    fillColor:
                        Theme.of(context).colorScheme.surfaceContainerHighest,
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'الرجاء إدخال الوصف';
                    }
                    if (value.length > 500) {
                      return 'الوصف طويل جداً';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<String>(
                  value: type,
                  decoration: InputDecoration(
                    labelText: 'نوع الخبرة',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    prefixIcon: const Icon(Icons.category),
                    filled: true,
                    fillColor:
                        Theme.of(context).colorScheme.surfaceContainerHighest,
                  ),
                  items: const [
                    DropdownMenuItem(value: 'job', child: Text('وظيفة')),
                    DropdownMenuItem(value: 'training', child: Text('تدريب')),
                    DropdownMenuItem(
                        value: 'volunteering', child: Text('تطوع')),
                  ],
                  onChanged: (value) {
                    if (value != null) {
                      setState(() => type = value);
                    }
                  },
                ),
                const SizedBox(height: 24),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    TextButton(
                      onPressed: () => Navigator.pop(context),
                      style: TextButton.styleFrom(
                        foregroundColor: Theme.of(context).colorScheme.error,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 24, vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: const Text('إلغاء'),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        if (_formKey.currentState!.validate()) {
                          if (isEditing) {
                            _controller.updateExperience(
                              id: experience.id,
                              title: titleController.text,
                              organization: organizationController.text,
                              location: locationController.text,
                              description: descriptionController.text,
                              type: type,
                              startDate: DateTime.now(),
                              endDate: null,
                              isCurrentPosition: false,
                            );
                          } else {
                            _controller.addExperience(
                              title: titleController.text,
                              organization: organizationController.text,
                              location: locationController.text,
                              description: descriptionController.text,
                              type: type,
                              startDate: DateTime.now(),
                              endDate: null,
                              isCurrentPosition: false,
                            );
                          }
                          Navigator.pop(context);
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        foregroundColor:
                            Theme.of(context).colorScheme.onPrimary,
                        backgroundColor: Theme.of(context).colorScheme.primary,
                        padding: const EdgeInsets.symmetric(
                            horizontal: 24, vertical: 12),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(8),
                        ),
                      ),
                      child: Text(isEditing ? 'تحديث' : 'إضافة'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _confirmDelete(Experience experience) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: Text('هل أنت متأكد من حذف خبرة "${experience.title}"؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            style: TextButton.styleFrom(
              foregroundColor: Theme.of(context).colorScheme.error,
            ),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      await _controller.deleteExperience(experience.id);
    }
  }
}

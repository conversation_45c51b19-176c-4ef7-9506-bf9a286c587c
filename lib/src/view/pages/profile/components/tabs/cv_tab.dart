import 'package:almashal/src/data/models/user.dart';
import 'package:almashal/src/data/services/auth_service.dart';
import 'package:almashal/src/view/components/buttons/custom_filled_button.dart';
import 'package:almashal/src/view/components/form_fields/custom_dropdwon_form_field.dart';
import 'package:almashal/src/view/components/form_fields/file_form_feild.dart';
import 'package:almashal/src/view/components/form_fields/image_form_feild.dart';
import 'package:almashal/src/view/pages/profile/widget/empty_state.dart';
import 'package:almashal/src/view/pages/profile/widget/loading_indicator.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../../../../controllers/profile/cv_controller.dart';
import '../../../../../data/models/profile/cv.dart';

class CVTab extends StatefulWidget {
  final bool isOwner;
  final String userId;
  final User? user;

  const CVTab({
    super.key,
    required this.isOwner,
    required this.userId,
    this.user,
  });

  @override
  State<CVTab> createState() => _CVTabState();
}

class _CVTabState extends State<CVTab> {
  late final CVController _controller;
  late final GlobalKey<FormState> _formKey;
  late final TextEditingController _titleController;
  // Add a state variable to track CV updates
  final _cvUpdateTrigger = ValueNotifier<int>(0);

  @override
  void initState() {
    super.initState();
    _controller = Get.find<CVController>();
    _formKey = GlobalKey<FormState>();
    _titleController = TextEditingController();
    _controller.setUserId(widget.userId);
    // _controller.loadCVs();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _cvUpdateTrigger.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(


      body: ValueListenableBuilder<int>(
        valueListenable: _cvUpdateTrigger,
        builder: (context, _, __) {
          return Obx(() {
            // Use Get.log instead of print for logging
            Get.log("CV Tab rebuilding, user: ${widget.user}");

            if (_controller.isLoading.value) {
              return const LoadingIndicator();
            }

            // Get the latest CV data from AuthService if this is the owner's profile
            final User? currentUser = widget.isOwner
                ? AuthService.instance.userData.value?.user
                : widget.user;

            if (currentUser?.cvType == null) {
              return EmptyState(
                message: 'لا توجد سير ذاتية مضافة',
                // onRefresh: _controller.loadCVs,
              );
            }

            return _buildCVCard(
              context,
              currentUser?.cvType,
              currentUser?.cvFile,
              currentUser?.cvText
            );
          });
        },
      ),
      floatingActionButton:( widget.isOwner && widget.user?.cvType == null)
          ? FloatingActionButton(
              onPressed: () => _pickAndUploadCV(context),
              tooltip: 'إضافة سيرة ذاتية',
              child: const Icon(Icons.add),

            )
          : null,
    );
  }

  Widget _buildCVCard(BuildContext context, String? type, String? cvFile, String? cvText) {
    return SingleChildScrollView(
      child: Padding(
        padding: const EdgeInsets.all(16.0),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with CV type and icon
            Row(
              children: [
                Icon(
                  _getFileIcon(type ?? ""),
                  size: 40,
                  color: Theme.of(context).primaryColor,
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        _getCVTypeTitle(type ?? ""),
                        style: const TextStyle(
                          fontWeight: FontWeight.bold,
                          fontSize: 18,
                        ),
                      ),
                      const SizedBox(height: 4),
                      Text(
                        'السيرة الذاتية',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 14,
                        ),
                      ),
                    ],
                  ),
                ),
                if (widget.isOwner)
                  IconButton(
                    icon: const Icon(Icons.edit),
                    onPressed: () => _pickAndUploadCV(context),
                    tooltip: 'تعديل السيرة الذاتية',
                  ),
              ],
            ),

            const SizedBox(height: 24),

            // CV Content based on type
            _buildCVContent(context, type, cvFile, cvText),

            // Actions
            if (cvFile != null && type != 'text')
              Padding(
                padding: const EdgeInsets.only(top: 24.0, bottom: 16.0),
                child: Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CustomFilledButton(
                      onTap: () async {
                        // Check if fileUrl is available
                        final fileUrl = widget.user?.cvFileUrl;
                        if (fileUrl == null || fileUrl.isEmpty) {
                          Get.snackbar(
                            'خطأ',
                            'رابط الملف غير متوفر',
                            snackPosition: SnackPosition.BOTTOM,
                            backgroundColor: Colors.red,
                            colorText: Colors.white,
                          );
                          return;
                        }

                        // Create a CV object to pass to the download method
                        final cv = CV(
                          id: '0',
                          title: 'السيرة الذاتية',
                          fileUrl: fileUrl,
                          fileType: type ?? '',
                          isDefault: true,
                          isPublic: true,
                          createdAt: DateTime.now(),
                          updatedAt: DateTime.now(),
                        );

                        try {
                          await _controller.downloadCV(cv);
                        } catch (e) {
                          // Error is already handled in the controller and service
                          Get.log('Error opening CV in browser: $e');
                        }
                      },
                      label: 'عرض السيرة الذاتية',
                    ),
                  ],
                ),
              ),
          ],
        ),
      ),
    );
  }

  String _getCVTypeTitle(String type) {
    switch (type.toLowerCase()) {
      case 'pdf':
        return 'ملف PDF';
      case 'image':
        return 'صورة السيرة الذاتية';
      case 'text':
        return 'نص السيرة الذاتية';
      default:
        return 'السيرة الذاتية';
    }
  }

  Widget _buildCVContent(BuildContext context, String? type, String? cvFile, String? cvText) {
    switch (type?.toLowerCase()) {
      case 'pdf':
        return _buildFilePreview(context, cvFile, 'PDF');
      case 'image':
        return _buildImagePreview(context, cvFile);
      case 'text':
        return _buildTextPreview(context, cvText);
      default:
        return const Center(
          child: Text(
            'نوع السيرة الذاتية غير معروف',
            style: TextStyle(
              color: Colors.grey,
              fontSize: 16,
            ),
          ),
        );
    }
  }

  Widget _buildFilePreview(BuildContext context, String? filePath, String fileType) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withAlpha(76),
        ),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            _getFileIcon(fileType),
            size: 80,
            color: Theme.of(context).primaryColor,
          ),
          const SizedBox(height: 16),
          Text(
            'ملف $fileType',
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              fontSize: 18,
            ),
          ),
          if (filePath != null) ...[
            const SizedBox(height: 8),
            Text(
              filePath.split('/').last,
              style: TextStyle(
                color: Colors.grey[600],
                fontSize: 14,
              ),
              textAlign: TextAlign.center,
            ),
          ],
          const SizedBox(height: 16),
          Text(
            'اضغط على زر التحميل لعرض الملف',
            style: TextStyle(
              color: Colors.grey[600],
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildImagePreview(BuildContext context, String? imagePath) {
    if (imagePath == null) {
      return _buildFilePreview(context, null, 'صورة');
    }

    final imageUrl = widget.user?.cvFileUrl;

    return Container(
      width: double.infinity,
      // Use a more responsive height that adapts to screen size
      constraints: BoxConstraints(
        maxHeight: MediaQuery.of(context).size.height * 0.4, // 40% of screen height
        minHeight: 200, // Minimum height
      ),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withAlpha(76),
        ),
      ),
      child: imageUrl != null
          ? ClipRRect(
              borderRadius: BorderRadius.circular(12),
              child: Image.network(
                imageUrl,
                fit: BoxFit.contain,
                loadingBuilder: (context, child, loadingProgress) {
                  if (loadingProgress == null) return child;
                  return Center(
                    child: CircularProgressIndicator(
                      value: loadingProgress.expectedTotalBytes != null
                          ? loadingProgress.cumulativeBytesLoaded /
                              loadingProgress.expectedTotalBytes!
                          : null,
                    ),
                  );
                },
                errorBuilder: (context, error, stackTrace) {
                  return Center(
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        const Icon(
                          Icons.error_outline,
                          color: Colors.red,
                          size: 48,
                        ),
                        const SizedBox(height: 16),
                        Text(
                          'تعذر تحميل الصورة',
                          style: TextStyle(
                            color: Colors.grey[600],
                          ),
                        ),
                      ],
                    ),
                  );
                },
              ),
            )
          : Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.image,
                    size: 80,
                    color: Theme.of(context).primaryColor,
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'لا توجد صورة متاحة',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  Widget _buildTextPreview(BuildContext context, String? text) {
    return Container(
      width: double.infinity,
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: Theme.of(context).colorScheme.surfaceContainerHighest,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Theme.of(context).colorScheme.outline.withAlpha(76),
        ),
      ),
      child: text != null && text.isNotEmpty
          ? Text(
              text,
              style: const TextStyle(
                fontSize: 16,
                height: 1.6,
              ),
              textAlign: TextAlign.right,
              textDirection: TextDirection.rtl,
            )
          : Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  Icon(
                    Icons.text_format,
                    size: 80,
                    color: Theme.of(context).primaryColor,
                  ),
                  const SizedBox(height: 16),
                  const Text(
                    'لا يوجد نص متاح',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      fontSize: 18,
                    ),
                  ),
                ],
              ),
            ),
    );
  }

  IconData _getFileIcon(String fileType) {
    switch (fileType.toLowerCase()) {
      case 'pdf':
        return Icons.picture_as_pdf;
      case 'image':

        return Icons.image;

         case 'text':

        return Icons.text_format;
      default:
        return Icons.insert_drive_file;
    }
  }

  Future<void> _pickAndUploadCV(BuildContext context) async {
    _showUploadDialog(context);

    // final result = await FilePicker.platform.pickFiles(
    //   type: FileType.custom,
    //   allowedExtensions: ['pdf', 'doc', 'docx'],
    // );

    // if (result != null && result.files.isNotEmpty) {
    //   final file = result.files.first;
    //   _showUploadDialog(context, file);
    // }
  }

  void _showUploadDialog(
    BuildContext context,
  ) {
    _titleController.clear();

    showDialog(
        context: context,
        builder: (context) {
          String? type;
          String? file;
          // No need for the text variable as we're using _titleController directly

          return AlertDialog(
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(16),
            ),
            title: const Text('رفع سيرة ذاتية جديدة'),
            content: Form(
              key: _formKey,
              child: StatefulBuilder(
                builder: (context, setState) =>
                    Column(mainAxisSize: MainAxisSize.min, children: [
                  CustomDropdownFormField(
                    label: Text(
                      'اختر نوع السيرة الذاتية',
                      style: TextStyle(
                        color: Theme.of(context).colorScheme.onSurface,
                      ),
                    ),
                    items: [
                      DropdownMenuItem(
                        value: 'image',
                        child: Text('صورة'),
                      ),
                      DropdownMenuItem(
                        value: 'pdf',
                        child: Text('PDF'),
                      ),
                      DropdownMenuItem(
                        value: 'text',
                        child: Text('نص'),
                      ),
                    ],
                    onChanged: (value) {
                      setState(() {
                        type = value;
                      });
                    },
                    validator: (value) {
                      if (value == null) {
                        return 'يرجى اختيار نوع السيرة الذاتية';
                      }
                      return null;
                    },
                  ),


                  const SizedBox(height: 16),
                  Column(
                    children: [
                      if (type == "text")
                        TextFormField(
                          controller: _titleController,
                          minLines: 5,
                          maxLines: 5,
                          decoration: InputDecoration(
                            labelText: 'السيرة الذاتية',
                            hintText: 'السيرة الذاتية',
                            border: OutlineInputBorder(
                              borderRadius: BorderRadius.circular(8),
                            ),
                            prefixIcon: const Icon(Icons.title),
                            filled: true,
                            fillColor: Theme.of(context)
                                .colorScheme
                                .surfaceContainerHighest,
                          ),
                          validator: (value) {
                            if (value == null || value.isEmpty) {
                              return 'يرجى إدخال  السيرة الذاتية';
                            }

                            return null;
                          },
                        ),
                      if (type == "pdf")
                        FileFormField(
                          onSaved: (value, fileType) {
                            if (fileType == FileFieldType.file) {
                               file = value;


                            }
                          },
                        ),
                      if (type == "image") ImageFormField(
                        onSaved: (value, fileType) {
                          if (fileType == ImageType.file) {
                             file = value;


                          }
                        },
                      )


                    ],
                  ),
                ]),
              ),
            ),
            actions: [
              TextButton(
                onPressed: () => Navigator.pop(context),
                style: TextButton.styleFrom(
                  foregroundColor: Theme.of(context).colorScheme.error,
                  padding:
                      const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(8),
                  ),
                ),
                child: const Text('إلغاء'),
              ),
              CustomFilledButton(
                onTap: () async {
                  if (_formKey.currentState!.validate()) {
                    _formKey.currentState!.save();

                    Get.log("type $type file $file");

                    final success = await _controller.uploadCV(
                      type: type ?? "",
                      file: file,
                      text: type == "text" ? _titleController.text : null,
                    );

                    if (success && context.mounted) {
                      // No need to manually update widget.user as we're now using AuthService directly
                      // Increment the update trigger to force a UI rebuild
                      _cvUpdateTrigger.value += 1;

                      // Close the dialog
                      Navigator.pop(context);

                      // Show a success message
                      Get.snackbar(
                        'تم بنجاح',
                        'تم تحديث السيرة الذاتية بنجاح',
                        snackPosition: SnackPosition.BOTTOM,
                        backgroundColor: Colors.green,
                        colorText: Colors.white,
                        duration: const Duration(seconds: 2),
                      );
                    }
                  }
                },
                // style: ElevatedButton.styleFrom(
                //   foregroundColor: Theme.of(context).colorScheme.onPrimary,
                //   backgroundColor: Theme.of(context).colorScheme.primary,
                //   padding:
                //       const EdgeInsets.symmetric(horizontal: 24, vertical: 12),
                //   shape: RoundedRectangleBorder(
                //     borderRadius: BorderRadius.circular(8),
                //   ),
                // ),
                label: ('رفع'),
              ),



            ],
          );
        });
  }

  // These methods are no longer used in the new implementation
  // but kept for reference in case they need to be reintroduced
  /*
  void _showPrivacyDialog(BuildContext context, CV cv) {
    // Implementation removed
  }

  void _showDeleteConfirmation(BuildContext context, CV cv) {
    // Implementation removed
  }
  */
}

import 'package:flutter/material.dart';
import 'package:font_awesome_flutter/font_awesome_flutter.dart';
import 'package:almashal/src/data/models/user.dart';
import 'package:url_launcher/url_launcher.dart';

class BasicInfoTab extends StatelessWidget {
  final bool isOwner;
  final User? user;

  const BasicInfoTab({
    super.key,
    required this.isOwner,
    this.user,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionCard(
            context: context,
            title: 'نبذة عني',
            content: user?.overview ?? 'لا توجد نبذة',
            icon: FontAwesomeIcons.user,
            iconColor: Colors.blue,
          ),
          const SizedBox(height: 16),
          _buildSectionCard(
            context: context,
            title: 'معلومات التواصل',
            icon: FontAwesomeIcons.phone,
            iconColor: Colors.green,
            child: Column(
              children: [
                _buildContactTile(
                  context: context,
                  icon: FontAwesomeIcons.mobile,
                  title: 'رقم الجوال',
                  subtitle: user?.mobile ?? '',
                  iconColor: Colors.green,
                ),
                _buildContactTile(
                  context: context,
                  icon: FontAwesomeIcons.envelope,
                  title: 'البريد الإلكتروني',
                  subtitle: user?.email ?? '',
                  iconColor: Colors.blue,
                ),
                if (user?.address != null && user?.address != user?.overview)
                  _buildContactTile(
                    context: context,
                    icon: FontAwesomeIcons.locationDot,
                    title: 'العنوان',
                    subtitle: user?.address ?? '',
                    iconColor: Colors.orange,
                  ),
              ],
            ),
          ),
          const SizedBox(height: 16),
          _buildSectionCard(
            context: context,
            title: 'روابط التواصل الاجتماعي',
            icon: FontAwesomeIcons.shareNodes,
            iconColor: const Color.fromARGB(255, 167, 64, 185),
            child: _buildSocialLinks(context),
          ),
        ],
      ),
    );
  }

  Widget _buildSectionCard({
    required BuildContext context,
    required String title,
    String? content,
    required IconData icon,
    Color? iconColor,
    Widget? child,
  }) {
    return Card(
      elevation: 0,
      shape: RoundedRectangleBorder(
        borderRadius: BorderRadius.circular(12),
        side: BorderSide(
          color: Colors.brown[200]!, // تغيير لون الحدود إلى بني
          width: 1,
        ),
      ),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                FaIcon(icon,
                    color: iconColor ?? Theme.of(context).primaryColor),
                const SizedBox(width: 12),
                Text(
                  title,
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                ),
              ],
            ),
            if (content != null) ...[
              const SizedBox(height: 16),
              Text(
                content,
                style: Theme.of(context).textTheme.bodyMedium,
              ),
            ],
            if (child != null) ...[
              const SizedBox(height: 16),
              child,
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildContactTile({
    required BuildContext context,
    required IconData icon,
    required String title,
    required String subtitle,
    Color? iconColor,
  }) {
    return ListTile(
      leading: FaIcon(icon, color: iconColor),
      title: Text(title),
      subtitle: Text(
        subtitle.isNotEmpty ? subtitle : 'غير متوفر',
        style: const TextStyle(fontSize: 14),
      ),
      contentPadding: EdgeInsets.zero,
      minLeadingWidth: 24,
    );
  }

  Widget _buildSocialLinks(BuildContext context) {
    final Map<String, Map<String, dynamic>> socialInfo = {
      'facebook_link': {
        'icon': FontAwesomeIcons.facebookF,
        'name': 'فيسبوك',
        'color': const Color(0xFF1877F2),
        'value': user?.facebookLink,
      },
      'x_link': {
        'icon': FontAwesomeIcons.xTwitter,
        'name': 'تويتر (X)',
        'color': Colors.black,
        'value': user?.xLink,
      },
      'instagram_link': {
        'icon': FontAwesomeIcons.instagram,
        'name': 'انستغرام',
        'color': const Color(0xFFE4405F),
        'value': user?.instagramLink,
      },
      'linkedin_link': {
        'icon': FontAwesomeIcons.linkedinIn,
        'name': 'لينكد إن',
        'color': const Color(0xFF0A66C2),
        'value': user?.linkedinLink,
      },
      'youtube_link': {
        'icon': FontAwesomeIcons.youtube,
        'name': 'يوتيوب',
        'color': const Color(0xFFFF0000),
        'value': user?.youtubeLink,
      },
      'snapshot_link': {
        'icon': FontAwesomeIcons.snapchat,
        'name': 'سناب شات',
        'color': const Color(0xFFFFFC00),
        'value': user?.snapshotLink,
      },
      'tiktok_link': {
        'icon': FontAwesomeIcons.tiktok,
        'name': 'تيك توك',
        'color': Colors.black,
        'value': user?.tiktokLink,
      },
    };

    socialInfo.removeWhere((key, value) => value['value'] == null);

    if (socialInfo.isEmpty) {
      return Center(
        child: Text(
          'لا توجد روابط تواصل اجتماعي',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Theme.of(context).disabledColor,
              ),
        ),
      );
    }

    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 5,
      mainAxisSpacing: 16,
      crossAxisSpacing: 16,
      childAspectRatio: 1,
      children: socialInfo.entries.map((entry) {
        if (entry.value['value'] == null) return const SizedBox.shrink();

        final info = socialInfo[entry.key] ??
            {
              'icon': FontAwesomeIcons.link,
              'name': entry.key,
              'color': Theme.of(context).primaryColor,
            };

        return _buildSocialIcon(
          icon: info['icon'],
          name: info['name'],
          color: info['color'],
          url: info['value'],
        );
      }).toList(),
    );
  }

  Widget _buildSocialIcon({
    required IconData icon,
    required String name,
    required Color color,
    required String url,
  }) {
    return InkWell(
      borderRadius: BorderRadius.circular(12),
      onTap: () => _launchURL(url),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Container(
            padding: EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: color.withOpacity(0.1),
              shape: BoxShape.circle,
              border: Border.all(
                color: color.withOpacity(0.3),
                width: 1,
              ),
            ),
            child: Center(
              child: FaIcon(
                icon,
                color: color,
                size: 24,
              ),
            ),
          ),
          // const SizedBox(height: 8),
          // Text(
          //   name,
          //   style: const TextStyle(fontSize: 12),
          //   textAlign: TextAlign.center,
          //   maxLines: 1,
          //   overflow: TextOverflow.ellipsis,
          // ),
        ],
      ),
    );
  }

  Future<void> _launchURL(String url) async {
    if (url.isEmpty) return;

    final Uri uri = Uri.parse(url.startsWith('http') ? url : 'https://$url');

    if (await canLaunchUrl(uri)) {
      await launchUrl(
        uri,
        mode: LaunchMode.externalApplication,
      );
    } else {
      debugPrint('Could not launch $url');
    }
  }
}

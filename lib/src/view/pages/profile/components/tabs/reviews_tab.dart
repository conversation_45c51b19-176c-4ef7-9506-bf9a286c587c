import 'package:almashal/src/controllers/profile/reviews_controller.dart';
import 'package:almashal/src/data/models/profile/review.dart';
import 'package:almashal/src/view/components/image/custom_cached_network_image.dart';
import 'package:almashal/src/view/components/shared/index.dart';
import 'package:almashal/src/data/services/auth_service.dart';
import 'package:almashal/src/core/theme/app_colors.dart';
import 'package:almashal/src/core/theme/app_dimensions.dart';
import 'package:flutter/material.dart';
import 'package:flutter_spinkit/flutter_spinkit.dart';
import 'package:get/get.dart';

class ReviewsTab extends StatefulWidget {
  const ReviewsTab({
    super.key,
    required this.userId,
  });

  final String userId;

  @override
  State<ReviewsTab> createState() => _ReviewsTabState();
}

class _ReviewsTabState extends State<ReviewsTab> {
  late final ReviewsController _controller;
  final _formKey = GlobalKey<FormState>();
  final _commentController = TextEditingController();
  double _rating = 3.0;

  @override
  void initState() {
    super.initState();
    // استخدام Put.lazyPut لتسجيل وحدة التحكم بشكل كسول
    _controller = Get.put(ReviewsController());
    _controller.setUserId(widget.userId);
  }

  @override
  void dispose() {
    _commentController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: RefreshIndicator(
        onRefresh: _controller.loadReviews,
        child: Obx(() {
          if (_controller.isLoading.value) {
            return const LoadingState(
              message: 'جاري تحميل التقييمات...',
              type: LoadingIndicatorType.threeBounce,
            );
          }

          if (_controller.reviewsList.isEmpty) {
            return EmptyState(
              message: 'لا توجد تقييمات',
              description: 'لم يقم أحد بتقييم هذا المستخدم بعد',
              icon: Icons.rate_review,
              onRefresh: _controller.loadReviews,
            );
          }

          return ListView.builder(
            padding: EdgeInsets.all(AppDimensions.medium),
            itemCount: _controller.reviewsList.length + (_controller.canLoadMore() ? 1 : 0),
      itemBuilder: (context, index) {
              // التحقق من إمكانية تحميل المزيد من البيانات
              if (index == _controller.reviewsList.length) {
                _controller.loadMoreReviews();
                return Padding(
                  padding: EdgeInsets.symmetric(vertical: AppDimensions.medium),
                  child: Center(
                    child: SpinKitThreeBounce(
                      color: AppColors.primary,
                      size: 20,
                    ),
                  ),
                );
              }
              return _buildReviewCard(_controller.reviewsList[index]);
            },
          );
        }),
      ),
      // زر إضافة مراجعة (فقط للمستخدمين المسجلين ومختلفين عن صاحب الملف)
      floatingActionButton: AuthService.instance.isAuthenticated() &&
              AuthService.instance.userData.value!.user.id.toString() != widget.userId
          ? FloatingActionButton(
              onPressed: () => _showAddReviewDialog(),
              tooltip: 'إضافة تقييم',
              backgroundColor: AppColors.primary,
              child: const Icon(Icons.rate_review),
            )
          : null,
    );
  }

  Widget _buildReviewCard(Review review) {
    return CustomCard(
      margin: EdgeInsets.only(bottom: AppDimensions.medium),
      title: review.reviewer.displayName,
      subtitle: review.createdAt,
      titleIcon: Icons.person,
      child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
          // صورة المستخدم والتقييم بالنجوم
            Row(
              children: [
              ClipRRect(
                borderRadius: BorderRadius.circular(AppDimensions.borderRadiusCircular),
                child: CustomCachedNetworkImage(
                  imageUrl: review.reviewer.image ?? "",
                  width: AppDimensions.avatarSizeMedium,
                  height: AppDimensions.avatarSizeMedium,
                ),
              ),
              SizedBox(width: AppDimensions.medium),
                      Row(
                        children: List.generate(
                          5,
                          (starIndex) => Icon(
                    starIndex < review.rating
                                ? Icons.star
                                : Icons.star_border,
                            color: Colors.amber,
                            size: 16,
                          ),
                        ),
                      ),
                    ],
                  ),
          SizedBox(height: AppDimensions.medium),
          // نص التقييم
          Text(
            review.comment,
            style: TextStyle(
              color: AppColors.textPrimary,
              fontSize: AppDimensions.fontSizeRegular,
            ),
          ),
        ],
      ),
    );
  }

  // نافذة إضافة مراجعة جديدة
  Future<void> _showAddReviewDialog() async {
    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(AppDimensions.borderRadiusLarge),
        ),
        child: SingleChildScrollView(
          padding: EdgeInsets.all(AppDimensions.large),
          child: Form(
            key: _formKey,
            autovalidateMode: AutovalidateMode.onUserInteraction,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  'إضافة تقييم',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: AppColors.primary,
                      ),
                ),
                SizedBox(height: AppDimensions.large),
                
                // مكون التقييم بالنجوم
                Directionality(
                  textDirection: TextDirection.ltr,
                  child: Slider(
                    value: _rating,
                    onChanged: (value) {
                      setState(() {
                        _rating = value;
                      });
                    },
                    min: 1,
                    max: 5,
                    divisions: 4,
                    label: _rating.round().toString(),
                    activeColor: AppColors.primary,
                  ),
                ),
                
                // عرض النجوم المرئية
                Row(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: List.generate(5, (index) {
                    return IconButton(
                      icon: Icon(
                        index < _rating.round() ? Icons.star : Icons.star_border,
                        color: Colors.amber,
                      ),
                      onPressed: () {
                        setState(() {
                          _rating = (index + 1).toDouble();
                        });
                      },
                    );
                  }),
                ),
                
                SizedBox(height: AppDimensions.medium),
                
                // حقل نص التقييم
                TextFormField(
                  controller: _commentController,
                  maxLines: 3,
                  decoration: InputDecoration(
                    labelText: 'نص التقييم',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(AppDimensions.borderRadiusMedium),
                    ),
                    filled: true,
                    fillColor: AppColors.surfaceLight,
                    enabledBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(AppDimensions.borderRadiusMedium),
                      borderSide: BorderSide(color: AppColors.primary.withOpacity(0.5)),
                    ),
                    focusedBorder: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(AppDimensions.borderRadiusMedium),
                      borderSide: BorderSide(color: AppColors.primary, width: 2),
                    ),
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'الرجاء إدخال نص التقييم';
                    }
                    return null;
                  },
                ),
                
                SizedBox(height: AppDimensions.large),
                
                // أزرار الإلغاء والحفظ
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                  children: [
                    TextButton(
                      onPressed: () {
                        Navigator.of(context).pop();
                      },
                      style: TextButton.styleFrom(
                        foregroundColor: AppColors.textSecondary,
                        padding: EdgeInsets.symmetric(
                          horizontal: AppDimensions.medium,
                          vertical: AppDimensions.small,
                        ),
                      ),
                      child: const Text('إلغاء'),
                    ),
                    ElevatedButton(
                      onPressed: () {
                        if (_formKey.currentState?.validate() ?? false) {
                          // إرسال التقييم
                          _controller.addReview({
                            'user_id': widget.userId,
                            'rating': _rating.round(),
                            'comment': _commentController.text,
                          });
                          Navigator.of(context).pop();
                        }
                      },
                      style: ElevatedButton.styleFrom(
                        backgroundColor: AppColors.primary,
                        foregroundColor: Colors.white,
                        padding: EdgeInsets.symmetric(
                          horizontal: AppDimensions.medium,
                          vertical: AppDimensions.small,
                        ),
                      ),
                      child: const Text('حفظ التقييم'),
                    ),
                  ],
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }
}

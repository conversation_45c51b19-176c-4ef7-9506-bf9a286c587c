import 'dart:io';
import 'package:almashal/src/view/pages/profile/widget/empty_state.dart';
import 'package:almashal/src/view/pages/profile/widget/loading_indicator.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:intl/intl.dart';
import 'package:file_picker/file_picker.dart';
import 'package:almashal/src/controllers/profile/achievements_controller.dart';
import 'package:almashal/src/data/models/profile/achievement.dart';

class AchievementsTab extends StatefulWidget {
  final bool isOwner;
  final String userId;

  const AchievementsTab({
    super.key,
    required this.isOwner,
    required this.userId,
  });

  @override
  State<AchievementsTab> createState() => _AchievementsTabState();
}

class _AchievementsTabState extends State<AchievementsTab> {
  late final AchievementsController _controller;
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _typeController = TextEditingController();
  DateTime? _selectedDate;
  File? _certificateFile;
  File? _imageFile;

  @override
  void initState() {
    super.initState();
    _controller = Get.find<AchievementsController>();
    _controller.setUserId(widget.userId);
    // _controller.loadAchievements();
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _typeController.dispose();
    super.dispose();
  }

  Future<void> _pickFile(String type) async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: type == 'certificate' ? FileType.any : FileType.image,
        allowMultiple: false,
      );

      if (result != null) {
        final file = File(result.files.single.path!);
        setState(() {
          if (type == 'certificate') {
            _certificateFile = file;
          } else {
            _imageFile = file;
          }
        });
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء اختيار الملف',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }

  Future<void> _showAddEditDialog([Achievement? achievement]) async {
    if (achievement != null) {
      _titleController.text = achievement.title;
      _descriptionController.text = achievement.description;
      _typeController.text = achievement.type;
      _selectedDate = achievement.date;
    } else {
      _titleController.clear();
      _descriptionController.clear();
      _typeController.clear();
      _selectedDate = null;
      _certificateFile = null;
      _imageFile = null;
    }

    await showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => Dialog(
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(16),
        ),
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Form(
            key: _formKey,
            autovalidateMode: AutovalidateMode.onUserInteraction,
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                Text(
                  achievement != null ? 'تعديل إنجاز' : 'إضافة إنجاز',
                  style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                        fontWeight: FontWeight.bold,
                        color: Theme.of(context).colorScheme.primary,
                      ),
                ),
                const SizedBox(height: 24),
                TextFormField(
                  controller: _titleController,
                  decoration: InputDecoration(
                    labelText: 'العنوان',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    prefixIcon: const Icon(Icons.title),
                    filled: true,
                    fillColor:
                        Theme.of(context).colorScheme.surfaceContainerHighest,
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'الرجاء إدخال العنوان';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                TextFormField(
                  controller: _descriptionController,
                  maxLines: 3,
                  decoration: InputDecoration(
                    labelText: 'الوصف',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    prefixIcon: const Icon(Icons.description),
                    filled: true,
                    fillColor:
                        Theme.of(context).colorScheme.surfaceContainerHighest,
                  ),
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'الرجاء إدخال الوصف';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                DropdownButtonFormField<String>(
                  value: _typeController.text.isEmpty
                      ? null
                      : _typeController.text,
                  decoration: InputDecoration(
                    labelText: 'النوع',
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                    prefixIcon: const Icon(Icons.category),
                    filled: true,
                    fillColor:
                        Theme.of(context).colorScheme.surfaceContainerHighest,
                  ),
                  items: const [
                    DropdownMenuItem(value: 'شهادة', child: Text('شهادة')),
                    DropdownMenuItem(value: 'جائزة', child: Text('جائزة')),
                    DropdownMenuItem(
                        value: 'إنجاز شخصي', child: Text('إنجاز شخصي')),
                  ],
                  onChanged: (value) {
                    if (value != null) {
                      _typeController.text = value;
                    }
                  },
                  validator: (value) {
                    if (value == null || value.isEmpty) {
                      return 'الرجاء اختيار النوع';
                    }
                    return null;
                  },
                ),
                const SizedBox(height: 16),
                InkWell(
                  onTap: () async {
                    final date = await showDatePicker(
                      context: context,
                      initialDate: _selectedDate ?? DateTime.now(),
                      firstDate: DateTime(1900),
                      lastDate: DateTime.now(),
                    );
                    if (date != null) {
                      setState(() => _selectedDate = date);
                    }
                  },
                  child: InputDecorator(
                    decoration: InputDecoration(
                      labelText: 'التاريخ',
                      border: OutlineInputBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                      prefixIcon: const Icon(Icons.calendar_today),
                      filled: true,
                      fillColor:
                          Theme.of(context).colorScheme.surfaceContainerHighest,
                    ),
                    child: Text(
                      _selectedDate != null
                          ? DateFormat('yyyy/MM/dd').format(_selectedDate!)
                          : 'اختر التاريخ',
                    ),
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => _pickFile('certificate'),
                        icon: const Icon(Icons.upload_file),
                        label: const Text('رفع شهادة'),
                        style: ElevatedButton.styleFrom(
                          foregroundColor:
                              Theme.of(context).colorScheme.onPrimary,
                          backgroundColor:
                              Theme.of(context).colorScheme.primary,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ),
                    if (_certificateFile != null) ...[
                      const SizedBox(width: 8),
                      IconButton(
                        onPressed: () =>
                            setState(() => _certificateFile = null),
                        icon: const Icon(Icons.clear),
                      ),
                    ],
                  ],
                ),
                if (_certificateFile != null)
                  Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: Text(
                      'تم اختيار: ${_certificateFile!.path.split('/').last}',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: ElevatedButton.icon(
                        onPressed: () => _pickFile('image'),
                        icon: const Icon(Icons.image),
                        label: const Text('رفع صورة'),
                        style: ElevatedButton.styleFrom(
                          foregroundColor:
                              Theme.of(context).colorScheme.onPrimary,
                          backgroundColor:
                              Theme.of(context).colorScheme.primary,
                          padding: const EdgeInsets.symmetric(vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                      ),
                    ),
                    if (_imageFile != null) ...[
                      const SizedBox(width: 8),
                      IconButton(
                        onPressed: () => setState(() => _imageFile = null),
                        icon: const Icon(Icons.clear),
                      ),
                    ],
                  ],
                ),
                if (_imageFile != null)
                  Padding(
                    padding: const EdgeInsets.only(top: 8),
                    child: Text(
                      'تم اختيار: ${_imageFile!.path.split('/').last}',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ),
                const SizedBox(height: 24),
                Obx(() {
                  if (_controller.isUploading.value) {
                    return Column(
                      children: [
                        LinearProgressIndicator(
                          value: _controller.uploadProgress.value,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'جاري الرفع... ${(_controller.uploadProgress.value * 100).toStringAsFixed(0)}%',
                          style: Theme.of(context).textTheme.bodySmall,
                        ),
                      ],
                    );
                  }
                  return Row(
                    mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                    children: [
                      TextButton(
                        onPressed: () => Navigator.pop(context),
                        style: TextButton.styleFrom(
                          foregroundColor: Theme.of(context).colorScheme.error,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 24, vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: const Text('إلغاء'),
                      ),
                      ElevatedButton(
                        onPressed: () async {
                          if (_formKey.currentState!.validate()) {
                            if (_selectedDate == null) {
                              Get.snackbar(
                                'تنبيه',
                                'الرجاء اختيار التاريخ',
                                snackPosition: SnackPosition.BOTTOM,
                              );
                              return;
                            }

                            final success = achievement != null
                                ? await _controller.updateAchievement(
                                    id: achievement.id,
                                    title: _titleController.text,
                                    description: _descriptionController.text,
                                    type: _typeController.text,
                                    date: _selectedDate,
                                    certificateFile: _certificateFile,
                                    imageFile: _imageFile,
                                  )
                                : await _controller.addAchievement(
                                    title: _titleController.text,
                                    description: _descriptionController.text,
                                    type: _typeController.text,
                                    date: _selectedDate!,
                                    certificateFile: _certificateFile,
                                    imageFile: _imageFile,
                                  );

                            if (success && mounted) {
                              Navigator.pop(context);
                            }
                          }
                        },
                        style: ElevatedButton.styleFrom(
                          foregroundColor:
                              Theme.of(context).colorScheme.onPrimary,
                          backgroundColor:
                              Theme.of(context).colorScheme.primary,
                          padding: const EdgeInsets.symmetric(
                              horizontal: 24, vertical: 12),
                          shape: RoundedRectangleBorder(
                            borderRadius: BorderRadius.circular(8),
                          ),
                        ),
                        child: Text(achievement != null ? 'تحديث' : 'إضافة'),
                      ),
                    ],
                  );
                }),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _confirmDelete(Achievement achievement) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('تأكيد الحذف'),
        content: const Text('هل أنت متأكد من حذف هذا الإنجاز؟'),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: const Text('إلغاء'),
          ),
          TextButton(
            onPressed: () => Navigator.pop(context, true),
            child: const Text('حذف'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final success = await _controller.deleteAchievement(achievement.id);
      if (success) {
        _controller.loadAchievements();
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: RefreshIndicator(
        onRefresh: _controller.loadAchievements,
        child: Obx(() {
          if (_controller.isLoading.value) {
            return const LoadingIndicator();
          }

          if (_controller.achievementsList.isEmpty) {
            return EmptyState(
              message: 'لا توجد إنجازات مضافة',
              onRefresh: _controller.loadAchievements,
            );
          }

          return ListView.builder(
            padding: const EdgeInsets.all(16),
            itemCount: _controller.achievementsList.length,
            itemBuilder: (context, index) {
              final achievement = _controller.achievementsList[index];
              return Card(
                margin: const EdgeInsets.only(bottom: 16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.stretch,
                  children: [
                    if (achievement.imageUrl != null)
                      Image.network(
                        achievement.imageUrl!,
                        height: 200,
                        fit: BoxFit.cover,
                        errorBuilder: (context, error, stackTrace) {
                          return const SizedBox(
                            height: 200,
                            child: Center(
                              child: Icon(Icons.error_outline),
                            ),
                          );
                        },
                      ),
                    Padding(
                      padding: const EdgeInsets.all(16),
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Row(
                            children: [
                              Expanded(
                                child: Text(
                                  achievement.title,
                                  style: Theme.of(context).textTheme.titleLarge,
                                ),
                              ),
                              if (widget.isOwner)
                                PopupMenuButton<String>(
                                  onSelected: (value) {
                                    switch (value) {
                                      case 'edit':
                                        _showAddEditDialog(achievement);
                                        break;
                                      case 'delete':
                                        _confirmDelete(achievement);
                                        break;
                                    }
                                  },
                                  itemBuilder: (context) => [
                                    const PopupMenuItem(
                                      value: 'edit',
                                      child: Text('تعديل'),
                                    ),
                                    const PopupMenuItem(
                                      value: 'delete',
                                      child: Text('حذف'),
                                    ),
                                  ],
                                ),
                            ],
                          ),
                          const SizedBox(height: 8),
                          Text(achievement.description),
                          const SizedBox(height: 8),
                          Wrap(
                            spacing: 8,
                            children: [
                              Chip(
                                label: Text(achievement.type),
                                backgroundColor: Theme.of(context)
                                    .primaryColor
                                    .withOpacity(0.1),
                              ),
                              Chip(
                                label: Text(
                                  DateFormat('yyyy/MM/dd')
                                      .format(achievement.date),
                                ),
                                backgroundColor: Theme.of(context)
                                    .primaryColor
                                    .withOpacity(0.1),
                              ),
                            ],
                          ),
                          if (achievement.certificateUrl != null)
                            Padding(
                              padding: const EdgeInsets.only(top: 8),
                              child: ElevatedButton.icon(
                                onPressed: () => _controller
                                    .downloadCertificate(achievement),
                                icon: const Icon(Icons.download),
                                label: const Text('تحميل الشهادة'),
                              ),
                            ),
                        ],
                      ),
                    ),
                  ],
                ),
              );
            },
          );
        }),
      ),
      floatingActionButton: widget.isOwner
          ? FloatingActionButton(
              onPressed: () => _showAddEditDialog(),
              tooltip: 'إضافة إنجاز',
              child: const Icon(Icons.add),
            )
          : null,
    );
  }
}

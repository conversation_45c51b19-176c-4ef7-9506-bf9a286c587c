// import 'package:flutter/material.dart';
// import '../../../../core/theme/profile_theme.dart';
// import 'package:feather_icons/feather_icons.dart';

// class QuickInfoBar extends StatelessWidget {
//   final int achievementsCount;
//   final String education;
//   final String currentJob;
//   final String familyBranch;

//   const QuickInfoBar({
//     Key? key,
//     required this.achievementsCount,
//     required this.education,
//     required this.currentJob,
//     required this.familyBranch,
//   }) : super(key: key);

//   @override
//   Widget build(BuildContext context) {
//     return Container(
//       margin: const EdgeInsets.fromLTRB(16, 60, 16, 16),
//       padding: const EdgeInsets.all(16),
//       decoration: ProfileTheme.cardDecoration,
//       child: Row(
//         mainAxisAlignment: MainAxisAlignment.spaceAround,
//         children: [
//           _buildInfoItem(
//             icon: FeatherIcons.award,
//             label: 'الإنجازات',
//             value: achievementsCount.toString(),
//           ),
//           _buildDivider(),
//           _buildInfoItem(
//             icon: FeatherIcons.book,
//             label: 'التعليم',
//             value: education,
//           ),
//           _buildDivider(),
//           _buildInfoItem(
//             icon: FeatherIcons.briefcase,
//             label: 'العمل',
//             value: currentJob,
//           ),
//           _buildDivider(),
//           _buildInfoItem(
//             icon: FeatherIcons.users,
//             label: 'الفرع',
//             value: familyBranch,
//           ),
//         ],
//       ),
//     );
//   }

//   Widget _buildInfoItem({
//     required IconData icon,
//     required String label,
//     required String value,
//   }) {
//     return Expanded(
//       child: Column(
//         mainAxisSize: MainAxisSize.min,
//         children: [
//           Icon(icon, color: ProfileTheme.primaryColor, size: 24),
//           const SizedBox(height: 8),
//           Text(
//             value,
//             style: const TextStyle(
//               fontWeight: FontWeight.bold,
//               fontSize: 16,
//               color: ProfileTheme.textColor,
//             ),
//             maxLines: 1,
//             overflow: TextOverflow.ellipsis,
//           ),
//           const SizedBox(height: 4),
//           Text(
//             label,
//             style: TextStyle(
//               fontSize: 12,
//               color: ProfileTheme.textColor.withOpacity(0.7),
//             ),
//             maxLines: 1,
//             overflow: TextOverflow.ellipsis,
//           ),
//         ],
//       ),
//     );
//   }

//   Widget _buildDivider() {
//     return Container(
//       height: 40,
//       width: 1,
//       color: Colors.grey.withOpacity(0.2),
//     );
//   }
// }

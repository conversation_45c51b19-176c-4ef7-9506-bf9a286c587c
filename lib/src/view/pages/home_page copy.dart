import 'package:almashal/src/data/services/auth_service.dart';
import 'package:almashal/src/view/components/layouts/custom_drawer.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import '../../core/routes/app_pages.dart';
import '../../core/values/assets.dart';
import '../components/buttons/custom_filled_button.dart';
import '../components/cards/home_card.dart';
import '../components/layouts/master_page.dart';

class HomePage extends StatelessWidget {
  const HomePage({super.key});

  @override
  Widget build(BuildContext context) {
    return MasterPage(
      drawer: const CustomDrawer(),
      withBackground: true,
      title: "أسرة المشعل",
      body: Center(
        child: SingleChildScrollView(
          child: Column(
            children: [
              Padding(
                padding: const EdgeInsets.symmetric(
                  vertical: 22.0,
                  horizontal: 16,
                ),
                child: FractionallySizedBox(
                  widthFactor: 0.33,
                  child: AspectRatio(
                    aspectRatio: 118 / 155,
                    child: HomeCard(
                      title: 'شجرة العائلة',
                      svgIcon: Assets.tree,
                      onTap: () {
                        Get.toNamed(Routes.FAMILY_TREE_PAGE);
                      },
                    ),
                  ),
                ),
              ),
              GridView.count(
                padding: const EdgeInsets.all(16),
                crossAxisCount: 3,
                crossAxisSpacing: 8,
                mainAxisSpacing: 25,
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                childAspectRatio: 118 / 155,
                children: [
                  HomeCard(
                    title: 'النسب',
                    svgIcon: Assets.family,
                    onTap: () {
                      Get.toNamed(Routes.PARENT_PAGE);
                    },
                  ),
                  HomeCard(
                    title: 'المناسبات',
                    svgIcon: Assets.calendar,
                    onTap: () {
                      Get.toNamed(Routes.OCCASIONS_PAGE);
                    },
                  ),
                  HomeCard(
                    title: 'الأخبار',
                    svgIcon: Assets.news,
                    onTap: () {
                      Get.toNamed(Routes.NEWS_PAGE);
                    },
                  ),
                  HomeCard(
                    title: 'من الذاكرة',
                    svgIcon: Assets.images,
                    onTap: () {
                      Get.toNamed(Routes.MEMORY_PAGE);
                    },
                  ),
                  HomeCard(
                    title: 'اللجان',
                    svgIcon: Assets.commitee,
                    onTap: () {
                      Get.toNamed(Routes.COMMITTEES_PAGE);
                    },
                  ),
                  HomeCard(
                    title: 'جائزة التفوق',
                    svgIcon: Assets.medal,
                    onTap: () {
                      Get.toNamed(Routes.EXCELLENCE_AWARDS_PAGE);
                    },
                  ),
                ],
              ),
              SizedBox(
                width: double.maxFinite,
                child: Obx(
                  () => CustomFilledButton(
                    label: AuthService.instance.userData.value != null
                        ? 'تسجيل الخروج'
                        : 'تسجيل الدخول',
                    onTap: AuthService.instance.userData.value != null
                        ? () async {
                            await AuthService.instance.logout();
                          }
                        : () async {
                            Get.toNamed(Routes.LOGIN_PAGE);
                          },
                    margin: const EdgeInsets.only(
                      bottom: 16,
                      top: 64,
                      right: 16,
                      left: 16,
                    ),
                  ),
                ),
              )
            ],
          ),
        ),
      ),
    );
  }
}

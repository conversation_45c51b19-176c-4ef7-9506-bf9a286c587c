import 'package:almashal/src/view/components/image/attached_images_bar.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../../controllers/about_family_controller.dart';
import '../../../data/enums/page_loading_status.dart';
import '../../components/errors/loading.dart';
import '../../components/errors/no_content.dart';
import '../../components/errors/no_internet.dart';
import '../../components/errors/server_error.dart';
import '../../components/html/html_view_component.dart';
import '../../components/image/attached_youtube_videos_bar.dart';
import '../../components/layouts/master_page.dart';

class ParentPage extends GetView<AboutFamilyController> {
  const ParentPage({super.key});

  @override
  Widget build(BuildContext context) {
    return Obx(() => MasterPage(
          title: 'النسب',
          shareText: controller.shareText.value,
          shareLink: controller.shareLink.value,
          body: Obx(() {
            if (controller.pageLoadingStatus.value ==
                PageLoadingStatus.loading) {
              return const Loading();
            } else if (controller.pageLoadingStatus.value ==
                PageLoadingStatus.networkError) {
              return const NoInternet();
            } else if (controller.pageLoadingStatus.value ==
                PageLoadingStatus.failed) {
              return const ServerError();
            } else {
              if (controller.aboutFamily.value == null) {
                return const NoContent();
              }
              var aboutData = controller.aboutFamily.value;
              return SingleChildScrollView(
                padding: const EdgeInsets.all(16.0),
                child: Column(
                  children: [
                    HtmlViewComponent(
                      html: aboutData?.about ?? '',
                    ),
                    if ((aboutData?.images ?? []).isNotEmpty)
                      AttachedImagesBar(
                        images: aboutData?.images ?? [],
                      ),
                    if ((aboutData?.youtubeVideos ?? []).isNotEmpty)
                      AttachedYoutubeVideosBar(
                        youtubeVideos: aboutData?.youtubeVideos ?? [],
                      ),
                  ],
                ),
              );
            }
          }),
        ));
  }
}

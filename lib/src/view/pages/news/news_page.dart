import 'package:almashal/src/controllers/news_controller.dart';
import 'package:almashal/src/core/routes/app_pages.dart';
import 'package:almashal/src/core/values/app_config.dart';
import 'package:almashal/src/data/enums/page_loading_status.dart';
import 'package:almashal/src/view/components/cards/custom_first_card.dart';
import 'package:almashal/src/view/components/errors/loading.dart';
import 'package:almashal/src/view/components/errors/no_content.dart';
import 'package:almashal/src/view/components/errors/no_internet.dart';
import 'package:almashal/src/view/components/errors/server_error.dart';
import 'package:almashal/src/view/components/layouts/master_page.dart';
import 'package:almashal/src/view/components/list_tiles/news_list_tile.dart';
import 'package:almashal/src/view/components/loadings/loadmore.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class NewsPage extends GetView<NewsController> {
  const NewsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return MasterPage(
      title: 'الأخبار',
      isSearchable: true,
      onSearchSubmitted: (value) {
        if (value != null) {
          controller.search(value);
        }
      },
      onSearchToggled: (value) {
        if (!value) {
          controller.clearSearch();
        }
      },
      body: Obx(
        () {
          if (controller.pageLoadingStatus.value == PageLoadingStatus.loading) {
            return const Loading();
          } else if (controller.pageLoadingStatus.value ==
              PageLoadingStatus.networkError) {
            return const NoInternet();
          } else if (controller.pageLoadingStatus.value ==
              PageLoadingStatus.failed) {
            return const ServerError();
          } else {
            var news = controller.news.value;
            if (news.isEmpty) {
              String message = "عذراً، لا يوجد أخبار حالياً.";
              if (controller.searchTerm.value?.isNotEmpty ?? false) {
                message =
                    "عذراً، لا يوجد نتائج مطابقة لكلمة البحث  '${controller.searchTerm.value}'.";
              }
              return NoContent(
                message: message,
              );
            }
            return Column(
              children: [
                Expanded(
                  child: ListView.builder(
                    itemCount: news.length,
                    padding: const EdgeInsets.all(16),
                    itemBuilder: (BuildContext context, int index) {
                      if (index == 0) {
                        return CustomFirstCard(
                          imageUrl: AppConfig.thumbBaseUrl + news[index].image,
                          text: news[index].title,
                          onTap: () {
                            Get.toNamed(Routes.NEWS_DETAIL_PAGE,
                                arguments: news[index]);
                          },
                        );
                      } else {
                        return Column(
                          children: [
                            NewsListTile(
                              imageUrl:
                                  AppConfig.thumbBaseUrl + news[index].image,
                              text: news[index].title,
                              onTap: () {
                                Get.toNamed(Routes.NEWS_DETAIL_PAGE,
                                    arguments: news[index]);
                              },
                              date: news[index].publishedAt,
                              margin: const EdgeInsets.only(top: 16),
                            ),
                            if (index == news.length - 1 &&
                                !controller.cantLoadMore())
                              LoadMore(onLoadMore: () {
                                controller.loadMore();
                              })
                          ],
                        );
                      }
                    },
                  ),
                ),
              ],
            );
          }
        },
      ),
    );
  }
}

import 'package:almashal/src/core/utils/common_functions.dart';
import 'package:almashal/src/core/utils/record_visit_function.dart';
import 'package:almashal/src/core/values/app_config.dart';
import 'package:almashal/src/data/models/news.dart';
import 'package:almashal/src/view/components/fabs/add_comment_floating_action_button.dart';
import 'package:almashal/src/view/components/html/html_view_component.dart';
import 'package:almashal/src/view/components/image/attached_images_bar.dart';
import 'package:almashal/src/view/components/image/attached_youtube_videos_bar.dart';
import 'package:almashal/src/view/components/image/custom_cached_network_image.dart';
import 'package:almashal/src/view/components/layouts/master_page.dart';
import 'package:almashal/src/view/components/list/comment_list.dart';
import 'package:almashal/src/view/components/widgets/visit_count_widget.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

class NewsDetailPage extends StatelessWidget {
  const NewsDetailPage({super.key});

  @override
  Widget build(BuildContext context) {
    News news = Get.arguments;

print(news.toJson());
    // Record the visit
    WidgetsBinding.instance.addPostFrameCallback((_) {
      RecordVisitFunction.news(news.id);
    });
    return MasterPage(
      title: news.title,
      shareText:
          '${news.title}\n\n${CommonFunctions.htmlToShareableText(news.content)}',
      shareLink: news.shareLink,
      floatingActionButton: AddCommentFloatingActionButton(
        type: 'news',
        id: news.id,
      ),
      body: SingleChildScrollView(
        padding:
            const EdgeInsets.only(bottom: 16, right: 16, left: 16, top: 16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  news.title,
                  style: TextStyle(
                    fontSize: 20,
                    fontWeight: FontWeight.w500,
                    color: Get.theme.primaryColor,
                  ),
                ),
                Padding(
                  padding: const EdgeInsets.only(
                    top: 8.0,
                    bottom: 8,
                  ),
                  child: Row(
                    children: [
                      Text(
                        news.publishedAt,
                        style: TextStyle(
                          color: Get.theme.colorScheme.secondary,
                        ),
                      ),
                      const Spacer(),
                      VisitCountWidget(
                        visitsCount: news.visitsCount,
                      ),
                    ],
                  ),
                ),
              ],
            ),
            CustomCachedNetworkImage(
              imageUrl: AppConfig.imageBaseUrl + news.image,
              thumbImageUrl: AppConfig.thumbBaseUrl + news.image,
              width: double.maxFinite,
              fit: BoxFit.fitWidth,
              borderRadius: BorderRadius.circular(8),
              withPreview: true,
            ),
            HtmlViewComponent(
              html: "<div>${news.content}</div>",
            ),
            if (news.images.isNotEmpty) AttachedImagesBar(images: news.images),
            if (news.youtubeVideos.isNotEmpty)
              AttachedYoutubeVideosBar(
                youtubeVideos: news.youtubeVideos,
              ),
            CommentList(
              type: 'news',
              id: news.id,
            ),
          ],
        ),
      ),
    );
  }
}

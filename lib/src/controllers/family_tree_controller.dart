import 'package:almashal/src/core/values/app_config.dart';
import 'package:get/get.dart';
import 'package:url_launcher/url_launcher.dart';

import '../core/utils/common_functions.dart';
import '../data/enums/page_loading_status.dart';
import '../data/services/network_service.dart';

class FamilyTreeController extends GetxController {
  static FamilyTreeController get instance => Get.find();
  var image = ''.obs;
  var shareUrl = ''.obs;
  var fileDownloading = false.obs;
  var pageLoadingStatus = PageLoadingStatus.done.obs;
  var apiProvider = AppConfig.apiProvider;

  @override
  void onInit() {
    getData();
    super.onInit();
  }

  // get tree image
  void getData() {
    pageLoadingStatus.value = PageLoadingStatus.loading;
    NetworkService.instance.checkConnectivity(
      () async {
        try {
          var response = await apiProvider.getFamilyTreeImage();
          image.value = response.treeImage;
          shareUrl.value = response.treeShareText;
          pageLoadingStatus.value = PageLoadingStatus.done;
        } catch (err) {
          CommonFunctions.handleError(err);
          pageLoadingStatus.value = PageLoadingStatus.failed;
        }
      },
      () {
        pageLoadingStatus.value = PageLoadingStatus.networkError;
      },
    );
  }

  void downloadFamilyTree() async {
    // print((await getDownloadsDirectory())?.absolute.path);
    // fileDownloading.value = true;
    if (await canLaunchUrl(Uri.parse(shareUrl.value))) {
      await launchUrl(
        Uri.parse(
          shareUrl.value,
        ),
      );
    }

    // var file = await FileSaver.instance.saveFile(
    //   name: 'ALMASHAL_FAMILY_TREE.pdf',
    //   link: LinkDetails(
    //     link: "https://almashalfamily.com/downloads/almashal-tree.pdf",
    //   ),
    //   mimeType: MimeType.pdf,
    // );
    // await File(file).copy(
    //     '${(await getDownloadsDirectory())?.absolute.path ?? ""}/almashal-tree.pdf');
    // fileDownloading.value = false;
    // CommonFunctions.showSuccessMessage("تم تحميل الملف بنجاح الى الملفات".tr);
  }
}

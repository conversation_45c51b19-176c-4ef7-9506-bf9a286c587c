
import 'package:almashal/src/core/values/app_config.dart';
import 'package:get/get.dart';

import '../core/utils/common_functions.dart';
import '../data/enums/page_loading_status.dart';
import '../data/models/committee.dart';
import '../data/services/network_service.dart';

class CommitteesController extends GetxController {
  Rx<List<Committee>> committees = Rx<List<Committee>>([]);
  var pageLoadingStatus = PageLoadingStatus.done.obs;
  var apiProvider = AppConfig.apiProvider;

  @override
  void onInit() {
    getData();
    super.onInit();
  }

  // get tree image
  void getData() {
    pageLoadingStatus.value = PageLoadingStatus.loading;
    NetworkService.instance.checkConnectivity(
      () async {
        try {
          var response = await apiProvider.getCommittees();

          committees.value = response;
          pageLoadingStatus.value = PageLoadingStatus.done;
        } catch (err) {
          CommonFunctions.handleError(err);
          pageLoadingStatus.value = PageLoadingStatus.failed;
        }
      },
      () {
        pageLoadingStatus.value = PageLoadingStatus.networkError;
      },
    );
  }
}

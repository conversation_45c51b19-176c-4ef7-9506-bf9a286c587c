import 'package:almashal/src/core/values/app_config.dart';
import 'package:almashal/src/data/models/responses/occasions_response.dart';
import 'package:get/get.dart';

import '../core/utils/common_functions.dart';
import '../data/enums/page_loading_status.dart';
import '../data/models/occasion.dart';
import '../data/services/network_service.dart';

class OccasionsController extends GetxController {
  OccasionsController({required this.type});
  Rx<List<Occasion>> occasions = Rx<List<Occasion>>([]);
  var pageLoadingStatus = PageLoadingStatus.done.obs;
  var apiProvider = AppConfig.apiProvider;
  int page = 0;
  int lastPage = 0;
  Rx<int> type = 1.obs;
  Rx<bool> showSearchField = false.obs;

  Rx<String?> searchTerm = Rx<String?>(null);

  void search(String value) {
    searchTerm.value = value;
    page = 0;
    lastPage = 0;
    getData();
  }

  void clearSearch() {
    searchTerm.value = null;
    page = 0;
    lastPage = 0;
    getData();
  }

  @override
  void onInit() {
    getData();
    super.onInit();
  }

  // get tree image
  void getData() {
    pageLoadingStatus.value = PageLoadingStatus.loading;
    NetworkService.instance.checkConnectivity(
      () async {
        try {
          print("type : $type");
          OccasionsResponse response;
          if (searchTerm.value == null || searchTerm.value!.isEmpty) {
            response = await apiProvider.getOccasions(type: type.value);
          } else {
            response = await apiProvider.searchInOccasions(
              type: type.value,
              search: searchTerm.value!,
            );
          }
          // if (kDebugMode) {
          //   print(response.toJson());
          // }
          occasions.value = response.occasions;
          occasions.refresh();
          page = response.currentPage;
          lastPage = response.lastPage;
          pageLoadingStatus.value = PageLoadingStatus.done;
        } catch (err) {
          CommonFunctions.handleError(err);
          pageLoadingStatus.value = PageLoadingStatus.failed;
        }
      },
      () {
        pageLoadingStatus.value = PageLoadingStatus.networkError;
      },
    );
  }

  // load more
  void loadMore() {
    print('loading more...');
    if (!cantLoadMore()) {
      NetworkService.instance.checkConnectivity(
        () async {
          try {
            OccasionsResponse response;
            if (searchTerm.value == null || searchTerm.value!.isEmpty) {
              response = await apiProvider.getOccasions(
                page: page + 1,
                type: type.value,
              );
            } else {
              response = await apiProvider.searchInOccasions(
                page: page + 1,
                type: type.value,
                search: searchTerm.value!,
              );
            }
            occasions.value.addAll(response.occasions);
            occasions.refresh();
            page = response.currentPage;
            lastPage = response.lastPage;
            pageLoadingStatus.value = PageLoadingStatus.done;
          } catch (err) {
            CommonFunctions.handleError(err);
          }
        },
        () {},
      );
    }
  }

  bool cantLoadMore() {
    return page == lastPage;
  }
}

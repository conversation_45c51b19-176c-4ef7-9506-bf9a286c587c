import 'dart:io';

import 'package:almashal/src/core/utils/common_functions.dart';
import 'package:almashal/src/core/utils/platform_helper.dart';
import 'package:almashal/src/core/values/app_config.dart';
import 'package:almashal/src/data/models/vms/user_token_vm.dart';
import 'package:almashal/src/data/models/vms/user_vm.dart';
import 'package:almashal/src/data/services/auth_service.dart';
import 'package:almashal/src/data/services/network_service.dart';
import 'package:dio/dio.dart' as dio;
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:http_parser/http_parser.dart';

class ProfileController extends GetxController {
  final int? id;
  ProfileController({this.id});

  Future<dynamic> getProfile(String userId) async {
    try {
      final apiProvider = AppConfig.apiProvider;
      final response = await apiProvider.getProfile(userId);
      return response;
    } catch (e) {
      // استخدام CommonFunctions بدلاً من ErrorBag
      CommonFunctions.handleError(e);
      rethrow;
    }
  }
}

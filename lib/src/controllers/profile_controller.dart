import 'package:almashal/src/core/utils/common_functions.dart';
import 'package:almashal/src/core/values/app_config.dart';
import 'package:get/get.dart';

class ProfileController extends GetxController {
  final int? id;
  ProfileController({this.id});

  Future<dynamic> getProfile(String userId) async {
    try {
      final apiProvider = AppConfig.apiProvider;
      final response = await apiProvider.getProfile(userId);
      return response;
    } catch (e) {
      // استخدام CommonFunctions بدلاً من ErrorBag
      CommonFunctions.handleError(e);
      rethrow;
    }
  }
}

import 'package:almashal/src/core/values/app_config.dart';
import 'package:almashal/src/data/models/responses/albums_response.dart';
// import 'package:flutter/foundation.dart';
import 'package:get/get.dart';

import '../core/utils/common_functions.dart';
import '../data/enums/page_loading_status.dart';
import '../data/models/album.dart';
import '../data/services/network_service.dart';

class AlbumsController extends GetxController {
  Rx<List<Album>> albums = Rx<List<Album>>([]);
  var pageLoadingStatus = PageLoadingStatus.done.obs;
  var apiProvider = AppConfig.apiProvider;
  int page = 0;
  int lastPage = 0;

  Rx<String?> searchTerm = Rx<String?>(null);

  void search(String value) {
    searchTerm.value = value;
    page = 0;
    lastPage = 0;
    getData();
  }

  void clearSearch() {
    searchTerm.value = null;
    page = 0;
    lastPage = 0;
    getData();
  }

  @override
  void onInit() {
    getData();
    super.onInit();
  }

  // get tree image
  void getData() {
    pageLoadingStatus.value = PageLoadingStatus.loading;
    NetworkService.instance.checkConnectivity(
      () async {
        try {
          AlbumsResponse response;
          if (searchTerm.value == null || searchTerm.value!.isEmpty) {
            response = await apiProvider.getAlbums(perPage: 11);
          } else {
            response = await apiProvider.searchInAlbums(
                search: searchTerm.value!, perPage: 11);
          }
          // if (kDebugMode) {
          //   print(response.toJson());
          // }
          albums.value = response.albums;
          page = response.currentPage;
          lastPage = response.lastPage;
          pageLoadingStatus.value = PageLoadingStatus.done;
        } catch (err) {
          CommonFunctions.handleError(err);
          pageLoadingStatus.value = PageLoadingStatus.failed;
        }
      },
      () {
        pageLoadingStatus.value = PageLoadingStatus.networkError;
      },
    );
  }

  // load more
  void loadMore() {
    print('loading more...');
    if (!cantLoadMore()) {
      NetworkService.instance.checkConnectivity(
        () async {
          try {
            AlbumsResponse response;
            if (searchTerm.value == null || searchTerm.value!.isEmpty) {
              response =
                  await apiProvider.getAlbums(page: page + 1, perPage: 11);
            } else {
              response = await apiProvider.searchInAlbums(
                  search: searchTerm.value!, page: page + 1, perPage: 11);
            }
            albums.value.addAll(response.albums);
            albums.refresh();
            page = response.currentPage;
            lastPage = response.lastPage;
            pageLoadingStatus.value = PageLoadingStatus.done;
          } catch (err) {
            CommonFunctions.handleError(err);
          }
        },
        () {},
      );
    }
  }

  bool cantLoadMore() {
    return page == lastPage;
  }
}

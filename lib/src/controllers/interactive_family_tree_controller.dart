import 'package:almashal/src/core/utils/common_functions.dart';
import 'package:almashal/src/core/values/app_config.dart';
import 'package:almashal/src/data/enums/page_loading_status.dart';
import 'package:almashal/src/data/models/family_tree_node.dart';
import 'package:almashal/src/data/services/network_service.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart' hide Node;
import 'package:graphview/GraphView.dart';

class InteractiveFamilyTreeController extends GetxController {
  static InteractiveFamilyTreeController get instance => Get.find();
  final apiProvider = AppConfig.apiProvider;
  var pageLoadingStatus = PageLoadingStatus.done.obs;

  // إعداد اتجاه شجرة العائلة - الوضع الافتراضي هو من الأسفل إلى الأعلى
  int orientation = BuchheimWalkerConfiguration.ORIENTATION_BOTTOM_TOP;

  // تكوين الشجرة
  late BuchheimWalkerConfiguration builder;

  // مجموعة النودات المطوية (مخفية فروعها)
  final RxSet<int> collapsedNodes = <int>{}.obs;

  // القيم الحركية للنودات
  final Map<int, double> nodeAnimationValues = {};

  // تبديل اتجاه الشجرة
  void toggleOrientation() {
    // تبديل بين الاتجاهات الثلاثة المتاحة
    if (orientation == BuchheimWalkerConfiguration.ORIENTATION_TOP_BOTTOM) {
      orientation = BuchheimWalkerConfiguration.ORIENTATION_RIGHT_LEFT;
    } else if (orientation ==
        BuchheimWalkerConfiguration.ORIENTATION_RIGHT_LEFT) {
      orientation = BuchheimWalkerConfiguration.ORIENTATION_BOTTOM_TOP;
    } else {
      orientation = BuchheimWalkerConfiguration.ORIENTATION_TOP_BOTTOM;
    }

    // تحديث إعدادات الشجرة مع الاتجاه الجديد
    builder.orientation = orientation;
  }

  // Tree nodes list
  final treeNodes = <FamilyTreeNode>[].obs;

  // تبديل حالة طي/عرض نود معين
  void toggleNodeCollapse(int nodeId) {
    // البحث عن النود في القائمة
    FamilyTreeNode? node =
        treeNodes.firstWhereOrNull((element) => element.id == nodeId);

    // إذا لم يوجد النود أو ليس له أطفال، نخرج من الدالة
    if (node == null || node.children == null || node.children!.isEmpty) {
      return;
    }

    if (collapsedNodes.contains(nodeId)) {
      // إذا كان النود مطوياً، قم بعرضه
      collapsedNodes.remove(nodeId);

      // عرض تنبيه بعدد الفروع التي تم عرضها
      int childrenCount = _countChildrenRecursively(nodeId);
      if (childrenCount > 0) {
        Get.snackbar(
          'تم عرض الفروع',
          'تم عرض $childrenCount فرع',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.green.withOpacity(0.7),
          colorText: Colors.white,
          duration: const Duration(seconds: 2),
        );
      }
    } else {
      // إذا كان النود معروضاً، قم بطيه
      collapsedNodes.add(nodeId);

      // عرض تنبيه بعدد الفروع التي تم إخفاؤها
      int childrenCount = _countChildrenRecursively(nodeId);
      if (childrenCount > 0) {
        Get.snackbar(
          'تم إخفاء الفروع',
          'تم إخفاء $childrenCount فرع',
          snackPosition: SnackPosition.BOTTOM,
          backgroundColor: Colors.orange.withOpacity(0.7),
          colorText: Colors.white,
          duration: const Duration(seconds: 2),
        );
      }
    }

    // تحديث الرسم البياني
    update();
  }

  // وظيفة عامة للحصول على عدد الأطفال
  int countChildren(FamilyTreeNode node) {
    return _countChildrenRecursively(node.id);
  }

  // حساب عدد الفروع لنود معين بشكل تكراري
  int _countChildrenRecursively(int nodeId) {
    FamilyTreeNode? node =
        treeNodes.firstWhereOrNull((element) => element.id == nodeId);
    if (node == null || node.children == null || node.children!.isEmpty) {
      return 0;
    }

    int count = node.children!.length;
    for (var child in node.children!) {
      count += _countChildrenRecursively(child.id);
    }

    return count;
  }

  // التحقق مما إذا كان النود مخفياً بسبب طي أحد أجداده
  bool isNodeHidden(int nodeId) {
    FamilyTreeNode? node =
        treeNodes.firstWhereOrNull((element) => element.id == nodeId);
    if (node == null) return false;

    // إذا كان النود ليس له أب، فلا يمكن أن يكون مخفياً (النود الرئيسي)
    if (node.parent == null) return false;

    // التحقق مما إذا كان أحد الأجداد مطوياً
    FamilyTreeNode? parent =
        treeNodes.firstWhereOrNull((element) => element.id == node.parent?.id);

    while (parent != null) {
      if (collapsedNodes.contains(parent.id)) {
        return true;
      }
      // الانتقال للجد التالي
      if (parent.parent == null) break;
      parent = treeNodes
          .firstWhereOrNull((element) => element.id == parent!.parent?.id);
    }

    return false;
  }

  // التحقق مما إذا كان النود لديه أطفال غير مخفيين
  bool hasVisibleChildren(int nodeId) {
    if (collapsedNodes.contains(nodeId)) return false;

    FamilyTreeNode? node =
        treeNodes.firstWhereOrNull((element) => element.id == nodeId);
    if (node == null || node.children == null || node.children!.isEmpty) {
      return false;
    }

    return true;
  }

  // بناء الرسم البياني للشجرة مع مراعاة النودات المطوية
  Graph get graph {
    final graph = Graph()..isTree = true;

    // إضافة العقد
    for (var element in treeNodes) {
      var node = Node.Id(element.id);
      graph.addNode(node);
    }

    // إضافة العلاقات مع مراعاة النودات المطوية
    for (var node in treeNodes) {
      if (node.children != null && node.children!.isNotEmpty) {
        // إضافة حواف فقط إذا لم يكن النود مطوياً
        if (!collapsedNodes.contains(node.id)) {
          for (var child in node.children!) {
            // إضافة الحافة فقط إذا لم تكن مخفية من خلال طي أحد الأجداد
            if (!isNodeHidden(child.id)) {
        graph.addEdge(Node.Id(node.id), Node.Id(child.id));
            }
          }
        }
      }
    }

    return graph;
  }

  // تحويل العقد إلى خريطة للوصول السريع
  Map<String, FamilyTreeNode> get nodesAsMap {
    var map = <String, FamilyTreeNode>{};
    for (var element in treeNodes) {
      map[element.id.toString()] = element;
    }
    return map;
  }

  @override
  void onInit() {
    // إعداد تكوين الشجرة
    builder = BuchheimWalkerConfiguration()
      ..siblingSeparation = 100
      ..levelSeparation = 150
      ..subtreeSeparation = 150
      ..orientation = orientation;

    // الحصول على البيانات
    getData();
    super.onInit();
  }

  void getData() async {
    pageLoadingStatus.value = PageLoadingStatus.loading;
    NetworkService.instance.checkConnectivity(
      () async {
        try {
          var response = await apiProvider.getFamilyTree();
          treeNodes.value = response;
          pageLoadingStatus.value = PageLoadingStatus.done;
        } catch (err) {
          CommonFunctions.handleError(err);
          pageLoadingStatus.value = PageLoadingStatus.failed;
        }
      },
      () {
        pageLoadingStatus.value = PageLoadingStatus.networkError;
      },
    );
  }

  Future<void> addChangeRequest(
      {required int familyTreeNodeId,
      required int requestType,
      required String note}) async {
    await NetworkService.instance.checkConnectivity(
      () async {
        try {
          var response =
              await AppConfig.authenticatedApiProvider.createTreeChangeRequest(
            {
              "family_tree_node_id": familyTreeNodeId,
              "request_type": requestType,
              "note": note
            },
          );
          Get.back();
          CommonFunctions.showSuccessMessage(response.message);
          if (kDebugMode) {
            print(response.toJson());
          }
        } catch (err) {
          CommonFunctions.handleError(err);
        }
      },
      () {
        CommonFunctions.showErrorMessage('عذراً .. انت غير متصل بالانترنت');
      },
    );
  }

  // التحقق مما إذا كان يمكن طي/توسيع النود
  bool canToggleNode(int nodeId) {
    // البحث عن النود في قائمة النودات
    FamilyTreeNode? node =
        treeNodes.firstWhereOrNull((element) => element.id == nodeId);

    // إذا لم يتم العثور على النود أو ليس له أطفال، فلا يمكن طيه
    if (node == null || node.children == null || node.children!.isEmpty) {
      return false;
    }

    // يمكن طي النود إذا كان له أطفال، بغض النظر عما إذا كان نوداً رئيسياً أو لا
    return true;
  }
}

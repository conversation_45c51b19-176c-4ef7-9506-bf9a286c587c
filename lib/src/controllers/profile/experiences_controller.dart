import 'package:get/get.dart';
import 'package:almashal/src/data/models/profile/experience.dart';
import 'package:almashal/src/controllers/profile_controller.dart';
import 'package:almashal/src/data/services/error_service.dart';

class ExperiencesController extends GetxController {
  final _profileController = Get.find<ProfileController>();
  final _errorService = Get.find<ErrorService>();

  // Observable variables
  final isLoading = false.obs;
  final experiencesList = <Experience>[].obs;

  // إضافة معرف المستخدم الحالي
  final userId = RxString('');

  @override
  void onInit() {
    super.onInit();
    // Bind to profile controller experiences list
    ever(_profileController.experiences, (List<Experience> experiences) {
      experiencesList.value = experiences;
    });
  }

  // تعيين معرف المستخدم وتحميل الخبرات
  void setUserId(String newUserId) {
    userId.value = newUserId;
    loadExperiences();
  }

  Future<void> loadExperiences() async {
    isLoading.value = true;
    try {
      await _profileController.loadExperiences(
          userId: userId.value, forceRefresh: true);
    } catch (e) {
      _errorService.handleError(e);
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> addExperience({
    required String title,
    required String organization,
    required DateTime startDate,
    DateTime? endDate,
    required String description,
    required String type,
    String? location,
    required bool isCurrentPosition,
  }) async {
    try {
      isLoading.value = true;

      await _profileController.addExperience(
        title: title,
        organization: organization,
        startDate: startDate,
        endDate: endDate,
        description: description,
        type: type,
        location: location,
        isCurrentPosition: isCurrentPosition,
      );
      await loadExperiences();
      Get.snackbar(
        'نجاح',
        'تمت إضافة الخبرة بنجاح',
        snackPosition: SnackPosition.BOTTOM,
      );
    } catch (e) {
      _errorService.handleError(e);
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> updateExperience({
    required int id,
    String? title,
    String? organization,
    DateTime? startDate,
    DateTime? endDate,
    String? description,
    String? type,
    String? location,
    bool? isCurrentPosition,
  }) async {
    try {
      isLoading.value = true;

      await _profileController.updateExperience(
        id: id,
        title: title,
        organization: organization,
        startDate: startDate,
        endDate: endDate,
        description: description,
        type: type,
        location: location,
        isCurrentPosition: isCurrentPosition,
      );
      await loadExperiences();
      Get.snackbar(
        'نجاح',
        'تم تحديث الخبرة بنجاح',
        snackPosition: SnackPosition.BOTTOM,
      );
      await loadExperiences();
    } catch (e) {
      _errorService.handleError(e);
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> deleteExperience(int id) async {
    try {
      isLoading.value = true;

      await _profileController.deleteExperience(id);
      await loadExperiences();
    } catch (e) {
      _errorService.handleError(e);
    } finally {
      isLoading.value = false;
    }
  }

  // Helper method to format duration
  String formatDuration(Experience experience) {
    final startDate = experience.startDate;
    final endDate =
        experience.isCurrentPosition ? DateTime.now() : experience.endDate;

    if (endDate == null) return 'غير محدد';

    final difference = endDate.difference(startDate);
    final years = difference.inDays ~/ 365;
    final months = (difference.inDays % 365) ~/ 30;

    String duration = '';
    if (years > 0) {
      duration += '$years سنة ';
    }
    if (months > 0) {
      duration += '$months شهر';
    }
    return duration.trim();
  }
}

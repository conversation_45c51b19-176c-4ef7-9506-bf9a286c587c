import 'package:get/get.dart';
import 'package:almashal/src/data/models/profile/skill.dart';
import 'package:almashal/src/data/services/profile_service.dart';
import 'package:almashal/src/data/services/error_service.dart';

class SkillsController extends GetxController {
  final RxList<Skill> skillsList = <Skill>[].obs;
  final RxBool isLoading = false.obs;
  final _profileService = Get.find<ProfileService>();
  final _errorService = Get.find<ErrorService>();

  // إضافة معرف المستخدم الحالي
  final userId = RxString('');

  @override
  void onInit() {
    super.onInit();
    // Bind to profile service skills list
    ever(_profileService.skills, (List<Skill> skills) {
      skillsList.value = skills;
    });
  }

  // تعيين معرف المستخدم وتحميل المهارات
  void setUserId(String newUserId) {
    userId.value = newUserId;
    loadSkills();
  }

  String getSkillLevelText(int level) {
    switch (level) {
      case 1:
        return 'مبتدئ';
      case 2:
        return 'متوسط';
      case 3:
        return 'متقدم';
      case 4:
        return 'خبير';
      case 5:
        return 'محترف';
      default:
        return 'غير محدد';
    }
  }

  bool _validateSkillData(String title, String description, int level) {
    if (title.isEmpty) {
      _errorService.handleError('يجب إدخال عنوان المهارة');
      return false;
    }

    if (title.length < 2) {
      _errorService.handleError('عنوان المهارة قصير جداً');
      return false;
    }

    if (title.length > 50) {
      _errorService.handleError('عنوان المهارة طويل جداً');
      return false;
    }

    if (description.length > 500) {
      _errorService.handleError('وصف المهارة طويل جداً');
      return false;
    }

    if (level < 0 || level > 100) {
      _errorService.handleError('مستوى المهارة يجب أن يكون بين 0 و 100');
      return false;
    }

    return true;
  }

  Future<void> loadSkills() async {
    isLoading.value = true;
    try {
      await _profileService.loadSkills(
          userId: userId.value, forceRefresh: true);
    } finally {
      isLoading.value = false;
    }
  }

  Future<bool> addSkill(String title, String description, int level) async {
    if (!_validateSkillData(title, description, level)) {
      return false;
    }

    isLoading.value = true;
    try {
      await _profileService.addSkill(title, description, level);

      return true;
    } catch (e) {
      _errorService.handleError('فشل في إضافة المهارة');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  Future<bool> updateSkill(
      String id, String title, String description, int level) async {
    if (!_validateSkillData(title, description, level)) {
      return false;
    }

    isLoading.value = true;
    try {
      await _profileService.updateSkill(id, title, description, level);
      return true;
    } catch (e) {
      _errorService.handleError('فشل في تحديث المهارة');
      return false;
    } finally {
      isLoading.value = false;
    }
  }

  Future<bool> deleteSkill(String id) async {
    isLoading.value = true;
    try {
      await _profileService.deleteSkill(id);

      return true;
    } catch (e) {
      _errorService.handleError('فشل في حذف المهارة');
      return false;
    } finally {
      isLoading.value = false;
    }
  }
}

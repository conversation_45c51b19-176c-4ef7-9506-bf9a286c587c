import 'package:almashal/src/core/utils/common_functions.dart';
import 'package:almashal/src/core/values/app_config.dart';
import 'package:almashal/src/data/enums/page_loading_status.dart';
import 'package:almashal/src/data/models/profile/review.dart';
import 'package:almashal/src/data/services/network_service.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';

class ReviewsController extends GetxController {
  static ReviewsController get instance => Get.find<ReviewsController>();
  
  // قائمة المراجعات
  final reviewsList = <Review>[].obs;
  
  // حالة تحميل الصفحة
  final isLoading = true.obs;
  
  // صفحة API الحالية والأخيرة (للصفحات المتعددة)
  int page = 1;
  int lastPage = 1;
  
  // معرف المستخدم الذي نعرض مراجعاته
  String? userId;
  
  // تعيين معرف المستخدم
  void setUserId(String id) {
    userId = id;
    loadReviews();
  }
  
  // تحميل المراجعات من API
  Future<void> loadReviews() async {
    isLoading.value = true;
    
    await NetworkService.instance.checkConnectivity(
      () async {
        try {
          // استدعاء API لجلب المراجعات
          final response = await AppConfig.apiProvider.getReviews(
            userId: userId,
            page: page,
          );
          
          if (kDebugMode) {
            print("تم استلام المراجعات: ${response.data.length}");
          }
          
          // تحديث البيانات
          reviewsList.value = response.data;
          page = response.meta.currentPage;
          lastPage = response.meta.lastPage;
          
          isLoading.value = false;
        } catch (err) {
          isLoading.value = false;
          CommonFunctions.handleError(err);
        }
      },
      () {
        isLoading.value = false;
        Get.snackbar(
          'خطأ في الاتصال',
          'يرجى التحقق من اتصالك بالإنترنت',
          snackPosition: SnackPosition.BOTTOM,
        );
      },
    );
  }
  
  // تحميل المزيد من المراجعات (للصفحات التالية)
  Future<void> loadMoreReviews() async {
    if (page < lastPage) {
      page++;
      
      await NetworkService.instance.checkConnectivity(
        () async {
          try {
            // استدعاء API لجلب صفحة إضافية
            final response = await AppConfig.apiProvider.getReviews(
              userId: userId,
              page: page,
            );
            
            // إضافة البيانات الجديدة للقائمة الحالية
            reviewsList.addAll(response.data);
            page = response.meta.currentPage;
            lastPage = response.meta.lastPage;
            
          } catch (err) {
            CommonFunctions.handleError(err);
          }
        },
        () {
          Get.snackbar(
            'خطأ في الاتصال',
            'يرجى التحقق من اتصالك بالإنترنت',
            snackPosition: SnackPosition.BOTTOM,
          );
        },
      );
    }
  }
  
  // إضافة مراجعة جديدة
  Future<void> addReview(Map<String, dynamic> data) async {
    await NetworkService.instance.checkConnectivity(
      () async {
        try {
          // استدعاء API لإضافة مراجعة
          final response = await AppConfig.authenticatedApiProvider.addReview(data);
          
          Get.back(); // إغلاق النافذة المنبثقة
          
          CommonFunctions.showSuccessMessage(response.message);
          
          // إعادة تحميل المراجعات لعرض المراجعة الجديدة
          loadReviews();
        } catch (err) {
          CommonFunctions.handleError(err);
        }
      },
      () {
        Get.snackbar(
          'خطأ في الاتصال',
          'يرجى التحقق من اتصالك بالإنترنت',
          snackPosition: SnackPosition.BOTTOM,
        );
      },
    );
  }
  
  // فحص إذا كان يمكن تحميل المزيد من المراجعات
  bool canLoadMore() {
    return page < lastPage;
  }
  
  @override
  void onInit() {
    super.onInit();
    // سيتم تعيين userId قبل تحميل المراجعات
  }
} 
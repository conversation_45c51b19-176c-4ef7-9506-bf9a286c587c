import 'dart:io';
import 'package:get/get.dart';
import 'package:almashal/src/data/models/profile/achievement.dart';
import 'package:almashal/src/controllers/profile_controller.dart';
import 'package:almashal/src/data/services/error_service.dart';

class AchievementsController extends GetxController {
  final _profileController = Get.find<ProfileController>();
  final _errorService = Get.find<ErrorService>();

  final isLoading = false.obs;
  final isUploading = false.obs;
  final achievementsList = <Achievement>[].obs;
  final uploadProgress = 0.0.obs;

  // إضافة معرف المستخدم الحالي
  final userId = RxString('');

  @override
  void onInit() {
    super.onInit();
    ever(_profileController.achievements, (List<Achievement> achievements) {
      achievementsList.value = achievements;
    });
  }

  // تعيين معرف المستخدم وتحميل الإنجازات
  void setUserId(String newUserId) {
    userId.value = newUserId;
    loadAchievements();
  }

  Future<void> loadAchievements() async {
    // isLoading.value = true;
    // try {
    //   await _profileService.loadAchievements(
    //       userId: userId.value, forceRefresh: true);
    // } catch (e) {
    //   _errorService.handleError(e);
    //   Get.snackbar(
    //     'خطأ',
    //     'حدث خطأ أثناء تحميل الإنجازات',
    //     snackPosition: SnackPosition.BOTTOM,
    //   );
    // } finally {
    //   isLoading.value = false;
    // }
  }

  Future<bool> addAchievement({
    required String title,
    required String description,
    required String type,
    required DateTime date,
    File? certificateFile,
    File? imageFile,
  }) async {
    isUploading.value = true;
    uploadProgress.value = 0.0;
    try {
      // التحقق من الملفات
      if (certificateFile != null) {
        final fileSize = await certificateFile.length();
        if (fileSize > 10 * 1024 * 1024) {
          // 10MB
          throw Exception('حجم الشهادة يجب أن لا يتجاوز 10 ميجابايت');
        }
      }

      if (imageFile != null) {
        final fileSize = await imageFile.length();
        if (fileSize > 5 * 1024 * 1024) {
          // 5MB
          throw Exception('حجم الصورة يجب أن لا يتجاوز 5 ميجابايت');
        }
      }

      await _profileController.addAchievement(
        title,
        description,
        type: type,
        date: date,
        certificateFile: certificateFile,
        imageFile: imageFile,
      );

      Get.snackbar(
        'نجاح',
        'تم إضافة الإنجاز بنجاح',
        snackPosition: SnackPosition.BOTTOM,
      );
      return true;
    } catch (e) {
      _errorService.handleError(e);
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء إضافة الإنجاز',
        snackPosition: SnackPosition.BOTTOM,
      );
      return false;
    } finally {
      isUploading.value = false;
      uploadProgress.value = 0.0;
    }
  }

  Future<bool> updateAchievement({
    required String id,
    String? title,
    String? description,
    String? type,
    DateTime? date,
    File? certificateFile,
    File? imageFile,
  }) async {
    isUploading.value = true;
    uploadProgress.value = 0.0;
    try {
      // التحقق من الملفات
      if (certificateFile != null) {
        final fileSize = await certificateFile.length();
        if (fileSize > 10 * 1024 * 1024) {
          // 10MB
          throw Exception('حجم الشهادة يجب أن لا يتجاوز 10 ميجابايت');
        }
      }

      if (imageFile != null) {
        final fileSize = await imageFile.length();
        if (fileSize > 5 * 1024 * 1024) {
          // 5MB
          throw Exception('حجم الصورة يجب أن لا يتجاوز 5 ميجابايت');
        }
      }

      await _profileController.updateAchievement(
        id,
        title: title,
        description: description,
        type: type,
        date: date,
        certificateFile: certificateFile,
        imageFile: imageFile,
      );

      Get.snackbar(
        'نجاح',
        'تم تحديث الإنجاز بنجاح',
        snackPosition: SnackPosition.BOTTOM,
      );
      return true;
    } catch (e) {
      _errorService.handleError(e);
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحديث الإنجاز',
        snackPosition: SnackPosition.BOTTOM,
      );
      return false;
    } finally {
      isUploading.value = false;
      uploadProgress.value = 0.0;
    }
  }

  Future<bool> deleteAchievement(String id) async {
    try {
      await _profileController.deleteAchievement(id);

      return true;
    } catch (e) {
      _errorService.handleError(e);
      return false;
    }
  }

  Future<void> downloadCertificate(Achievement achievement) async {
    try {
      if (achievement.certificateUrl == null) {
        throw Exception('لا يوجد شهادة لتحميلها');
      }

      // هنا يمكنك إضافة كود لتحميل الشهادة
      // مثلاً باستخدام url_launcher أو dio

      Get.snackbar(
        'نجاح',
        'تم تحميل الشهادة بنجاح',
        snackPosition: SnackPosition.BOTTOM,
      );
    } catch (e) {
      _errorService.handleError(e);
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء تحميل الشهادة',
        snackPosition: SnackPosition.BOTTOM,
      );
    }
  }
}

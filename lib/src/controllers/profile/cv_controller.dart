import 'dart:io';
import 'package:almashal/src/core/values/app_config.dart';
import 'package:almashal/src/data/providers/api/api_provider.dart';
import 'package:almashal/src/data/services/auth_service.dart';
import 'package:dio/dio.dart' as dio;
import 'package:get/get.dart';
import 'package:file_picker/file_picker.dart';
import '../../data/models/profile/cv.dart';
import '../../controllers/profile_controller.dart';
import '../../data/services/error_service.dart';
import 'package:flutter/material.dart';

class CVController extends GetxController {
  final _profileController = Get.find<ProfileController>();
  final _errorService = Get.find<ErrorService>();

  final isLoading = false.obs;
  final isUploading = false.obs;
  final cvList = <CV>[].obs;
  final uploadProgress = 0.0.obs;
  ApiProvider get apiProvider => AppConfig.authenticatedApiProvider;

  // إضافة معرف المستخدم الحالي
  final userId = RxString('');

  @override
  void onInit() {
    super.onInit();
    ever(_profileController.cvs, (List<CV> cvs) {
      cvList.value = cvs;
    });
  }

  // تعيين معرف المستخدم وتحميل السير الذاتية
  void setUserId(String newUserId) {
    userId.value = newUserId;
    // loadCVs();
  }

  bool _validateCVData(PlatformFile file, String title) {
    if (title.isEmpty) {
      _errorService.handleError('يجب إدخال عنوان السيرة الذاتية');
      return false;
    }

    if (title.length < 2) {
      _errorService.handleError('عنوان السيرة الذاتية قصير جداً');
      return false;
    }

    if (title.length > 100) {
      _errorService.handleError('عنوان السيرة الذاتية طويل جداً');
      return false;
    }

    // التحقق من نوع الملف
    final allowedExtensions = ['pdf', 'doc', 'docx'];
    final fileExtension = file.extension?.toLowerCase();
    if (fileExtension == null || !allowedExtensions.contains(fileExtension)) {
      _errorService
          .handleError('نوع الملف غير مدعوم. الأنواع المدعومة: PDF, DOC, DOCX');
      return false;
    }

    // التحقق من حجم الملف (مثلاً: 10 ميجابايت كحد أقصى)
    final maxSize = 10 * 1024 * 1024; // 10MB in bytes
    if (file.size > maxSize) {
      _errorService
          .handleError('حجم الملف كبير جداً. الحد الأقصى: 10 ميجابايت');
      return false;
    }

    return true;
  }

  Future<void> loadCVs() async {
    isLoading.value = true;
    try {
      await _profileController.loadCVs(
          userId: userId.value, forceRefresh: true);
    } catch (e) {
      _errorService.handleError(e);
    } finally {
      isLoading.value = false;
    }
  }

  Future<bool> uploadCV(
      {required String type, String? file, String? text}) async {
    // if (!_validateCVData(file, title)) {
    //   return false;
    // }

    isUploading.value = true;
    uploadProgress.value = 0.0;

    try {
      final formData = dio.FormData.fromMap({
        'cv_type': type,
        if (type != "text")
          'cv_file': file == null
              ? null
              : await dio.MultipartFile.fromFile(
                  file,
                ),
        if (type == "text") 'cv_text': text,
      });
      var response = await apiProvider.updateCv(formData);

      var cv_type = response.data['cv_type'];
      var cv_text = response.data['cv_text'];
      var cv_file = response.data['cv_file'];

      AuthService.instance.userData.value?.user.cvType = cv_type;
      AuthService.instance.userData.value?.user.cvText = cv_text;
      AuthService.instance.userData.value?.user.cvFile = cv_file;
      AuthService.instance.storeUserData(AuthService.instance.userData.value!);

      print(response.data);
      // var cvType=response.data['cv'];
      // AuthService.instance.userData.value?.user.cvFile = response.data['cv'];

      // await loadCVs();
      Get.snackbar(
        'نجاح',
        'تم رفع السيرة الذاتية بنجاح',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.green,
        colorText: Colors.white,
      );
      return true;
    } catch (e) {
      _errorService.handleError(e);
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء رفع السيرة الذاتية: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
      return false;
    } finally {
      isUploading.value = false;
      uploadProgress.value = 0.0;
    }
  }

  Future<bool> updateCVSettings(
      String cvId, bool isPublic, bool isDefault) async {
    isLoading.value = true;
    try {
      await _profileController.updateCVSettings(cvId, isPublic, isDefault);
      await loadCVs();
      Get.snackbar(
        'نجاح',
        'تم تحديث إعدادات السيرة الذاتية بنجاح',
        snackPosition: SnackPosition.BOTTOM,
      );
      return true;
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> deleteCV(String cvId) async {
    isLoading.value = true;
    try {
      await _profileController.deleteCV(cvId);
      await loadCVs();
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> downloadCV(CV cv) async {
    if (isLoading.value) {
      // Prevent multiple simultaneous operations
      Get.snackbar(
        'انتظر',
        'جاري تنفيذ عملية أخرى، يرجى الانتظار',
        snackPosition: SnackPosition.BOTTOM,
      );
      return;
    }

    isLoading.value = true;
    try {
      // Open the CV in browser instead of downloading
      await _profileController.openCVInBrowser(cv);
    } catch (e) {
      // Error is already handled in the ProfileService
      Get.log('Error in CVController.downloadCV: $e');
    } finally {
      isLoading.value = false;
    }
  }

  // طريقة اختيار وتحميل ملف السيرة الذاتية
  Future<void> pickAndUploadCV() async {
    try {
      final result = await FilePicker.platform.pickFiles(
        type: FileType.custom,
        allowedExtensions: ['pdf', 'doc', 'docx'],
      );

      if (result != null && result.files.isNotEmpty) {
        final file = result.files.first;

        // عرض مربع حوار التفاصيل قبل التحميل
        Get.dialog(
          AlertDialog(
            title: const Text('تحميل سيرة ذاتية جديدة'),
            content: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                TextField(
                  decoration: const InputDecoration(
                    labelText: 'عنوان السيرة الذاتية',
                  ),
                  onChanged: (value) => _cvTitle = value,
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Checkbox(
                      value: _isPublic,
                      onChanged: (value) => _isPublic = value ?? false,
                    ),
                    const Text('عام'),
                  ],
                ),
                Row(
                  children: [
                    Checkbox(
                      value: _isDefault,
                      onChanged: (value) => _isDefault = value ?? false,
                    ),
                    const Text('افتراضي'),
                  ],
                ),
              ],
            ),
            actions: [
              TextButton(
                onPressed: () => Get.back(),
                child: const Text('إلغاء'),
              ),
              TextButton(
                onPressed: () {
                  Get.back();
                  // uploadCV(file, _cvTitle, _isPublic, _isDefault);
                },
                child: const Text('تحميل'),
              ),
            ],
          ),
        );
      }
    } catch (e) {
      _errorService.handleError(e);
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء اختيار الملف: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );
    }
  }

  // متغيرات مؤقتة لتخزين بيانات التحميل
  String _cvTitle = '';
  bool _isPublic = true;
  bool _isDefault = false;
}

import 'package:almashal/src/core/values/app_config.dart';
import 'package:almashal/src/data/models/news.dart';
import 'package:almashal/src/data/models/responses/news_response.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';

import '../core/utils/common_functions.dart';
import '../data/enums/page_loading_status.dart';
import '../data/services/network_service.dart';

class NewsController extends GetxController {
  Rx<List<News>> news = Rx<List<News>>([]);
  var pageLoadingStatus = PageLoadingStatus.done.obs;
  var apiProvider = AppConfig.apiProvider;
  int page = 0;
  int lastPage = 0;

  @override
  void onInit() {
    getData();
    super.onInit();
  }

  // get tree image
  void getData() {
    pageLoadingStatus.value = PageLoadingStatus.loading;
    NetworkService.instance.checkConnectivity(
      () async {
        try {
          NewsResponse response;
          if (searchTerm.value == null || searchTerm.value!.isEmpty) {
            response = await apiProvider.getNews();
          } else {
            response =
                await apiProvider.searchInNews(search: searchTerm.value!);
          }
          if (kDebugMode) {
            print(response.toJson());
          }
          news.value = response.news;
          page = response.currentPage;
          lastPage = response.lastPage;
          pageLoadingStatus.value = PageLoadingStatus.done;
        } catch (err) {
          CommonFunctions.handleError(err);
          pageLoadingStatus.value = PageLoadingStatus.failed;
        }
      },
      () {
        pageLoadingStatus.value = PageLoadingStatus.networkError;
      },
    );
  }

  // load more
  void loadMore() {
    print('loading more...');
    if (!cantLoadMore()) {
      NetworkService.instance.checkConnectivity(
        () async {
          try {
            NewsResponse response;
            if (searchTerm.value == null || searchTerm.value!.isEmpty) {
              response = await apiProvider.getNews(page: page + 1);
            } else {
              response = await apiProvider.searchInNews(
                  search: searchTerm.value!, page: page + 1);
            }
            news.value.addAll(response.news);
            news.refresh();
            page = response.currentPage;
            lastPage = response.lastPage;
            pageLoadingStatus.value = PageLoadingStatus.done;
          } catch (err) {
            CommonFunctions.handleError(err);
          }
        },
        () {},
      );
    }
  }

  bool cantLoadMore() {
    return page == lastPage;
  }

  Rx<String?> searchTerm = Rx<String?>(null);

  void search(String value) {
    searchTerm.value = value;
    page = 0;
    lastPage = 0;
    getData();
  }

  void clearSearch() {
    searchTerm.value = null;
    page = 0;
    lastPage = 0;
    getData();
  }
}

import 'package:almashal/src/core/values/app_config.dart';
import 'package:almashal/src/data/models/comment.dart';
import 'package:almashal/src/data/providers/api/api_provider.dart';
import 'package:almashal/src/data/services/auth_service.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../core/utils/common_functions.dart';
import '../data/enums/page_loading_status.dart';
import '../data/services/network_service.dart';

class CommentsController extends GetxController {
  CommentsController({required this.type, required this.id, this.parentId});
  Rx<List<Comment>> comments = Rx<List<Comment>>([]);
  var pageLoadingStatus = PageLoadingStatus.done.obs;
  var repliesLoadingStatus = PageLoadingStatus.done.obs;
  ApiProvider get apiProvider => AuthService.instance.isAuthenticated()
      ? AppConfig.authenticatedApiProvider
      : AppConfig.apiProvider;
  int page = 0;
  int lastPage = 0;
  int repliesPage = 0;
  int repliesLastPage = 0;
  final String type;
  final int id;
  final int? parentId;
  final replyTextController = TextEditingController();
  @override
  void onInit() {
    getData();
    super.onInit();
  }

  // get tree image
  void getData() {
    pageLoadingStatus.value = PageLoadingStatus.loading;
    NetworkService.instance.checkConnectivity(
      () async {
        try {
          var response = await apiProvider.getComments(id: id, type: type);
          if (kDebugMode) {
            print(response.toJson());
          }
          comments.value = response.data;
          page = response.currentPage;
          lastPage = response.lastPage;
          pageLoadingStatus.value = PageLoadingStatus.done;
        } catch (err) {
          CommonFunctions.handleError(err);
          pageLoadingStatus.value = PageLoadingStatus.failed;
        }
      },
      () {
        pageLoadingStatus.value = PageLoadingStatus.networkError;
      },
    );
  }

  // load more
  void loadMore() {
    if (kDebugMode) {
      print('loading more...');
    }
    if (!cantLoadMore()) {
      NetworkService.instance.checkConnectivity(
        () async {
          try {
            var response = await apiProvider.getComments(
              page: page + 1,
              id: id,
              type: type,
            );

            comments.value.addAll(response.data);
            comments.refresh();
            page = response.currentPage;
            lastPage = response.lastPage;
            pageLoadingStatus.value = PageLoadingStatus.done;
          } catch (err) {
            if (kDebugMode) {
              print(err);
            }
            CommonFunctions.handleError(err);
          }
        },
        () {},
      );
    }
  }

  bool cantLoadMore() {
    return page == lastPage;
  }

  // get replies for a comment
  bool cantLoadMoreReplies() {
    return repliesPage == repliesLastPage;
  }

  // like a comment
  Future<void> likeComment(int commentId) async {
    await NetworkService.instance.checkConnectivity(
      () async {
        try {
          // استخدام واجهة API المحدثة لتتوافق مع الصور
          var response =
              await AppConfig.authenticatedApiProvider.likeComment(commentId);
          // CommonFunctions.showSuccessMessage(response.message);
        } catch (err) {
          CommonFunctions.handleError(err);
        }
      },
    );
  }

  // إضافة دالة جديدة لإلغاء الإعجاب
  Future<void> unlikeComment(int commentId) async {
    await NetworkService.instance.checkConnectivity(
      () async {
        try {
          // استخدام واجهة API الجديدة لإلغاء الإعجاب
          var response =
              await AppConfig.authenticatedApiProvider.unlikeComment(commentId);
          // CommonFunctions.showSuccessMessage(response.message);
        } catch (err) {
          CommonFunctions.handleError(err);
        }
      },
    );
  }

  Future<void> addComment(Map<String, dynamic> body) async {
    await NetworkService.instance.checkConnectivity(
      () async {
        try {
          var response = await AppConfig.authenticatedApiProvider.comment(body);

          getData();
          Get.back();
          // عرض رسالة النجاح
          CommonFunctions.showSuccessMessage(response.message);

          // تفريغ حقل النص
          replyTextController.clear();
        } catch (err) {
          print("حدث خطأ أثناء إضافة التعليق/الرد: $err");
          CommonFunctions.handleError(err);
        }
      },
    );
  }

  // delete a comment
  Future<void> deleteComment(int commentId) async {
    await NetworkService.instance.checkConnectivity(
      () async {
        try {
          var response =
              await AppConfig.authenticatedApiProvider.deleteComment(commentId);
          CommonFunctions.showSuccessMessage(response.message);
          // remove comment from the list

          // remove from comments list
          final index = comments.value.indexWhere((c) => c.id == commentId);
          if (index != -1) {
            comments.value.removeAt(index);
            comments.refresh();
          }
        } catch (err) {
          CommonFunctions.handleError(err);
        }
      },
    );
  }

  // دالة مساعدة للتحقق مما إذا كان التعليق معجب به بالفعل

  @override
  void onClose() {
    replyTextController.dispose();
    super.onClose();
  }
}

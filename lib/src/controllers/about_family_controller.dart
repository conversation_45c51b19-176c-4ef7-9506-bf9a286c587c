import 'package:almashal/src/core/values/app_config.dart';
import 'package:get/get.dart';

import '../core/utils/common_functions.dart';
import '../data/enums/page_loading_status.dart';
import '../data/models/responses/about_family_response.dart';
import '../data/services/network_service.dart';

class AboutFamilyController extends GetxController {
  Rx<AboutFamilyResponse?> aboutFamily = Rx<AboutFamilyResponse?>(null);
  var pageLoadingStatus = PageLoadingStatus.done.obs;
  var apiProvider = AppConfig.apiProvider;
  Rx<String?> shareText = Rx<String?>(null);
  Rx<String?> shareLink = Rx<String?>(null);

  @override
  void onInit() {
    getData();
    super.onInit();
  }

  // get tree image
  void getData() {
    pageLoadingStatus.value = PageLoadingStatus.loading;
    NetworkService.instance.checkConnectivity(
      () async {
        try {
          var response = await apiProvider.getAboutFamily();
          aboutFamily.value = response;
          shareText.value = CommonFunctions.htmlToShareableText(
            aboutFamily.value?.about,
          );
          shareLink.value = aboutFamily.value?.shareLink;
          pageLoadingStatus.value = PageLoadingStatus.done;
        } catch (err) {
          CommonFunctions.handleError(err);
          pageLoadingStatus.value = PageLoadingStatus.failed;
        }
      },
      () {
        pageLoadingStatus.value = PageLoadingStatus.networkError;
      },
    );
  }
}

import 'package:almashal/src/data/services/error_service.dart';
import 'package:almashal/src/data/services/network_service.dart';
import 'package:flutter/services.dart';
import 'package:get/get.dart';

import '../core/routes/app_pages.dart';
import '../data/services/auth_service.dart';

class SplashController extends GetxController {
  SplashController();
  @override
  Future<void> onInit() async {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual, overlays: []);

    super.onInit();
  }

  @override
  void onClose() {
    SystemChrome.setEnabledSystemUIMode(SystemUiMode.manual, overlays: [
      SystemUiOverlay.top,
      SystemUiOverlay.bottom,
    ]);

    super.onClose();
  }

  @override
  Future<void> onReady() async {
    Future.delayed(
      const Duration(seconds: 2),
      () {
        Get.offAllNamed(Routes.HOME_PAGE);
      },
    );
    super.onReady();
  }
}

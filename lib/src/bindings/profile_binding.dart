// import 'package:get/get.dart';
// import '../controllers/profile/achievements_controller.dart';
// import '../controllers/profile/experiences_controller.dart';
// import '../controllers/profile/skills_controller.dart';
// import '../controllers/profile/cv_controller.dart';
// import '../controllers/profile/gallery_controller.dart';
// import '../data/services/profile_service.dart';
// import '../data/providers/api/api_provider.dart';

// class ProfileBinding extends Bindings {
//   @override
//   void dependencies() {
//     // Register services if not already registered
//     if (!Get.isRegistered<ProfileService>()) {
//       Get.lazyPut<ProfileService>(
//           () => ProfileService(Get.find<ApiProvider>()));
//     }

//     // Register controllers
//     Get.lazyPut<ExperiencesController>(() => ExperiencesController());
//     Get.lazyPut<AchievementsController>(() => AchievementsController());
//     Get.lazyPut<SkillsController>(() => SkillsController());
//     Get.lazyPut<CVController>(() => CVController());
//     Get.lazyPut<GalleryController>(() => GalleryController());
//   }
// }

class Skill {
  final int id;
  final String title;
  final String description;
  final int level;
  Skill({
    required this.id,
    required this.title,
    required this.description,
    required this.level,
  });

  factory Skill.fromJson(Map<String, dynamic> json) {
    return Skill(
      id: int.parse(json['id'].toString()),
      title: json['name'],
      description: json['description'] ?? '',
      level: json['level'] ?? 1,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'name': title,
      'description': description,
      'level': level,
    };
  }

  Skill copyWith({
    int? id,
    String? title,
    String? description,
    int? level,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Skill(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      level: level ?? this.level,
    );
  }
}

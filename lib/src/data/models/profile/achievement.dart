class Achievement {
  final String id;
  final String title;
  final String description;
  final DateTime date;
  final String? certificateUrl;
  final String? imageUrl;
  final String type; // شهادة، جائزة، إنجاز شخصي
  final DateTime createdAt;
  final DateTime? updatedAt;

  Achievement({
    required this.id,
    required this.title,
    required this.description,
    required this.date,
    this.certificateUrl,
    this.imageUrl,
    required this.type,
    required this.createdAt,
    this.updatedAt,
  });

  factory Achievement.fromJson(Map<String, dynamic> json) {
    return Achievement(
      id: json['id'] as String,
      title: json['title'] as String,
      description: json['description'] as String,
      date: DateTime.parse(json['date'] as String),
      certificateUrl: json['certificate_url'] as String?,
      imageUrl: json['image_url'] as String?,
      type: json['type'] as String,
      createdAt: DateTime.parse(json['created_at'] as String),
      updatedAt: json['updated_at'] != null
          ? DateTime.parse(json['updated_at'] as String)
          : null,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'description': description,
      'date': date.toIso8601String(),
      'certificate_url': certificateUrl,
      'image_url': imageUrl,
      'type': type,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt?.toIso8601String(),
    };
  }

  Achievement copyWith({
    String? id,
    String? title,
    String? description,
    DateTime? date,
    String? certificateUrl,
    String? imageUrl,
    String? type,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return Achievement(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      date: date ?? this.date,
      certificateUrl: certificateUrl ?? this.certificateUrl,
      imageUrl: imageUrl ?? this.imageUrl,
      type: type ?? this.type,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

class CV {
  final String id;
  final String title;
  final String fileUrl;
  final String fileType;
  final bool isDefault;
  final bool isPublic;
  final DateTime createdAt;
  final DateTime updatedAt;

  CV({
    required this.id,
    required this.title,
    required this.fileUrl,
    required this.fileType,
    required this.isDefault,
    required this.isPublic,
    required this.createdAt,
    required this.updatedAt,
  });

  factory CV.fromJson(Map<String, dynamic> json) {
    return CV(
      id: json['id'],
      title: json['title'],
      fileUrl: json['file_url'],
      fileType: json['file_type'],
      isDefault: json['is_default'] ?? false,
      isPublic: json['is_public'] ?? true,
      createdAt: DateTime.parse(json['created_at']),
      updatedAt: DateTime.parse(json['updated_at']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'file_url': fileUrl,
      'file_type': fileType,
      'is_default': isDefault,
      'is_public': isPublic,
      'created_at': createdAt.toIso8601String(),
      'updated_at': updatedAt.toIso8601String(),
    };
  }

  CV copyWith({
    String? id,
    String? title,
    String? fileUrl,
    String? fileType,
    bool? isDefault,
    bool? isPublic,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return CV(
      id: id ?? this.id,
      title: title ?? this.title,
      fileUrl: fileUrl ?? this.fileUrl,
      fileType: fileType ?? this.fileType,
      isDefault: isDefault ?? this.isDefault,
      isPublic: isPublic ?? this.isPublic,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }
}

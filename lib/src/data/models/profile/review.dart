import 'package:almashal/src/data/models/comment_user.dart';

class Review {
  final int id;
  final int rating;
  final String comment;
  final String createdAt;
  final CommentUser reviewer;

  Review({
    required this.id,
    required this.rating,
    required this.comment,
    required this.createdAt,
    required this.reviewer,
  });

  factory Review.fromJson(Map<String, dynamic> json) {
    return Review(
      id: json['id'],
      rating: json['rating'],
      comment: json['comment'],
      createdAt: json['created_at'],
      reviewer: CommentUser.from<PERSON>son(json['reviewer']),
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'rating': rating,
      'comment': comment,
      'created_at': createdAt,
      'reviewer': reviewer.toJson(),
    };
  }
} 
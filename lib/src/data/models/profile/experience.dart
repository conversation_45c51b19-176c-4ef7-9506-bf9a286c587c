class Experience {
  final int id;
  final String title;
  final String organization;
  final DateTime startDate;
  final DateTime? endDate;
  final String description;
  final String type; // وظيفة، تدريب، تطوع
  final String typeLabel; // وظيفة، تدريب، تطوع
  final String? location;
  final bool isCurrentPosition;

  Experience({
    required this.id,
    required this.title,
    required this.organization,
    required this.startDate,
    this.endDate,
    required this.description,
    required this.type,
    required this.typeLabel,
    this.location,
    required this.isCurrentPosition,
  });

  factory Experience.fromJson(Map<String, dynamic> json) {
    return Experience(
      id: int.parse(json['id'].toString()),
      title: json['job_title'] as String,
      organization: json['company_name'] as String,
      startDate: DateTime.parse(json['start_date'].toString()),
      endDate: json['end_date'] != null
          ? DateTime.parse(json['end_date'].toString())
          : null,
      description: json['description'] as String,
      type: json['type'] as String,
      typeLabel: json['type_label'] as String,
      location: json['location'] as String?,
      isCurrentPosition: json['is_current_position'] as bool,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'title': title,
      'company_name': organization,
      'start_date': startDate.toIso8601String(),
      'end_date': endDate?.toIso8601String(),
      'description': description,
      'type': type,
      'location': location,
      'is_current_position': isCurrentPosition,
    };
  }

  Experience copyWith({
    int? id,
    String? title,
    String? organization,
    DateTime? startDate,
    DateTime? endDate,
    String? description,
    String? type,
    String? typeLabel,
    String? location,
    bool? isCurrentPosition,
  }) {
    return Experience(
      id: id ?? this.id,
      title: title ?? this.title,
      organization: organization ?? this.organization,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      description: description ?? this.description,
      type: type ?? this.type,
      typeLabel: typeLabel ?? this.typeLabel,
      location: location ?? this.location,
      isCurrentPosition: isCurrentPosition ?? this.isCurrentPosition,
    );
  }
}

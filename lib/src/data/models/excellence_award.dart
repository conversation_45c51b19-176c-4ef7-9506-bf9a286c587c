class ExcellenceAward {
  int id;
  String title;
  String content;
  String createdAt;
  String? shareLink;
  int visitsCount;

  ExcellenceAward({
    required this.id,
    required this.title,
    required this.content,
    required this.createdAt,
    required this.shareLink,
    this.visitsCount = 0,
  });

  factory ExcellenceAward.fromJson(Map<String, dynamic> json) =>
      ExcellenceAward(
        id: json["id"],
        title: json["title"],
        content: json["content"],
        createdAt: json["created_at"],
        shareLink: json["share_link"],
        visitsCount: json["visits_count"] ?? 0,
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "title": title,
        "content": content,
        "created_at": createdAt,
        "share_link": shareLink,
        "visits_count": visitsCount,
      };
}

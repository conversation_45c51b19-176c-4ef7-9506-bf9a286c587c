import 'media.dart';

class Album {
  Album({
    required this.id,
    required this.title,
    required this.image,
    required this.media,
    required this.viewType,
    required this.createdAt,
    required this.publishedAt,
    required this.shareLink,
    this.visitsCount = 0,
  });

  int id;
  String title;
  String image;
  int viewType;
  List<Media> media;
  DateTime createdAt;
  String publishedAt;
  String? shareLink;
  int visitsCount;

  factory Album.fromJson(Map<String, dynamic> json) => Album(
        id: json["id"],
        title: json["title"],
        image: json["image"],
        viewType: json["view_type"],
        media: List<Media>.from(json["media"].map((x) => Media.fromJson(x))),
        createdAt: DateTime.parse(json["created_at"]),
        publishedAt: json["published_at"],
        shareLink: json["share_link"],
        visitsCount: json["visits_count"] ?? 0,
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "title": title,
        "image": image,
        "view_type": viewType,
        "media": List<dynamic>.from(media.map((x) => x.toJson())),
        "created_at": createdAt.toIso8601String(),
        "published_at": publishedAt,
        "share_link": shareLink,
        "visits_count": visitsCount,
      };
}

class City {
  City({
    required this.id,
    required this.name,
    required this.countryId,
  });

  int id;
  String name;
  int countryId;

  factory City.fromJson(Map<String, dynamic> json) => City(
        id: json["id"],
        name: json["name"],
        countryId: json["country_id"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "country_id": countryId,
      };
}

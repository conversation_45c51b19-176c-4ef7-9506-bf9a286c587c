class SocialMediaLink {
  int id;
  String name;
  String icon;
  String url;

  SocialMediaLink({
    required this.id,
    required this.name,
    required this.icon,
    required this.url,
  });

  factory SocialMediaLink.fromJson(Map<String, dynamic> json) =>
      SocialMediaLink(
        id: json["id"],
        name: json["name"],
        icon: json["icon"],
        url: json["url"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "icon": icon,
        "url": url,
      };
}

import 'package:almashal/src/data/models/comment_user.dart';
import 'package:almashal/src/data/models/like.dart';

class Comment {
  int id;
  String body;
  CommentUser? user;
  String createdAt;
  List<Comment>? replies;
  List<Like>? likes;
  int? parentId;
  int likesCount;
  int commentableId;
  String commentableType;
  int get repliesCount => replies?.length ?? 0;
  bool liked;

  Comment({
    required this.id,
    required this.body,
    this.user,
    required this.createdAt,
    this.replies,
    this.likes,
    this.parentId,
    this.likesCount = 0,
    this.liked = false,
    required this.commentableId,
    required this.commentableType,
  });

  factory Comment.fromJson(Map<String, dynamic> json) => Comment(
        id: json["id"],
        body: json["body"],
        user: json["user"] == null ? null : CommentUser.fromJson(json["user"]),
        createdAt: json["created_at"],
        replies: json["replies"] == null
            ? null
            : List<Comment>.from(
                json["replies"].map((x) => Comment.fromJson(x))),
        parentId: json["parent_id"],
        likesCount: json["likes_count"] ?? 0,
        liked: json["liked"] ?? false,
        commentableId: json["commentable_id"],
        commentableType: json["commentable_type"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "body": body,
        "user": user?.toJson(),
        "created_at": createdAt,
        "replies": replies == null
            ? null
            : List<dynamic>.from(replies!.map((x) => x.toJson())),
        "parent_id": parentId,
        "likes_count": likesCount,
        "replies_count": repliesCount,
        "liked": liked,
        "commentable_id": commentableId,
        "commentable_type": commentableType,
      };
}

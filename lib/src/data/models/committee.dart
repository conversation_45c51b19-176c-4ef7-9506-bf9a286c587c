class Committee {
  Committee({
    required this.id,
    required this.name,
    required this.content,
  });

  int id;
  String name;
  String content;

  factory Committee.fromJson(Map<String, dynamic> json) => Committee(
        id: json["id"],
        name: json["name"],
        content: json["content"],
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "content": content,
      };
}

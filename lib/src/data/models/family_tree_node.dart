class FamilyTreeNode {
  int id;
  String name;
  String? nickName;
  String? overview;
  String? image;
  String? thumbImageUrl;
  int? order;
  int? gender;
  int? alive;
  String? birthDate;
  String? deathDate;
  String? birthPlace;
  String? deathPlace;
  String? job;
  String? address;
  int? familyMemberId;
  List<FamilyTreeNode>? children;
  FamilyTreeNode? parent;

  FamilyTreeNode({
    required this.id,
    required this.name,
    required this.nickName,
    required this.overview,
    required this.image,
    required this.thumbImageUrl,
    required this.order,
    required this.gender,
    required this.alive,
    required this.birthDate,
    required this.deathDate,
    required this.birthPlace,
    required this.deathPlace,
    required this.job,
    required this.address,
    this.familyMemberId,
    required this.children,
    required this.parent,
  });

  factory FamilyTreeNode.fromJson(Map<String, dynamic> json) => FamilyTreeNode(
        id: json["id"],
        name: json["name"],
        nickName: json["nick_name"],
        overview: json["overview"],
        image: json["image"],
        thumbImageUrl: json["thumb_image_url"],
        order: json["order"],
        gender: json["gender"],
        alive: json["alive"],
        birthDate: json["birth_date"],
        deathDate: json["death_date"],
        birthPlace: json["birth_place"],
        deathPlace: json["death_place"],
        job: json["job"],
        address: json["address"],
        familyMemberId: json["family_member_id"] == null
            ? null
            : int.parse(json["family_member_id"].toString()),
        children: List<FamilyTreeNode>.from(
            (json["children"] ?? []).map((x) => FamilyTreeNode.fromJson(x))),
        parent: json["parent"] == null
            ? null
            : FamilyTreeNode.fromJson(json["parent"]),
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "name": name,
        "nick_name": nickName,
        "overview": overview,
        "image": image,
        "thumb_image_url": thumbImageUrl,
        "order": order,
        "gender": gender,
        "alive": alive,
        "birth_date": birthDate,
        "death_date": deathDate,
        "birth_place": birthPlace,
        "death_place": deathPlace,
        "job": job,
        "address": address,
        "family_member_id": familyMemberId,
      };
}

class CommentVm {
    String body;
    int commentableId;
    String commentableType;

    CommentVm({
        required this.body,
        required this.commentableId,
        required this.commentableType,
    });

    factory CommentVm.fromJson(Map<String, dynamic> json) => CommentVm(
        body: json["body"],
        commentableId: json["commentable_id"],
        commentableType: json["commentable_type"],
    );

    Map<String, dynamic> toJson() => {
        "body": body,
        "commentable_id": commentableId,
        "commentable_type": commentableType,
    };
}
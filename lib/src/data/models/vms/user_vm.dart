import 'dart:io';

import 'package:almashal/src/core/utils/platform_helper.dart';
import 'package:dio/dio.dart' as dio;
import 'package:flutter/foundation.dart';
import 'package:get/get.dart' hide FormData, MultipartFile;

class UserVM {
  UserVM({
    this.fullName,
    this.firstName,
    this.middleName,
    this.lastName,
    this.grandfatherName,
    this.branchId,
    this.gender = 1,
    this.mobile,
    this.countryCode,
    this.email,
    this.password,
    this.passwordConfirmation,
    this.birthDate,
    this.birthPlace,
    this.countryId,
    this.cityId,
    this.address,
    this.overview,
    this.image,
    this.facebookLink,
    this.xLink,
    this.snapshotLink,
    this.instagramLink,
    this.tiktokLink,
    this.linkedinLink,
    this.youtubeLink,
  });

  String? fullName;
  String? firstName;
  String? middleName;
  String? lastName;
  String? grandfatherName;
  int? branchId;
  int? gender;
  String? mobile;
  String? countryCode;
  String? email;
  String? password;
  String? passwordConfirmation;
  DateTime? birthDate;
  String? birthPlace;
  int? countryId;
  int? cityId;
  String? address;
  String? overview;
  dynamic image;
  String? facebookLink;
  String? xLink;
  String? snapshotLink;
  String? instagramLink;
  String? tiktokLink;
  String? linkedinLink;
  String? youtubeLink;

  factory UserVM.fromJson(Map<String, dynamic> json) => UserVM(
        fullName: json["full_name"],
        firstName: json["first_name"],
        middleName: json["middle_name"],
        lastName: json["last_name"],
        grandfatherName: json["grandfather_name"],
        branchId: json["branch_id"],
        gender: json["gender"],
        mobile: json["mobile"],
        countryCode: json["country_code"],
        email: json["email"],
        birthDate: json["birth_date"],
        birthPlace: json["birth_place"],
        countryId: json["country_id"],
        cityId: json["city_id"],
        address: json["address"],
        overview: json["overview"],
        facebookLink: json["facebook_link"],
        xLink: json["x_link"],
        snapshotLink: json["snapchat_link"],
        instagramLink: json["instagram_link"],
        tiktokLink: json["tiktok_link"],
        linkedinLink: json["linkedin_link"],
        youtubeLink: json["youtube_link"],
      );

  Map<String, dynamic> toJson() {
    Map<String, dynamic> jsonMap = {
      "full_name": fullName ??
          (firstName != null && middleName != null && lastName != null
              ? "$firstName $middleName $lastName"
              : null),
      "first_name": firstName,
      "middle_name": middleName,
      "last_name": lastName,
      "grandfather_name": grandfatherName,
      "branch_id": branchId,
      "gender": gender,
      "mobile": mobile,
      "email": email,
      "country_code": countryCode,
      "birth_date": birthDate?.toIso8601String(),
      "birth_place": birthPlace,
      "country_id": countryId,
      "city_id": cityId,
      "address": address,
      "overview": overview,
      "facebook_link": facebookLink,
      "x_link": xLink,
      "snapchat_link": snapshotLink,
      "instagram_link": instagramLink,
      "tiktok_link": tiktokLink,
      "linkedin_link": linkedinLink,
      "youtube_link": youtubeLink,
      "password": password,
      "password_confirmation": passwordConfirmation
    };

    // إضافة روابط التواصل الاجتماعي التي ليست فارغة فقط

    return jsonMap..removeWhere((key, value) => value == null);
  }

  Map<String, dynamic> toJsonForRegister() {
    Map<String, dynamic> jsonMap = {
      "full_name": fullName ??
          (firstName != null && middleName != null && lastName != null
              ? "$firstName $middleName $lastName"
              : null),
      "mobile": mobile,
      "country_code": countryCode,
      "password": password,
      "password_confirmation": passwordConfirmation
    };

    // إضافة روابط التواصل الاجتماعي التي ليست فارغة فقط

    return jsonMap;
  }

  Future<dio.FormData> toFormData([dynamic imageFile]) async {
    Map<String, dynamic> formMap = {
      "full_name": fullName ??
          (firstName != null && middleName != null && lastName != null
              ? "$firstName $middleName $lastName"
              : null),
      "first_name": firstName,
      "middle_name": middleName,
      "last_name": lastName,
      "grandfather_name": grandfatherName,
      "branch_id": branchId,
      "gender": gender,
      "mobile": mobile,
      "email": email,
      "birth_date": birthDate?.toIso8601String(),
      "birth_place": birthPlace,
      "country_id": countryId,
      "city_id": cityId,
      "address": address,
      "overview": overview,
      "facebook_link": facebookLink,
      "x_link": xLink,
      "snapchat_link": snapshotLink,
      "instagram_link": instagramLink,
      "tiktok_link": tiktokLink,
      "linkedin_link": linkedinLink,
      "youtube_link": youtubeLink,
    };

    if (imageFile != null) {
      if (PlatformHelper.isWeb) {
        try {
          List<int> imageBytes = await imageFile.readAsBytes();
          formMap["image"] = dio.MultipartFile.fromBytes(
            imageBytes,
            filename: 'image.jpg',
          );
        } catch (e) {
          if (kDebugMode) {
            print('Error processing web image: $e');
          }
        }
      } else {
        formMap["image"] =
            await dio.MultipartFile.fromFile((imageFile as File).path);
      }
    }

    return dio.FormData.fromMap(formMap);
  }
}

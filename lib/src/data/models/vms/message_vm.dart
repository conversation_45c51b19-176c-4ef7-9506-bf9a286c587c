class MessageVM {
  MessageVM({
    this.name,
    this.email,
    this.mobile,
    this.subject,
    this.message,
  });

  String? name;
  String? email;
  String? mobile;
  String? subject;
  String? message;

  factory MessageVM.fromJson(Map<String, dynamic> json) => MessageVM(
        name: json["name"],
        email: json["email"],
        mobile: json["mobile"],
        subject: json["subject"],
        message: json["message"],
      );

  Map<String, dynamic> toJson() => {
        "name": name,
        "email": email,
        "mobile": mobile,
        "subject": subject,
        "message": message,
      };
}

import '../user.dart';

class UserTokenVM {
  UserTokenVM({
    required this.user,
    required this.token,
  });

  User user;
  String token;

  factory UserTokenVM.fromJson(Map<String, dynamic> json) => UserTokenVM(
        user: User.from<PERSON>son(json["user"]),
        token: json["token"],
      );

  Map<String, dynamic> toJson() => {
        "user": user.toJson(),
        "token": token,
      };
}

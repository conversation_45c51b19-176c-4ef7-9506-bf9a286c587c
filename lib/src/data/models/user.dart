import 'profile/achievement.dart';
import 'profile/cv.dart';
import 'profile/experience.dart';
import 'profile/skill.dart';

class User {
  User({
    required this.id,
    required this.firstName,
    required this.middleName,
    required this.lastName,
    required this.grandfatherName,
    required this.branch,
    required this.branchId,
    required this.gender,
    required this.mobile,
    required this.email,
    this.image,
    this.coverImage,
    this.birthDate,
    this.birthPlace,
    required this.country,
    required this.countryId,
    required this.city,
    required this.cityId,
    this.address,
    this.skills = const [],
    this.experiences = const [],
    this.achievements = const [],
    this.cvList = const [],
    this.defaultCV,
    this.cvFile,
    this.overview,
    this.facebookLink,
    this.xLink,
    this.snapshotLink,
    this.instagramLink,
    this.tiktokLink,
    this.linkedinLink,
    this.youtubeLink,
    this.isPublicProfile = true,
    this.privacySettings = const {
      'skills': true,
      'experiences': true,
      'achievements': true,
      'cvs': true,
      'social_links': true,
    },
    this.cvText,
    this.cvType,
  });

  int id;
  String firstName;
  String middleName;
  String lastName;
  String grandfatherName;
  String? branch;
  int? branchId;
  int gender;
  String mobile;
  String email;
  String? image;
  String? coverImage;
  DateTime? birthDate;
  String? birthPlace;
  String country;
  int countryId;
  String city;
  int cityId;
  String? address;
  String? overview;
  List<Skill> skills;
  List<Experience> experiences;
  List<Achievement> achievements;
  List<CV> cvList;
  CV? defaultCV;
  String? cvFile;
  String? cvText;
  String? cvType;
  bool isPublicProfile;
  Map<String, bool> privacySettings;

  String? facebookLink;
  String? xLink;
  String? snapshotLink;
  String? instagramLink;
  String? tiktokLink;
  String? linkedinLink;
  String? youtubeLink;

  String get displayName => "$firstName $middleName $lastName";
  String? get cvFileUrl =>
      cvFile == null ? null : "https://almashalfamily.com/uploads/${cvFile!}";

  factory User.fromJson(Map<String, dynamic> json) {
    return User(
      id: json["id"] ?? 0,
      firstName: json["first_name"] ?? "",
      middleName: json["middle_name"] ?? "",
      lastName: json["last_name"] ?? "",
      grandfatherName: json["grandfather_name"] ?? "",
      branch: json["branch"] ?? "",
      branchId: json["branch_id"] == null
          ? null
          : int.parse(json["branch_id"].toString()),
      gender: _parseIntSafely(json["gender"]),
      mobile: json["mobile"] ?? "",
      email: json["email"] ?? "",
      image: json["image"],
      coverImage: json["cover_image"],
      birthDate: _parseDateSafely(json["birth_date"]),
      birthPlace: json["birth_place"],
      country: json["country"] ?? "",
      countryId: _parseIntSafely(json["country_id"]),
      city: json["city"] ?? "",
      cityId: _parseIntSafely(json["city_id"]),
      address: json["address"],
      overview: json["overview"],
      facebookLink: json["facebook_link"],
      xLink: json["x_link"],
      snapshotLink: json["snapchat_link"],
      instagramLink: json["instagram_link"],
      tiktokLink: json["tiktok_link"],
      linkedinLink: json["linkedin_link"],
      youtubeLink: json["youtube_link"],
      cvFile: json["cv_file"],
      cvText: json["cv_text"],
      cvType: json["cv_type"],
      skills: (json["skills"] as List<dynamic>?)
              ?.map((e) => Skill.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      experiences: (json["experiences"] as List<dynamic>?)
              ?.map((e) => Experience.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      achievements: (json["achievements"] as List<dynamic>?)
              ?.map((e) => Achievement.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      cvList: (json["cv_list"] as List<dynamic>?)
              ?.map((e) => CV.fromJson(e as Map<String, dynamic>))
              .toList() ??
          [],
      defaultCV: json["default_cv"] != null
          ? CV.fromJson(json["default_cv"] as Map<String, dynamic>)
          : null,
      isPublicProfile: json["is_public_profile"] ?? true,
      privacySettings: (json["privacy_settings"] as Map<String, dynamic>?)?.map(
            (key, value) => MapEntry(key, value as bool),
          ) ??
          {
            'skills': true,
            'experiences': true,
            'achievements': true,
            'cvs': true,
            'social_links': true,
          },
    );
  }

  static int _parseIntSafely(dynamic value) {
    if (value == null) return 0;
    if (value is int) return value;
    if (value is String) {
      return int.tryParse(value) ?? 0;
    }
    return 0;
  }

  static DateTime? _parseDateSafely(dynamic value) {
    if (value == null) return null;
    try {
      if (value is String) {
        return DateTime.parse(value);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  Map<String, dynamic> toJson() => {
        "id": id,
        "first_name": firstName,
        "middle_name": middleName,
        "last_name": lastName,
        "grandfather_name": grandfatherName,
        "branch": branch,
        "branch_id": branchId,
        "gender": gender,
        "mobile": mobile,
        "email": email,
        "image": image,
        "cover_image": coverImage,
        "cv_file": cvFile,
        "birth_date": birthDate != null
            ? "${birthDate!.year.toString().padLeft(4, '0')}-${birthDate!.month.toString().padLeft(2, '0')}-${birthDate!.day.toString().padLeft(2, '0')}"
            : null,
        "birth_place": birthPlace,
        "country": country,
        "country_id": countryId,
        "city": city,
        "city_id": cityId,
        "address": address,
        "overview": overview,
        "skills": skills.map((e) => e.toJson()).toList(),
        "experiences": experiences.map((e) => e.toJson()).toList(),
        "achievements": achievements.map((e) => e.toJson()).toList(),
        "cv_list": cvList.map((e) => e.toJson()).toList(),
        "default_cv": defaultCV?.toJson(),
        "is_public_profile": isPublicProfile,
        "privacy_settings": privacySettings,
      };
}

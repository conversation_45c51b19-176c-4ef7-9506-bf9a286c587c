class Occasion {
  Occasion({
    required this.id,
    required this.title,
    required this.content,
    required this.image,
    required this.images,
    required this.youtubeVideos,
    required this.date,
    required this.createdAt,
    required this.publishedAt,
    required this.shareLink,
    this.visitsCount = 0,
  });

  int id;
  String title;
  String content;
  String image;
  List<String> images;
  List<String> youtubeVideos;
  String date;
  DateTime createdAt;
  String publishedAt;
  String? shareLink;
  int visitsCount;

  factory Occasion.fromJson(Map<String, dynamic> json) => Occasion(
        id: json["id"],
        title: json["title"],
        content: json["content"],
        image: json["image"],
        images: List<String>.from(json["images"].map((x) => x)),
        youtubeVideos: List<String>.from(json["youtube_videos"].map((x) => x)),
        date: json["date"],
        createdAt: DateTime.parse(json["created_at"]),
        publishedAt: json["published_at"],
        shareLink: json["share_link"],
        visitsCount: json["visits_count"] ?? 0,
      );

  Map<String, dynamic> toJson() => {
        "id": id,
        "title": title,
        "content": content,
        "image": image,
        "images": List<dynamic>.from(images.map((x) => x)),
        "youtube_videos": List<dynamic>.from(youtubeVideos.map((x) => x)),
        "date": date,
        "created_at": createdAt.toIso8601String(),
        "published_at": publishedAt,
        "share_link": shareLink,
        "visits_count": visitsCount,
      };
}

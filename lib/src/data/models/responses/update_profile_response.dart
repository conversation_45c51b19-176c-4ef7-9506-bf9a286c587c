import 'package:almashal/src/data/models/user.dart';

class UpdateProfileResponse {
  String message;
  User user;

  UpdateProfileResponse({required this.message, required this.user});

  factory UpdateProfileResponse.fromJson(Map<String, dynamic> json) =>
      UpdateProfileResponse(
        message: json["message"],
        user: User.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "data": user.toJson(),
      };
}

class GeneralResponse {
  final bool success;
  final String message;
  final Map<String, dynamic>? data;

  GeneralResponse({
    required this.success,
    required this.message,
    this.data,
  });

  factory GeneralResponse.fromJson(Map<String, dynamic> json) {
    // Handle different data types for success field
    bool successValue = false;
    if (json['success'] is bool) {
      successValue = json['success'];
    } else if (json['success'] is int) {
      successValue = json['success'] == 1;
    } else if (json['success'] is String) {
      successValue = json['success'].toLowerCase() == 'true' || json['success'] == '1';
    }
    
    // Handle different data types for message field
    String messageValue = '';
    if (json['message'] != null) {
      messageValue = json['message'].toString();
    }
    
    // Handle data field which might not be a Map
    Map<String, dynamic>? dataValue;
    if (json['data'] is Map<String, dynamic>) {
      dataValue = json['data'];
    }
    
    return GeneralResponse(
      success: successValue,
      message: messageValue,
      data: dataValue,
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
      'data': data,
    };
  }
}

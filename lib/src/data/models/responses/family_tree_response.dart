class FamilyTreeResponse {
  FamilyTreeResponse({
    required this.treeImage,
    required this.treeShareText,
  });

  String treeImage;
  String treeShareText;

  factory FamilyTreeResponse.fromJson(Map<String, dynamic> json) =>
      FamilyTreeResponse(
        treeImage: json["tree_image"],
        treeShareText: json["tree_share_text"],
      );

  Map<String, dynamic> toJson() => {
        "tree_image": treeImage,
        "tree_share_text": treeShareText,
      };
}

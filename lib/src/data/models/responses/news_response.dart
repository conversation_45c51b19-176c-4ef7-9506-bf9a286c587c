import '../news.dart';

class NewsResponse {
    NewsResponse({
        required this.total,
        required this.currentPage,
        required this.lastPage,
        required this.perPage,
        required this.news,
    });

    int total;
    int currentPage;
    int lastPage;
    int perPage;
    List<News> news;

    factory NewsResponse.fromJson(Map<String, dynamic> json) => NewsResponse(
        total: json["total"],
        currentPage: json["current_page"],
        lastPage: json["last_page"],
        perPage: json["per_page"],
        news: List<News>.from(json["data"].map((x) => News.fromJson(x))),
    );

    Map<String, dynamic> toJson() => {
        "total": total,
        "current_page": currentPage,
        "last_page": lastPage,
        "per_page": perPage,
        "data": List<dynamic>.from(news.map((x) => x.toJson())),
    };
}



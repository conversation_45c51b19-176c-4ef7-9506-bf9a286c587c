class UpdateProfileCoverResponse {
  String? message;
  String? coverImage;

  UpdateProfileCoverResponse({
    this.message,
    this.coverImage,
  });

  factory UpdateProfileCoverResponse.fromJson(Map<String, dynamic> json) =>
      UpdateProfileCoverResponse(
        message: json["message"],
        coverImage: json["cover_image"],
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "cover_image": coverImage,
      };
}

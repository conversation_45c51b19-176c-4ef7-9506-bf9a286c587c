import 'package:almashal/src/data/models/comment.dart';

class CommentResponse {
  int total;
  int currentPage;
  int lastPage;
  int perPage;
  List<Comment> data;

  CommentResponse({
    required this.total,
    required this.currentPage,
    required this.lastPage,
    required this.perPage,
    required this.data,
  });

  factory CommentResponse.fromJson(Map<String, dynamic> json) =>
      CommentResponse(
        total: json["total"],
        currentPage: json["current_page"],
        lastPage: json["last_page"],
        perPage: json["per_page"],
        data: List<Comment>.from(json["data"].map((x) => Comment.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "total": total,
        "current_page": currentPage,
        "last_page": lastPage,
        "per_page": perPage,
        "data": List<dynamic>.from(data.map((x) => x.toJson())),
      };
}

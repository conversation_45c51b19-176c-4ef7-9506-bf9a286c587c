import '../occasion.dart';

class OccasionsResponse {
    OccasionsResponse({
        required this.total,
        required this.currentPage,
        required this.lastPage,
        required this.perPage,
        required this.occasions,
    });

    int total;
    int currentPage;
    int lastPage;
    int perPage;
    List<Occasion> occasions;

    factory OccasionsResponse.fromJson(Map<String, dynamic> json) => OccasionsResponse(
        total: json["total"],
        currentPage: json["current_page"],
        lastPage: json["last_page"],
        perPage: json["per_page"],
        occasions: List<Occasion>.from(json["data"].map((x) => Occasion.fromJson(x))),
    );

    Map<String, dynamic> toJson() => {
        "total": total,
        "current_page": currentPage,
        "last_page": lastPage,
        "per_page": perPage,
        "data": List<dynamic>.from(occasions.map((x) => x.toJson())),
    };
}


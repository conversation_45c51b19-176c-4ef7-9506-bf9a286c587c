import 'package:almashal/src/data/models/profile/achievement.dart';
import 'package:almashal/src/data/models/profile/cv.dart';
import 'package:almashal/src/data/models/profile/experience.dart';
import 'package:almashal/src/data/models/profile/skill.dart';

class ProfileResponse {
  final bool success;
  final String message;
  final dynamic data;

  ProfileResponse({
    required this.success,
    required this.message,
    this.data,
  });

  factory ProfileResponse.fromJson(Map<String, dynamic> json) {
    return ProfileResponse(
      success: json['success'] as bool,
      message: json['message'] as String,
      data: json['data'],
    );
  }
}

class ProfileSkillsResponse extends ProfileResponse {
  final List<Skill> skills;

  ProfileSkillsResponse({
    required super.success,
    required super.message,
    required this.skills,
  });

  factory ProfileSkillsResponse.fromJson(Map<String, dynamic> json) {
    return ProfileSkillsResponse(
      success: json['success'] as bool,
      message: json['message'] as String,
      skills: (json['data'] as List<dynamic>)
          .map((e) => Skill.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}

class ProfileExperiencesResponse extends ProfileResponse {
  final List<Experience> experiences;

  ProfileExperiencesResponse({
    required super.success,
    required super.message,
    required this.experiences,
  });

  factory ProfileExperiencesResponse.fromJson(Map<String, dynamic> json) {
    return ProfileExperiencesResponse(
      success: json['success'] as bool,
      message: json['message'] as String,
      experiences: (json['data'] as List<dynamic>)
          .map((e) => Experience.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}

class ProfileAchievementsResponse extends ProfileResponse {
  final List<Achievement> achievements;

  ProfileAchievementsResponse({
    required super.success,
    required super.message,
    required this.achievements,
  });

  factory ProfileAchievementsResponse.fromJson(Map<String, dynamic> json) {
    return ProfileAchievementsResponse(
      success: json['success'] as bool,
      message: json['message'] as String,
      achievements: (json['data'] as List<dynamic>)
          .map((e) => Achievement.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}

class ProfileCVsResponse extends ProfileResponse {
  final List<CV> cvs;

  ProfileCVsResponse({
    required super.success,
    required super.message,
    required this.cvs,
  });

  factory ProfileCVsResponse.fromJson(Map<String, dynamic> json) {
    return ProfileCVsResponse(
      success: json['success'] as bool,
      message: json['message'] as String,
      cvs: (json['data'] as List<dynamic>)
          .map((e) => CV.fromJson(e as Map<String, dynamic>))
          .toList(),
    );
  }
}

import '../album.dart';

class AlbumsResponse {
    AlbumsResponse({
        required this.total,
        required this.currentPage,
        required this.lastPage,
        required this.perPage,
        required this.albums,
    });

    int total;
    int currentPage;
    int lastPage;
    int perPage;
    List<Album> albums;

    factory AlbumsResponse.fromJson(Map<String, dynamic> json) => AlbumsResponse(
        total: json["total"],
        currentPage: json["current_page"],
        lastPage: json["last_page"],
        perPage: json["per_page"],
        albums: List<Album>.from(json["data"].map((x) => Album.fromJson(x))),
    );

    Map<String, dynamic> toJson() => {
        "total": total,
        "current_page": currentPage,
        "last_page": lastPage,
        "per_page": perPage,
        "data": List<dynamic>.from(albums.map((x) => x.toJson())),
    };
}

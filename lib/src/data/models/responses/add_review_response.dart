class AddReviewResponse {
  final bool success;
  final String message;

  AddReviewResponse({
    required this.success,
    required this.message,
  });

  factory AddReviewResponse.fromJson(Map<String, dynamic> json) {
    return AddReviewResponse(
      success: json['success'] ?? true,
      message: json['message'] ?? 'تمت إضافة المراجعة بنجاح',
    );
  }

  Map<String, dynamic> toJson() {
    return {
      'success': success,
      'message': message,
    };
  }
} 
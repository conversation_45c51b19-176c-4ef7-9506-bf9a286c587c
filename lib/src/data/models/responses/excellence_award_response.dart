import 'package:almashal/src/data/models/excellence_award.dart';

class ExcellenceAwardResponse {
  int total;
  int currentPage;
  int lastPage;
  int perPage;
  List<ExcellenceAward> excellenceAwards;

  ExcellenceAwardResponse({
    required this.total,
    required this.currentPage,
    required this.lastPage,
    required this.perPage,
    required this.excellenceAwards,
  });

  factory ExcellenceAwardResponse.fromJson(Map<String, dynamic> json) =>
      ExcellenceAwardResponse(
        total: json["total"],
        currentPage: json["current_page"],
        lastPage: json["last_page"],
        perPage: json["per_page"],
        excellenceAwards: List<ExcellenceAward>.from(
            json["data"].map((x) => ExcellenceAward.fromJson(x))),
      );

  Map<String, dynamic> toJson() => {
        "total": total,
        "current_page": currentPage,
        "last_page": lastPage,
        "per_page": perPage,
        "data": List<dynamic>.from(excellenceAwards.map((x) => x.toJson())),
      };
}

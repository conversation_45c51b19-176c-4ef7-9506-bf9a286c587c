class SlideDataResponse {
  String? slideText;
  bool? slideCoffetteEnabled;

  SlideDataResponse({
    this.slideText,
    this.slideCoffetteEnabled,
  });

  factory SlideDataResponse.fromJson(Map<String, dynamic> json) =>
      SlideDataResponse(
        slideText: json["slide_text"],
        slideCoffetteEnabled: json["slide_coffette_enabled"],
      );

  Map<String, dynamic> toJson() => {
        "slide_text": slideText,
        "slide_coffette_enabled": slideCoffetteEnabled,
      };
}

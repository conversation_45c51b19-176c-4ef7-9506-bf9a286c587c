import '../user.dart';

class LoginResponse {
  LoginResponse({
    required this.message,
    required this.user,
    required this.token,
  });

  String message;
  User user;
  String token;

  factory LoginResponse.fromJson(Map<String, dynamic> json) {
    // التحقق من وجود البيانات في المكان الصحيح
    final userData = json["data"] ?? json["user"] ?? {};

    return LoginResponse(
      message: json["message"] ?? "",
      user: User.fromJson(userData is Map<String, dynamic> ? userData : {}),
      token: json["token"] ?? "",
    );
  }

  Map<String, dynamic> toJson() => {
        "message": message,
        "data": user.toJson(),
        "token": token,
      };
}

class OccasionCountResponse {
  int futureCount;
  int pastCount;
  OccasionCountResponse({
    required this.futureCount,
    required this.pastCount,
  });

  factory OccasionCountResponse.fromJson(Map<String, dynamic> json) =>
      OccasionCountResponse(
        futureCount: json["future_count"],
        
        pastCount: json["past_count"],
      );

  Map<String, dynamic> toJson() => {
        "future_count": futureCount,
        "past_count": pastCount,
      };
}

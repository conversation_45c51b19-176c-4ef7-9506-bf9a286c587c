class AboutFamilyResponse {
  AboutFamilyResponse({
    required this.about,
    required this.images,
    required this.youtubeVideos,
    required this.shareLink,
  });

  String about;
  List<String> images;
  List<String> youtubeVideos;
  String shareLink;

  factory AboutFamilyResponse.fromJson(Map<String, dynamic> json) =>
      AboutFamilyResponse(
        about: json["about"],
        images: List<String>.from(json["images"].map((x) => x)),
        youtubeVideos: List<String>.from(json["youtube_videos"].map((x) => x)),
        shareLink: json["share_link"],
      );

  Map<String, dynamic> toJson() => {
        "about": about,
        "images": List<dynamic>.from(images.map((x) => x)),
        "youtube_videos": List<dynamic>.from(youtubeVideos.map((x) => x)),
        "share_link": shareLink,
      };
}

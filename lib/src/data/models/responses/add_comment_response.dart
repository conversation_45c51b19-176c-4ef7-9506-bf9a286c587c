import 'package:almashal/src/data/models/comment.dart';

class AddCommentResponse {
  String message;
  Comment data;

  AddCommentResponse({
    required this.message,
    required this.data,
  });

  factory AddCommentResponse.fromJson(Map<String, dynamic> json) =>
      AddCommentResponse(
        message: json["message"],
        data: Comment.fromJson(json["data"]),
      );

  Map<String, dynamic> toJson() => {
        "message": message,
        "data": data.toJson(),
      };
}

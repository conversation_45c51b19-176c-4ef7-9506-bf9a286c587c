class Media {
  Media({
    required this.type,
    required this.media,
    this.title,
    this.detail,
    this.images,
  });

  int type;
  String media;
  String? title;
  String? detail;
  List<String>? images;

  factory Media.fromJson(Map<String, dynamic> json) => Media(
        type: json["type"],
        media: json["media"],
        title: json["title"],
        detail: json["detail"],
        images: List<String>.from(json["images"].map((x) => x)),
      );

  Map<String, dynamic> toJson() => {
        "type": type,
        "media": media,
        "title": title,
        "detail": detail,
      };
}

import 'package:get/get.dart';
import 'package:dio/dio.dart';

class ErrorBag extends GetxService {
  Future<ErrorBag> init() async {
    return this;
  }

  static ErrorBag instance = Get.find<ErrorBag>();

  Rx<Map<String, dynamic>> validationError = Rx<Map<String, dynamic>>({});

  RxBool cleared = RxBool(false);

  void clear() {
    validationError.value = {};
    cleared.value = true;
  }

  void setErrors(Map<String, dynamic> errors) {
    validationError.value = errors;
    cleared.value = false;
  }

  Map<String, dynamic> getErrors() {
    cleared.value = true;
    return validationError.value;
  }
}

class ErrorService extends GetxService {
  final _errorBag = Get.find<ErrorBag>();

  static var instance;

  // تفعيل أو تعطيل الإشعارات لأخطاء تبويبات الملف الشخصي
  // يمكن تغييره حسب الحاجة
  final RxBool silentProfileErrors = RxBool(true);

  void handleError(dynamic error) {
    // تجاهل أخطاء محددة من API في صفحة الملف الشخصي
    if (silentProfileErrors.value && _shouldSuppressProfileError(error)) {
      print('تم تجاهل خطأ API في الملف الشخصي: $error');
      return;
    }

    print('Error occurred: $error');
    Get.snackbar(
      'خطأ',
      error.toString(),
      snackPosition: SnackPosition.BOTTOM,
    );

    // If it's a validation error, set it in ErrorBag
    if (error is Map<String, dynamic>) {
      _errorBag.setErrors(error);
    }
  }

  // فحص ما إذا كان يجب تجاهل خطأ الملف الشخصي
  bool _shouldSuppressProfileError(dynamic error) {
    // التحقق إذا كان الخطأ من نوع DioException مع كود 404
    if (error is DioException && error.response?.statusCode == 404) {
      final requestPath = error.requestOptions.path;

      // قائمة بمسارات API المتعلقة بالملف الشخصي
      final profilePaths = [
        '/api/user/skills',
        '/api/user/experiences',
        '/api/user/achievements',
        '/api/user/cvs',
        '/api/user/overview'
      ];

      // التحقق إذا كان الخطأ متعلق بأحد مسارات الملف الشخصي
      return profilePaths.any((path) => requestPath.contains(path));
    }

    return false;
  }
}

import 'dart:convert';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:flutter_secure_storage/flutter_secure_storage.dart';
import 'package:get_storage/get_storage.dart';
import '../../core/utils/platform_helper.dart';

/// خدمة التخزين المحلي - تدعم الويب والموبايل
class StorageService {
  static final StorageService _instance = StorageService._internal();
  factory StorageService() => _instance;
  StorageService._internal();

  // Instance of secure storage for mobile
  final FlutterSecureStorage _secureStorage = const FlutterSecureStorage();
  // GetStorage for web and as a fallback
  final GetStorage _localStorage = GetStorage();

  /// تخزين قيمة مع مفتاح
  Future<void> saveData(String key, String value) async {
    if (PlatformHelper.isWeb) {
      // استخدام GetStorage للويب
      await _localStorage.write(key, value);
    } else {
      try {
        // استخدام التخزين الآمن للموبايل
        await _secureStorage.write(key: key, value: value);
      } catch (e) {
        // في حالة الفشل، استخدم GetStorage كحل بديل
        await _localStorage.write(key, value);
      }
    }
  }

  /// استرجاع قيمة مرتبطة بمفتاح معين
  Future<String?> getData(String key) async {
    if (PlatformHelper.isWeb) {
      // استخدام GetStorage للويب
      return _localStorage.read(key);
    } else {
      try {
        // استخدام التخزين الآمن للموبايل
        return await _secureStorage.read(key: key);
      } catch (e) {
        // في حالة الفشل، استخدم GetStorage كحل بديل
        return _localStorage.read(key);
      }
    }
  }

  /// حذف قيمة مرتبطة بمفتاح معين
  Future<void> deleteData(String key) async {
    if (PlatformHelper.isWeb) {
      // استخدام GetStorage للويب
      await _localStorage.remove(key);
    } else {
      try {
        // استخدام التخزين الآمن للموبايل
        await _secureStorage.delete(key: key);
      } catch (e) {
        // في حالة الفشل، استخدم GetStorage كحل بديل
        await _localStorage.remove(key);
      }
    }
  }

  /// حذف جميع البيانات المخزنة
  Future<void> deleteAll() async {
    if (PlatformHelper.isWeb) {
      // استخدام GetStorage للويب
      await _localStorage.erase();
    } else {
      try {
        // استخدام التخزين الآمن للموبايل
        await _secureStorage.deleteAll();
        // أيضًا مسح GetStorage للتأكد من تنظيف البيانات المخزنة كنسخة احتياطية
        await _localStorage.erase();
      } catch (e) {
        // في حالة الفشل، استخدم GetStorage كحل بديل
        await _localStorage.erase();
      }
    }
  }

  /// حفظ كائن JSON
  Future<void> saveObject(String key, Map<String, dynamic> value) async {
    String jsonString = jsonEncode(value);
    await saveData(key, jsonString);
  }

  /// استرجاع كائن JSON
  Future<Map<String, dynamic>?> getObject(String key) async {
    String? jsonString = await getData(key);
    if (jsonString == null || jsonString.isEmpty) {
      return null;
    }
    try {
      return jsonDecode(jsonString) as Map<String, dynamic>;
    } catch (e) {
      return null;
    }
  }

  /// حفظ قائمة من الكائنات
  Future<void> saveList(String key, List<dynamic> value) async {
    String jsonString = jsonEncode(value);
    await saveData(key, jsonString);
  }

  /// استرجاع قائمة من الكائنات
  Future<List<dynamic>?> getList(String key) async {
    String? jsonString = await getData(key);
    if (jsonString == null || jsonString.isEmpty) {
      return null;
    }
    try {
      return jsonDecode(jsonString) as List<dynamic>;
    } catch (e) {
      return null;
    }
  }

  /// تخزين قيمة بشكل متزامن (فقط للويب أو باستخدام GetStorage)
  void saveDataSync(String key, String value) {
    _localStorage.write(key, value);
  }

  /// استرجاع قيمة بشكل متزامن (فقط للويب أو باستخدام GetStorage)
  String? getDataSync(String key) {
    return _localStorage.read(key);
  }
}

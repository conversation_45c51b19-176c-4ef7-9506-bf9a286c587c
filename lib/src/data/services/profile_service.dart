import 'dart:io';
import 'package:almashal/src/core/utils/common_functions.dart';
import 'package:almashal/src/core/values/app_config.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';
import 'package:dio/dio.dart' as dio;
import 'package:url_launcher/url_launcher.dart';
import '../providers/api/api_provider.dart';
import '../models/profile/achievement.dart';
import '../models/profile/cv.dart';
import '../models/profile/experience.dart';
import '../models/profile/skill.dart';
import 'auth_service.dart';

class ProfileService extends GetxService {
  final ApiProvider _apiProvider;
  final _dio = dio.Dio();

  ProfileService(this._apiProvider);

  ApiProvider get authenticatedApiProvider =>
      AppConfig.authenticatedApiProvider;

  ApiProvider get apiProvider => AppConfig.authenticatedApiProvider;

  // Observable variables
  final skills = <Skill>[].obs;
  final experiences = <Experience>[].obs;
  final achievements = <Achievement>[].obs;
  final cvs = <CV>[].obs;
  final Map<String, List<Skill>> _userSkillsMap = <String, List<Skill>>{}.obs;
  final Map<String, List<Experience>> _userExperiencesMap =
      <String, List<Experience>>{}.obs;
  final Map<String, List<Achievement>> _userAchievementsMap =
      <String, List<Achievement>>{}.obs;
  final Map<String, List<CV>> _userCVsMap = <String, List<CV>>{}.obs;
  final isLoading = false.obs;
  final currentUserId = RxString('');

  // Cache control
  final _cacheTimeout = const Duration(minutes: 5);
  final Map<String, DateTime> _lastSkillsUpdateMap = {};
  final Map<String, DateTime> _lastExperiencesUpdateMap = {};
  final Map<String, DateTime> _lastAchievementsUpdateMap = {};
  final Map<String, DateTime> _lastCVsUpdateMap = {};

  // Skills methods
  Future<void> loadSkills({String? userId, bool forceRefresh = false}) async {
    final targetUserId =
        userId ?? AuthService.instance.userData.value?.user.id.toString() ?? '';
    currentUserId.value = targetUserId;

    if (!forceRefresh &&
        _lastSkillsUpdateMap.containsKey(targetUserId) &&
        DateTime.now().difference(_lastSkillsUpdateMap[targetUserId]!) <
            _cacheTimeout) {
      if (_userSkillsMap.containsKey(targetUserId)) {
        skills.value = _userSkillsMap[targetUserId] ?? [];
        return;
      }
    }

    isLoading.value = true;
    try {
      final response = targetUserId ==
              AuthService.instance.userData.value?.user.id.toString()
          ? await authenticatedApiProvider.getSkills(null)
          : await authenticatedApiProvider.getSkills(int.parse(targetUserId));

      _userSkillsMap[targetUserId] = response;
      skills.value = response;
      _lastSkillsUpdateMap[targetUserId] = DateTime.now();
    } catch (e) {
      CommonFunctions.handleError(e);
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> addSkill(String title, String description, int level) async {
    try {
      final skill = await authenticatedApiProvider.addSkill({
        'name': title,
        'description': description,
        'level': level,
      });
      // Get.back();
      CommonFunctions.showSuccessMessage(skill.message);
      await loadSkills(forceRefresh: true);
    } catch (e) {
      CommonFunctions.handleError(e);
      rethrow;
    }
  }

  Future<void> updateSkill(
      String id, String title, String description, int level) async {
    try {
      final skill = await authenticatedApiProvider.updateSkill(id, {
        'name': title,
        'description': description,
        'level': level,
      });
      // Get.back();
      CommonFunctions.showSuccessMessage(skill.message);
      await loadSkills(forceRefresh: true);
    } catch (e) {
      CommonFunctions.handleError(e);
      rethrow;
    }
  }

  Future<void> deleteSkill(String id) async {
    try {
      final response = await authenticatedApiProvider.deleteSkill(id);
      CommonFunctions.showSuccessMessage(response.message);
      await loadSkills(forceRefresh: true);
    } catch (e) {
      CommonFunctions.handleError(e);
      rethrow;
    }
  }

  // Experiences methods
  Future<void> loadExperiences(
      {String? userId, bool forceRefresh = false}) async {
    final targetUserId =
        userId ?? AuthService.instance.userData.value?.user.id.toString() ?? '';
    currentUserId.value = targetUserId;

    if (!forceRefresh &&
        _lastExperiencesUpdateMap.containsKey(targetUserId) &&
        DateTime.now().difference(_lastExperiencesUpdateMap[targetUserId]!) <
            _cacheTimeout) {
      if (_userExperiencesMap.containsKey(targetUserId)) {
        experiences.value = _userExperiencesMap[targetUserId] ?? [];
        return;
      }
    }

    isLoading.value = true;
    try {
      final response = targetUserId ==
              AuthService.instance.userData.value?.user.id.toString()
          ? await apiProvider.getExperiences(null)
          : await apiProvider.getExperiences(int.parse(targetUserId));
      _userExperiencesMap[targetUserId] = response;
      experiences.value = response;
      _lastExperiencesUpdateMap[targetUserId] = DateTime.now();
    } catch (e) {
      CommonFunctions.handleError(e);
      rethrow;
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> addExperience({
    required String title,
    required String organization,
    required DateTime startDate,
    DateTime? endDate,
    required String description,
    required String type,
    String? location,
    required bool isCurrentPosition,
  }) async {
    try {
      final data = {
        'job_title': title,
        'company_name': organization,
        'start_date': startDate.toIso8601String(),
        'end_date': endDate?.toIso8601String(),
        'description': description,
        'type': type,
        'location': location,
        'is_current_position': isCurrentPosition,
      };

      final response = await apiProvider.addExperience(data);

      // final experience =
      //     Experience.fromJson(response.data as Map<String, dynamic>);
      // experiences.add(experience);
      // return experience;
    } catch (e) {
      CommonFunctions.handleError(e);
      rethrow;
    }
  }

  Future<void> updateExperience({
    required int id,
    String? title,
    String? organization,
    DateTime? startDate,
    DateTime? endDate,
    String? description,
    String? type,
    String? location,
    bool? isCurrentPosition,
  }) async {
    try {
      final data = {
        if (title != null) 'job_title': title,
        if (organization != null) 'company_name': organization,
        if (startDate != null) 'start_date': startDate.toIso8601String(),
        if (endDate != null) 'end_date': endDate.toIso8601String(),
        if (description != null) 'description': description,
        if (type != null) 'type': type,
        if (location != null) 'location': location,
        if (isCurrentPosition != null) 'is_current_position': isCurrentPosition,
      };

      final response = await apiProvider.updateExperience(id, data);
    } catch (e) {
      CommonFunctions.handleError(e);
      rethrow;
    }
  }

  Future<void> deleteExperience(int id) async {
    try {
      final response = await apiProvider.deleteExperience(id);
    } catch (e) {
      CommonFunctions.handleError(e);
      rethrow;
    }
  }

  // Achievements methods
  Future<void> loadAchievements(
      {String? userId, bool forceRefresh = false}) async {
    final targetUserId =
        userId ?? AuthService.instance.userData.value?.user.id.toString() ?? '';
    currentUserId.value = targetUserId;

    if (!forceRefresh &&
        _lastAchievementsUpdateMap.containsKey(targetUserId) &&
        DateTime.now().difference(_lastAchievementsUpdateMap[targetUserId]!) <
            _cacheTimeout) {
      if (_userAchievementsMap.containsKey(targetUserId)) {
        achievements.value = _userAchievementsMap[targetUserId] ?? [];
        return;
      }
    }

    isLoading.value = true;
    try {
      final response = targetUserId ==
              AuthService.instance.userData.value?.user.id.toString()
          ? await apiProvider.getUserAchievements()
          : await apiProvider.getUserAchievementsById(targetUserId);

      if (response.success) {
        _userAchievementsMap[targetUserId] = response.achievements;
        achievements.value = response.achievements;
        _lastAchievementsUpdateMap[targetUserId] = DateTime.now();
      } else {
        throw Exception(response.message);
      }
    } catch (e) {
      CommonFunctions.handleError(e);
      rethrow;
    } finally {
      isLoading.value = false;
    }
  }

  Future<Achievement> addAchievement(
    String title,
    String description, {
    required String type,
    required DateTime date,
    File? certificateFile,
    File? imageFile,
  }) async {
    try {
      final formData = dio.FormData.fromMap({
        'title': title,
        'description': description,
        'type': type,
        'date': date.toIso8601String(),
        if (certificateFile != null)
          'certificate': await dio.MultipartFile.fromFile(certificateFile.path),
        if (imageFile != null)
          'image': await dio.MultipartFile.fromFile(imageFile.path),
      });

      final response = await apiProvider.addAchievement(formData);
      if (!response.success) {
        throw Exception(response.message);
      }
      final achievement =
          Achievement.fromJson(response.data as Map<String, dynamic>);
      achievements.add(achievement);
      return achievement;
    } catch (e) {
      CommonFunctions.handleError(e);
      rethrow;
    }
  }

  Future<void> updateAchievement(
    String id, {
    String? title,
    String? description,
    String? type,
    DateTime? date,
    File? certificateFile,
    File? imageFile,
  }) async {
    try {
      final formData = dio.FormData.fromMap({
        if (title != null) 'title': title,
        if (description != null) 'description': description,
        if (type != null) 'type': type,
        if (date != null) 'date': date.toIso8601String(),
        if (certificateFile != null)
          'certificate': await dio.MultipartFile.fromFile(certificateFile.path),
        if (imageFile != null)
          'image': await dio.MultipartFile.fromFile(imageFile.path),
      });

      final response = await apiProvider.updateAchievement(id, formData);
      if (!response.success) {
        throw Exception(response.message);
      }
      final index = achievements.indexWhere((a) => a.id == id);
      if (index != -1) {
        achievements[index] =
            Achievement.fromJson(response.data as Map<String, dynamic>);
      }
    } catch (e) {
      CommonFunctions.handleError(e);
      rethrow;
    }
  }

  Future<void> deleteAchievement(String id) async {
    try {
      final response = await apiProvider.deleteAchievement(id);
      if (!response.success) {
        throw Exception(response.message);
      }
      achievements.removeWhere((a) => a.id == id);
    } catch (e) {
      CommonFunctions.handleError(e);
      rethrow;
    }
  }

  // CVs methods
  Future<void> loadCVs({String? userId, bool forceRefresh = false}) async {
    final targetUserId =
        userId ?? AuthService.instance.userData.value?.user.id.toString() ?? '';
    currentUserId.value = targetUserId;

    if (!forceRefresh &&
        _lastCVsUpdateMap.containsKey(targetUserId) &&
        DateTime.now().difference(_lastCVsUpdateMap[targetUserId]!) <
            _cacheTimeout) {
      if (_userCVsMap.containsKey(targetUserId)) {
        cvs.value = _userCVsMap[targetUserId] ?? [];
        return;
      }
    }

    isLoading.value = true;
    try {
      final response = targetUserId ==
              AuthService.instance.userData.value?.user.id.toString()
          ? await apiProvider.getUserCVs()
          : await apiProvider.getUserCVsById(targetUserId);

      if (response.success) {
        _userCVsMap[targetUserId] = response.cvs;
        cvs.value = response.cvs;
        _lastCVsUpdateMap[targetUserId] = DateTime.now();
      } else {
        throw Exception(response.message);
      }
    } catch (e) {
      CommonFunctions.handleError(e);
      rethrow;
    } finally {
      isLoading.value = false;
    }
  }

  Future<void> uploadCV(
    {required String type,
      String? file,String? text}) async {
    try {



      final formData = dio.FormData.fromMap({
      'cv_type':type,
        if (type  != "text") 'cv_file':file==null?null: await dio.MultipartFile.fromFile(file ),
          if (type == "text") 'text': text,
      });

      await apiProvider.updateCv(formData);
      // await loadCVs(forceRefresh: true);
    } catch (e) {
      CommonFunctions.handleError(e);
    }
  }

  Future<void> updateCVSettings(
      String cvId, bool isPublic, bool isDefault) async {
    try {
      if (isDefault) {
        await authenticatedApiProvider.makeDefaultCV(cvId);
      }
      await authenticatedApiProvider
          .updateCVPrivacy(cvId, {'is_public': isPublic});
      await loadCVs(forceRefresh: true);
    } catch (e) {
      CommonFunctions.handleError(e);
    }
  }

  Future<void> deleteCV(String cvId) async {
    try {
      await authenticatedApiProvider.deleteCV(cvId);
      await loadCVs(forceRefresh: true);
    } catch (e) {
      CommonFunctions.handleError(e);
    }
  }

  Future<void> openCVInBrowser(CV cv) async {
    try {
      // Check if the URL is valid
      if (cv.fileUrl.isEmpty) {
        throw Exception('رابط الملف غير صالح');
      }

      // Show a loading snackbar
      Get.snackbar(
        'جاري الفتح',
        'يتم فتح الملف في المتصفح...',
        snackPosition: SnackPosition.BOTTOM,
        duration: const Duration(seconds: 2),
      );

      // Use url_launcher to open the URL in browser
      final Uri url = Uri.parse(cv.fileUrl);
      if (await canLaunchUrl(url)) {
        await launchUrl(url, mode: LaunchMode.externalApplication);

        Get.snackbar(
          'تم الفتح',
          'تم فتح الملف في المتصفح',
          snackPosition: SnackPosition.BOTTOM,
          duration: const Duration(seconds: 2),
        );
      } else {
        throw Exception('لا يمكن فتح الرابط: ${cv.fileUrl}');
      }
    } catch (e) {
      Get.snackbar(
        'خطأ',
        'حدث خطأ أثناء فتح الملف: ${e.toString()}',
        snackPosition: SnackPosition.BOTTOM,
        backgroundColor: Colors.red,
        colorText: Colors.white,
      );

      // Log the error
      Get.log('Error opening CV in browser: $e');
    }
  }

  // This method is no longer used

  // Privacy methods
  Future<void> updatePrivacySettings(Map<String, bool> settings) async {
    try {
      final response =
          await authenticatedApiProvider.updatePrivacySettings(settings);
      if (response.success) {
        // Refresh all data after privacy update
        await Future.wait([
          loadSkills(forceRefresh: true),
          loadExperiences(forceRefresh: true),
          loadAchievements(forceRefresh: true),
          loadCVs(forceRefresh: true),
        ]);
      }
    } catch (e) {
      CommonFunctions.handleError(e);
    }
  }

  // Initialize service
  Future<ProfileService> init() async {
    await Future.wait([
      loadSkills(),
      loadExperiences(),
      loadAchievements(),
      loadCVs(),
    ]);
    return this;
  }
}

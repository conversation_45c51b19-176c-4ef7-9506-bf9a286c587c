import 'dart:convert';
import 'dart:io';

import 'package:almashal/src/core/routes/app_pages.dart';
import 'package:almashal/src/core/utils/platform_helper.dart';
import 'package:almashal/src/data/models/branch.dart';
import 'package:almashal/src/data/models/city.dart';
import 'package:almashal/src/data/models/country.dart';
import 'package:almashal/src/data/models/social_media_link.dart';
import 'package:almashal/src/data/models/vms/message_vm.dart';
import 'package:almashal/src/data/models/vms/user_token_vm.dart';
import 'package:almashal/src/data/models/vms/user_vm.dart';
import 'package:almashal/src/data/providers/api/api_provider.dart';
import 'package:dio/dio.dart' as dio;
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:http_parser/http_parser.dart';
import 'package:get_storage/get_storage.dart';
import 'package:flutter/material.dart';

import '../../core/utils/common_functions.dart';
import '../../core/values/app_config.dart';
import 'network_service.dart';

// إنشاء نسخة مبسطة من StorageService مباشرة هنا
class StorageService {
  final GetStorage _localStorage = GetStorage();

  Future<void> saveData(String key, String value) async {
    await _localStorage.write(key, value);
  }

  Future<String?> getData(String key) async {
    return _localStorage.read(key);
  }

  Future<void> deleteData(String key) async {
    await _localStorage.remove(key);
  }
}

class AuthService extends GetxService {
  var apiProvider = AppConfig.apiProvider;
  Rx<UserTokenVM?> userData = Rx<UserTokenVM?>(null);
  Rx<List<Country>> countries = Rx<List<Country>>([]);
  Rx<List<City>> cities = Rx<List<City>>([]);
  Rx<List<Branch>> branches = Rx<List<Branch>>([]);
  Rx<List<SocialMediaLink>> socialMediaLinks = Rx<List<SocialMediaLink>>([]);
  Rx<Map<String, dynamic>> validationError = Rx<Map<String, dynamic>>({});

  var slideTex = Rx<String?>(null);
  var slideCoffetteEnabled = Rx<bool>(false);

  // استخدام النسخة المبسطة من خدمة التخزين
  final StorageService _storageService = StorageService();

  // instance
  static AuthService instance = Get.find<AuthService>();
  Future<AuthService> init() async {
    await getUserData();
    await getProfile();
    getBranches();
    getCountries();
    getSocialMediaLinks();
    getSlideData();
    return this;
  }

  bool isAuthenticated() {
    return userData.value != null;
  }

  /// تسجيل الدخول باستخدام البريد الإلكتروني أو رقم الجوال
  ///
  /// يقوم بتحديد نوع المعرف (بريد إلكتروني أو رقم جوال) تلقائياً
  /// ويرسل معلومات الجهاز المطلوبة (fcm_token, device_id, device_type)
  Future<void> login(String identifier, String password) async {
    await NetworkService.instance.checkConnectivity(
      () async {
        try {
          Map<String, dynamic> loginData = {
            'mobile': identifier,
            'password': password,
            'fcm_token': await getFcmToken(),
            'device_id': await CommonFunctions.getDeviceId(),
            'device_type': _getDeviceTypeCode(),
          };

          var response = await apiProvider.login(loginData);
          userData.value =
              UserTokenVM(user: response.user, token: response.token);
          storeUserData(
              UserTokenVM(user: response.user, token: response.token));
          Get.back();
          CommonFunctions.showSuccessMessage(response.message);
          if (kDebugMode) {
            print(response.toJson());
          }
        } catch (err) {
          CommonFunctions.handleError(err);
        }
      },
      () {
        CommonFunctions.showErrorMessage('عذراً .. انت غير متصل بالانترنت');
      },
    );
  }

  // تحديد رمز نوع الجهاز (1: أندرويد، 2: iOS، 3: ويب)
  int _getDeviceTypeCode() {
    if (PlatformHelper.isWeb) return 3;
    if (PlatformHelper.isAndroid) return 1;
    if (PlatformHelper.isIOS) return 2;
    return 3; // الويب كقيمة افتراضية
  }

  Future<void> logout() async {
    await NetworkService.instance.checkConnectivity(
      () async {
        try {
          // الاحتفاظ بنسخة من توكن المصادقة في حال تم محو الكائن الأصلي
          final token = userData.value?.token;

          // استخدام مزود API المصادق عليه
          // إذا كانت البيانات قد تم تنظيفها من قبل، نستخدم ApiProvider مع توكن مخصص
          if (token != null) {
            var response = token.isNotEmpty
                ? await AppConfig.authenticatedApiProvider.logout()
                : await ApiProvider(
                    dio.Dio(
                      dio.BaseOptions(
                        baseUrl: AppConfig.apiUrl,
                        headers: {
                          "Content-Type": "application/json",
                          "Accept": "application/json",
                          'api-key': "NwaAi8q5SXQAu9P5X3bqSPGkakoI",
                          'Authorization': 'Bearer $token',
                        },
                      ),
                    ),
                  ).logout();

            // بعد نجاح عملية تسجيل الخروج، قم بتنظيف البيانات المحلية
            userData.value = null;
            deleteUserData();

            Get.back();
            CommonFunctions.showSuccessMessage(response.message);
            if (kDebugMode) {
              print(response.toJson());
            }
          } else {
            // إذا لم يكن هناك توكن، فقط قم بتنظيف البيانات
            userData.value = null;
            deleteUserData();
            Get.back();
          }
        } catch (err) {
          CommonFunctions.handleError(err);
        }
      },
      () {
        CommonFunctions.showErrorMessage('عذراً .. انت غير متصل بالانترنت');
      },
    );
  }

  Future<void> forgetPassword(String mobile) async {
    await NetworkService.instance.checkConnectivity(
      () async {
        try {
          var response = await apiProvider.forgetPassword({
            'mobile': mobile,
          });

          Get.back();
          Get.toNamed(Routes.RESET_PASSWORD_PAGE, arguments: mobile);
          CommonFunctions.showSuccessMessage(response.message);
          if (kDebugMode) {
            print(response.toJson());
          }
        } catch (err) {
          CommonFunctions.handleError(err);
        }
      },
      () {
        CommonFunctions.showErrorMessage('عذراً .. انت غير متصل بالانترنت');
      },
    );
  }

  Future<void> resetPassword(String mobile, String resetPasswordCode,
      String newPassword, String newPasswordConfirmation) async {
    await NetworkService.instance.checkConnectivity(
      () async {
        try {
          var response = await apiProvider.resetPassword({
            'mobile': mobile,
            'reset_password_code': resetPasswordCode,
            'new_password': newPassword,
            'new_password_confirmation': newPasswordConfirmation,
          });

          Get.back();
          Get.offAndToNamed(Routes.LOGIN_PAGE);
          CommonFunctions.showSuccessMessage(response.message);
          if (kDebugMode) {
            print(response.toJson());
          }
        } catch (err) {
          CommonFunctions.handleError(err);
        }
      },
      () {
        CommonFunctions.showErrorMessage('عذراً .. انت غير متصل بالانترنت');
      },
    );
  }

  /// تغيير كلمة المرور للمستخدم الحالي
  ///
  /// يتطلب تسجيل الدخول ويستخدم مزود API المصادق عليه
  Future<void> changePassword(String currentPassword, String newPassword,
      String newPasswordConfirmation) async {
    await NetworkService.instance.checkConnectivity(
      () async {
        try {
          // التأكد من أن المستخدم مسجل دخول
          if (userData.value == null || userData.value!.token.isEmpty) {
            CommonFunctions.showErrorMessage('يجب تسجيل الدخول أولاً');
            return;
          }

          var response =
              await AppConfig.authenticatedApiProvider.changePassword({
            'current_password': currentPassword,
            'new_password': newPassword,
            'new_password_confirmation': newPasswordConfirmation,
          });

          Get.back();
          CommonFunctions.showSuccessMessage(response.message);
          if (kDebugMode) {
            print(response.toJson());
          }
        } catch (err) {
          CommonFunctions.handleError(err);
        }
      },
      () {
        CommonFunctions.showErrorMessage('عذراً .. انت غير متصل بالانترنت');
      },
    );
  }

  Future<bool> register(UserVM userVM) async {
    bool success = false;
    await NetworkService.instance.checkConnectivity(
      () async {
        try {
          // تجهيز بيانات التسجيل مع معلومات الجهاز
          // استخراج البيانات من نموذج المستخدم
          var userData = userVM.toJsonForRegister();
          // حذف حقل البريد الإلكتروني من البيانات المرسلة
          // userData.remove('email'); // تم إزالة هذا السطر للحفاظ على توافق التطبيق

          var formData = {
            ...userData,
            'fcm_token': await getFcmToken(),
            'device_id': await CommonFunctions.getDeviceId(),
            'device_type': _getDeviceTypeCode(),
          };

          var response = await apiProvider.register(formData);
          Get.back();
          CommonFunctions.showSuccessMessage(response.message);
          success = true;
          if (kDebugMode) {
            print(response.toJson());
          }
        } catch (err) {
          CommonFunctions.handleError(err);
          success = false;
        }
      },
      () {
        CommonFunctions.showErrorMessage('عذراً .. انت غير متصل بالانترنت');
        success = false;
      },
    );
    return success;
  }

  Future<bool> checkRegistrationStatus(String mobile) async {
    bool isPending = false;
    bool isInactive = false;

    await NetworkService.instance.checkConnectivity(
      () async {
        try {
          var response = await apiProvider.checkRegistrationStatus({
            'mobile': mobile,
          });
          Get.back();
          CommonFunctions.showSuccessMessage(response.message);
          // إذا وصلنا إلى هنا، فإن الطلب موجود
          isPending = true;
          return;
        } catch (err) {
          // في حالة خطأ 403 (حساب غير مفعل)، نعتبر أن الحساب مسجل ولكن غير مفعل
          if (err is dio.DioException &&
              (err.response?.statusCode == 403 ||
                  err.response?.statusCode == 404)) {
            // نعرض رسالة خاصة بالحساب غير المفعل
            CommonFunctions.showErrorMessage(
                err.response?.data['message']?.toString() ?? 'حساب غير مفعل');
            return;
          }

          // معالجة الأخطاء الأخرى
          CommonFunctions.handleError(err);
          isPending = false;
          return;
        }
      },
      () {
        CommonFunctions.showErrorMessage('عذراً .. انت غير متصل بالانترنت');
        isPending = false;
        return;
      },
    );

    return isPending;
  }

  Future<void> updateProfile(UserVM userVM, dynamic image) async {
    if (kDebugMode) {
      print(userVM.toJson());
    }
    await NetworkService.instance.checkConnectivity(
      () async {
        try {
          dynamic formData;
          if (PlatformHelper.isWeb && image != null) {
            // معالجة الملفات في الويب
            List<int> imageBytes = await image.readAsBytes();
            formData = dio.FormData.fromMap({
              ...userVM.toJson(),
              "image": dio.MultipartFile.fromBytes(
                imageBytes,
                filename: 'image.jpg',
                contentType: MediaType('image', 'jpeg'),
              ),
            });
          } else {
            // معالجة الملفات في تطبيقات الموبايل
            if (image != null) {
              // استخدام FormData من مكتبة dio للموبايل
              formData = await userVM.toFormData(image);
            } else {
              formData = dio.FormData.fromMap(userVM.toJson());
            }
          }

          // استخدام AppConfig.authenticatedApiProvider بدلاً من إنشاء كائن جديد
          var response = await AppConfig.authenticatedApiProvider
              .updateProfile(formData, image is File ? image : null);

          CommonFunctions.showSuccessMessage(response.message);

          // تحديث بيانات المستخدم في الذاكرة
          userData.value = UserTokenVM(
            user: response.user,
            token: userData.value?.token ?? '',
          );

          // تخزين البيانات المحدثة
          storeUserData(userData.value!);
        } catch (err) {
          CommonFunctions.handleError(err);
        }
      },
      () {
        CommonFunctions.showErrorMessage('عذراً .. انت غير متصل بالانترنت');
      },
    );
  }

  /// تحديث صورة الغلاف فقط
  ///
  /// يستخدم نقطة وصول API مخصصة لتحديث صورة الغلاف فقط
  Future<void> updateProfileCover(File image) async {
    await NetworkService.instance.checkConnectivity(
      () async {
        try {
          // التأكد من أن المستخدم مسجل دخول
          if (!isAuthenticated()) {
            CommonFunctions.showErrorMessage('يجب تسجيل الدخول أولاً');
            return;
          }

          dynamic formData;
          if (PlatformHelper.isWeb) {
            // معالجة الملفات في الويب
            List<int> imageBytes = await image.readAsBytes();
            formData = dio.FormData.fromMap({
              "cover_image": dio.MultipartFile.fromBytes(
                imageBytes,
                filename: 'cover_image.jpg',
                contentType: MediaType('image', 'jpeg'),
              ),
            });
          } else {
            // معالجة الملفات في تطبيقات الموبايل
            formData = dio.FormData.fromMap({
              "cover_image": await dio.MultipartFile.fromFile(image.path),
            });
          }

          // استخدام النقطة العامة لتحديث البروفايل لكن مع صورة الغلاف فقط
          var response = await AppConfig.authenticatedApiProvider
              .updateProfileCover(formData);

          Get.back();
          CommonFunctions.showSuccessMessage(response.message ?? "");

          // تحديث بيانات المستخدم في الذاكرة
          userData.value?.user.coverImage = response.coverImage;
          userData.value = userData.value;

          // تخزين البيانات المحدثة
          storeUserData(userData.value!);
        } catch (err) {
          CommonFunctions.handleError(err);
        }
      },
      () {
        CommonFunctions.showErrorMessage('عذراً .. انت غير متصل بالانترنت');
      },
    );
  }

  // update cv
  Future<void> updateCV(File cv) async {
    await NetworkService.instance.checkConnectivity(
      () async {
        try {
          // التأكد من أن المستخدم مسجل دخول
          if (!isAuthenticated()) {
            CommonFunctions.showErrorMessage('يجب تسجيل الدخول أولاً');
            return;
          }

          dynamic formData;
          if (PlatformHelper.isWeb) {
            // معالجة الملفات في الويب
            List<int> imageBytes = await cv.readAsBytes();
            formData = dio.FormData.fromMap({
              "cv": dio.MultipartFile.fromBytes(
                imageBytes,
                filename: 'cv.jpg',
                contentType: MediaType('application', 'pdf'),
              ),
            });
          } else {
            // معالجة الملفات في تطبيقات الموبايل
            formData = dio.FormData.fromMap({
              "cv": await dio.MultipartFile.fromFile(cv.path),
            });
          }

          // استخدام النقطة العامة لتحديث البروفايل لكن مع صورة الغلاف فقط
          var response =
              await AppConfig.authenticatedApiProvider.updateCV(formData);

          Get.back();
          CommonFunctions.showSuccessMessage(response.message ?? "");

          // تحديث بيانات المستخدم في الذاكرة
          userData.value?.user.cvFile = response.cv;
          userData.value = userData.value;

          // تخزين البيانات المحدثة
          storeUserData(userData.value!);
        } catch (err) {
          CommonFunctions.handleError(err);
        }
      },
      () {
        CommonFunctions.showErrorMessage('عذراً .. انت غير متصل بالانترنت');
      },
    );
  }

  /// تحديث صورة الملف الشخصي فقط
  ///
  /// يستخدم نقطة وصول API مخصصة لتحديث الصورة فقط
  Future<void> updateProfileImage(dynamic image) async {
    await NetworkService.instance.checkConnectivity(
      () async {
        try {
          // التأكد من أن المستخدم مسجل دخول
          if (userData.value == null || userData.value!.token.isEmpty) {
            CommonFunctions.showErrorMessage('يجب تسجيل الدخول أولاً');
            return;
          }

          dynamic formData;
          if (PlatformHelper.isWeb && image != null) {
            // معالجة الملفات في الويب
            List<int> imageBytes = await image.readAsBytes();
            formData = dio.FormData.fromMap({
              "image": dio.MultipartFile.fromBytes(
                imageBytes,
                filename: 'image.jpg',
                contentType: MediaType('image', 'jpeg'),
              ),
            });
          } else if (image is File) {
            // معالجة الملفات في تطبيقات الموبايل
            formData = dio.FormData.fromMap({
              "image": await dio.MultipartFile.fromFile(image.path),
            });
          } else {
            CommonFunctions.showErrorMessage('صيغة الصورة غير صحيحة');
            return;
          }

          // استخدام النقطة الخاصة بتحديث الصورة فقط
          var response = await AppConfig.authenticatedApiProvider
              .updateProfileImage(formData);

          Get.back();
          CommonFunctions.showSuccessMessage(response.message);

          // تحديث بيانات المستخدم في الذاكرة
          userData.value = UserTokenVM(
            user: response.user,
            token: userData.value?.token ?? '',
          );

          // تخزين البيانات المحدثة
          storeUserData(userData.value!);
        } catch (err) {
          CommonFunctions.handleError(err);
        }
      },
      () {
        CommonFunctions.showErrorMessage('عذراً .. انت غير متصل بالانترنت');
      },
    );
  }

  Future<void> contact(MessageVM message) async {
    await NetworkService.instance.checkConnectivity(
      () async {
        try {
          var response = await apiProvider.contact(message.toJson());
          Get.back();
          CommonFunctions.showSuccessMessage(response.message);
          if (kDebugMode) {
            print(response.toJson());
          }
        } catch (err) {
          CommonFunctions.handleError(err);
        }
      },
      () {
        CommonFunctions.showErrorMessage('عذراً .. انت غير متصل بالانترنت');
      },
    );
  }

  Future<void> getBranches() async {
    await NetworkService.instance.checkConnectivity(
      () async {
        try {
          branches.value = await apiProvider.getBranches();
        } catch (err) {
          CommonFunctions.handleError(err);
        }
      },
      () {
        CommonFunctions.showErrorMessage('عذراً .. انت غير متصل بالانترنت');
      },
    );
  }

  Future<void> getCountries() async {
    await NetworkService.instance.checkConnectivity(
      () async {
        try {
          countries.value = await apiProvider.getCountries();
        } catch (err) {
          CommonFunctions.handleError(err);
        }
      },
      () {
        CommonFunctions.showErrorMessage('عذراً .. انت غير متصل بالانترنت');
      },
    );
  }

  Future<void> getCities(int countryId) async {
    await NetworkService.instance.checkConnectivity(
      () async {
        try {
          cities.value = await apiProvider.getCities(countryId);
          cities.refresh();
        } catch (err) {
          CommonFunctions.handleError(err);
        }
      },
      () {
        CommonFunctions.showErrorMessage('عذراً .. انت غير متصل بالانترنت');
      },
    );
  }

  // get social media links
  Future<void> getSocialMediaLinks() async {
    await NetworkService.instance.checkConnectivity(
      () async {
        try {
          socialMediaLinks.value = await apiProvider.getSocialMediaLinks();
        } catch (err) {
          CommonFunctions.handleError(err);
        }
      },
      () {
        CommonFunctions.showErrorMessage('عذراً .. انت غير متصل بالانترنت');
      },
    );
  }

  Future<void> getSlideData() async {
    await NetworkService.instance.checkConnectivity(
      () async {
        try {
          var response = await apiProvider.getSlideData();
          slideTex.value = response.slideText;
          slideCoffetteEnabled.value = response.slideCoffetteEnabled ?? false;
        } catch (err) {
          CommonFunctions.handleError(err);
        }
      },
      () {
        CommonFunctions.showErrorMessage('عذراً .. انت غير متصل بالانترنت');
      },
    );
  }

  Future<String?> getFcmToken() async {
    if (PlatformHelper.isWeb) {
      // يمكنك تنفيذ طريقة خاصة للحصول على FCM token في الويب
      // أو إرجاع قيمة افتراضية
      return 'web-fcm-token';
    }
    return await FirebaseMessaging.instance.getToken();
  }

  void storeUserData(UserTokenVM data) {
    _storageService.saveData('user_data', jsonEncode(data.toJson()));
  }

  void deleteUserData() {
    _storageService.deleteData('user_data');
  }

  Future<void> getUserData() async {
    try {
      var userData = await _storageService.getData('user_data');
      if (userData != null && userData.isNotEmpty) {
        this.userData.value = UserTokenVM.fromJson(jsonDecode(userData));
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error reading user data: $e');
      }
      // Reset user data in case of error
      userData.value = null;
    }
  }

  ///  Handle registration approval notification
  void handleRegistrationApproval(
      Map<String, dynamic> notificationData, bool isBackground) async {
    // Get registration data from notification
    String? messageText = notificationData['message'];
    String? mobileNumber = notificationData['mobile'];

    // التأكد من أن البيانات غير فارغة
    String mobile = mobileNumber ?? '';

    // حفظ رقم الجوال مؤقتاً لاستخدامه في صفحة تسجيل الدخول
    if (mobile.isNotEmpty) {
      StorageService().saveData('temp_approved_mobile', mobile);
    }

    // عرض رسالة للمستخدم إذا كان التطبيق في المقدمة
    if (!isBackground) {
      // Display success message with themed dialog
      Get.dialog(
        Dialog(
          shape: RoundedRectangleBorder(
            borderRadius: BorderRadius.circular(16),
          ),
          elevation: 0,
          backgroundColor: Colors.transparent,
          child: Container(
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              color: Colors.white,
              shape: BoxShape.rectangle,
              borderRadius: BorderRadius.circular(16),
              boxShadow: const [
                BoxShadow(
                  color: Colors.black26,
                  blurRadius: 10.0,
                  offset: Offset(0.0, 10.0),
                ),
              ],
            ),
            child: Column(
              mainAxisSize: MainAxisSize.min,
              children: [
                // Icon
                Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    color: Colors.green.withOpacity(0.1),
                    shape: BoxShape.circle,
                  ),
                  child: const Icon(
                    Icons.check_circle,
                    color: Colors.green,
                    size: 50,
                  ),
                ),
                const SizedBox(height: 20),

                // Title
                Text(
                  'تمت الموافقة على طلب التسجيل!',
                  style: Get.textTheme.titleLarge?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 12),

                // Message
                Text(
                  'مرحباً بك في تطبيق عائلة المشعل! يمكنك الآن تسجيل الدخول باستخدام رقم الجوال وكلمة المرور التي أدخلتها عند التسجيل.',
                  style: Get.textTheme.bodyMedium,
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),

                // Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      Get.back(); // Close dialog

                      // Navigate to login page
                      Get.offAllNamed(Routes.LOGIN_PAGE);
                    },
                    style: ElevatedButton.styleFrom(
                      padding: const EdgeInsets.symmetric(vertical: 12),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(8),
                      ),
                    ),
                    child: const Text(
                      'تسجيل الدخول',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      );
    } else {
      // التطبيق في الخلفية، أرسل إشعار عادي
      CommonFunctions.showSuccessMessage(
        'تمت الموافقة على طلب التسجيل!',
      );
    }
  }

  /// جلب ملف شخصي عام لمستخدم آخر
  Future<dynamic> getPublicProfile(String userId) async {
    try {
      // استخدام مزود API المصادق عليه لضمان إرسال توكن المصادقة
      final isAuthenticated = userData.value != null;
      final apiProvider = isAuthenticated
          ? AppConfig.authenticatedApiProvider
          : AppConfig.apiProvider;

      final response = await apiProvider.getProfile(userId);
      return response;
    } catch (e) {
      // استخدام CommonFunctions بدلاً من ErrorBag
      CommonFunctions.handleError(e);
      rethrow;
    }
  }

  Future<void> getProfile() async {
    // try {
      final isAuthenticated = userData.value != null;
      print("Strat get profile & isAuthenticated $isAuthenticated ${userData.value}");

      // if (!isAuthenticated) {
      //   return;
      // }
      final apiProvider = AppConfig.authenticatedApiProvider;

      final response = await apiProvider.getProfile();
      print("end get profile");
      userData.value?.user = response;
      storeUserData(userData.value!);
    // } catch (e) {

      // استخدام CommonFunctions بدلاً من ErrorBag
      // CommonFunctions.handleError(e);
    // }
  }
}

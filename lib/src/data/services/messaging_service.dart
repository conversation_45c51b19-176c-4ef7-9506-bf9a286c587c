import 'dart:io';

import 'package:almashal/src/core/routes/app_pages.dart';
import 'package:almashal/src/core/utils/common_functions.dart';
import 'package:almashal/src/core/values/app_config.dart';
import 'package:almashal/src/data/providers/api/api_provider.dart';
import 'package:almashal/src/data/services/auth_service.dart';
import 'package:almashal/src/data/services/network_service.dart';
import 'package:almashal/src/view/components/bottomsheets/notification_bottom_sheet.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';

class MessagingService extends GetxService {
  static MessagingService get instance => Get.find();
  Rx<String?> fcmToken = Rx<String?>(null);
  final FirebaseMessaging fcm = FirebaseMessaging.instance;
  Future<MessagingService> init() async {
    NotificationSettings settings = await fcm.requestPermission(
      alert: true,
      announcement: false,
      badge: true,
      carPlay: false,
      criticalAlert: false,
      provisional: false,
      sound: true,
    );

    debugPrint(
        'User granted notifications permission: ${settings.authorizationStatus}');

    // Retrieving the FCM token
    fcm.getToken().then((token) {
      fcmToken.value = token;
      registerDevice();
    });
    fcm.onTokenRefresh.listen((token) {
      debugPrint('refresh fcmToken: $fcmToken');
      fcmToken.value = token;
      registerDevice();
    });
    // Handling the initial message received when the app is launched from dead (killed state)
    // When the app is killed and a new notification arrives when user clicks on it
    // It gets the data to which screen to open
    FirebaseMessaging.instance.getInitialMessage().then((message) {
      if (message != null) {
        handleNotificationClick(message);
      }
    });
// Handling background messages using the specified handler
    FirebaseMessaging.onBackgroundMessage(firebaseMessagingBackgroundHandler);

    // Listening for incoming messages while the app is in the foreground
    FirebaseMessaging.onMessage.listen((RemoteMessage message) {
      debugPrint('Got a message whilst in the foreground!');
      debugPrint('Message data: ${message.notification!.title.toString()}');

      if (message.notification != null) {
        final notificationData = message.data;
        print(notificationData);
        final title = message.notification!.title;
        final body = message.notification!.body;
        Get.bottomSheet(
          NotificationBottomSheet(
            title: title,
            body: body,
            data: notificationData,
          ),
        );
      }
    });
    // Handling a notification click event when the app is in the background
    FirebaseMessaging.onMessageOpenedApp.listen((RemoteMessage message) {
      debugPrint(
          'onMessageOpenedApp: ${message.notification!.title.toString()}');
      handleNotificationClick(message);
    });
    return this;
  }

  void handleNotificationClick(RemoteMessage message) {
    final notificationData = message.data;

    if (notificationData.containsKey('screen')) {
      goToScreen(notificationData);
    }
  }

  Future<void> registerDevice() async {
    if (kDebugMode) {
      print("register device");
      print(await CommonFunctions.getDeviceId());
    }
    if (fcmToken.value != null) {
      NetworkService.instance.checkConnectivity(() async {
        // استخدام النسخة المصادق عليها من Dio إذا كان المستخدم مسجل الدخول
        final isAuthenticated = AuthService.instance.userData.value != null;
        final dioInstance = isAuthenticated
            ? AppConfig.authenticatedDioInstance
            : AppConfig.dioInstance;

        await ApiProvider(dioInstance).registerDevice({
          "fcm_token": fcmToken.value,
          'device_id': await CommonFunctions.getDeviceId(),
          "device_type": Platform.isAndroid ? 1 : 2,
          "user_id": AuthService.instance.userData.value?.user.id,
        });
      });
    }
  }

  Future<void> goToScreen(Map<String, dynamic>? data) async {
    if (data != null) {
      final screen = data['screen'];
      int? id = int.parse(data['id'].toString());
      if (screen != null) {
        dynamic item;
        switch (screen) {
          case 'album':
            item = await ApiProvider(AppConfig.dioInstance).getAlbumDetail(id);
            break;
          case 'news':
            item = await ApiProvider(AppConfig.dioInstance).getNewsDetail(id);
            break;
          case 'occasion':
            item =
                await ApiProvider(AppConfig.dioInstance).getOccasionDetail(id);
            break;
          case 'excellence-award':
            item = await ApiProvider(AppConfig.dioInstance)
                .getExcellenceAwardDetail(id);
            break;
        }
        Get.toNamed(
          {
                'album': Routes.ALBUM_DETAIL_PAGE,
                'news': Routes.NEWS_DETAIL_PAGE,
                'occasion': Routes.OCCASION_DETAIL_PAGE,
                'excellence-award': Routes.EXCELLENCE_AWARD_DETAIL_PAGE,
              }[screen] ??
              "",
          arguments: item,
        );
      }
    }
  }
}

Future<void> firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // If you're going to use other Firebase services in the background, such as Firestore,
  // make sure you call `initializeApp` before using other Firebase services.
  debugPrint('Handling a background message: ${message.notification!.title}');
}

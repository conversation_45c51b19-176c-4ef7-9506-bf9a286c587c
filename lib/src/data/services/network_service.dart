import 'package:connectivity_plus/connectivity_plus.dart';
import 'package:flutter/material.dart';
import 'package:get/get.dart';

import '../../core/utils/common_functions.dart';

class NetworkService extends GetxService {
  static NetworkService instance = Get.find();
  var connected = false.obs;

  Future<NetworkService> init() async {
    // التحقق من الاتصال مباشرة عند التهيئة
    connected.value = !(await Connectivity().checkConnectivity())
        .contains(ConnectivityResult.none);

    // إعداد المستمع للتغييرات في حالة الاتصال
    Connectivity().onConnectivityChanged.listen((result) {
      connected.value = !result.contains(ConnectivityResult.none);
    });

    return this;
  }

  @override
  Future<void> onInit() async {
    // تم نقل الكود إلى طريقة init
    super.onInit();
  }

  Future<void> checkConnectivity(Future Function() connectedCallback,
      [VoidCallback? notConnectedCallback]) async {
    var result = await Connectivity().checkConnectivity();
    if (result.contains(ConnectivityResult.none)) {
      if (notConnectedCallback == null) {
        CommonFunctions.showErrorMessage("انت غير متصل بالانترنت".tr);
      } else {
        notConnectedCallback();
      }
    } else {
      await connectedCallback();
    }
  }

  void addListener(Function(bool value) callback) {
    connected.listen(callback);
  }
}

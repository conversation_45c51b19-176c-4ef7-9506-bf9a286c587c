import 'dart:ui';

import 'package:flutter/material.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:get/get.dart';
import 'package:flutter/foundation.dart' show kIsWeb;
import 'package:phone_form_field/phone_form_field.dart';
import 'package:url_strategy/url_strategy.dart';

import 'core/routes/app_pages.dart';
import 'core/theme/app_theme.dart';
import 'core/utils/platform_helper.dart';

class App extends StatefulWidget {
  final ThemeData? theme;
  final ThemeData? darkTheme;

  const App({super.key, this.theme, this.darkTheme});

  @override
  State<App> createState() => _AppState();
}

class _AppState extends State<App> {
  @override
  void initState() {
    super.initState();

    // إزالة الهاش "#" من عناوين URL في متصفح الويب
    if (PlatformHelper.isWeb) {
      setPathUrlStrategy();
    }
  }

  @override
  Widget build(BuildContext context) {
    return GetMaterialApp(
      debugShowCheckedModeBanner: false,
      initialRoute: Routes.SPLASH_PAGE,
      theme: widget.theme ?? AppTheme.lightTheme,
      darkTheme: widget.darkTheme ?? AppTheme.darkTheme,
      themeMode: ThemeMode.light, // الوضع الافتراضي هو الوضع الفاتح
      defaultTransition: _getDefaultTransition(),
      transitionDuration: const Duration(milliseconds: 300),
      supportedLocales: const [
        Locale('ar'),
      ],
      fallbackLocale: const Locale('ar'),
      localizationsDelegates: [
        DefaultMaterialLocalizations.delegate,
        ...PhoneFieldLocalization.delegates,
      ],
      title: 'أسرة المشعل',
      getPages: AppPages.pages,
      locale: const Locale('ar'),
      textDirection: TextDirection.rtl,
      // تكوينات إضافية لتحسين تجربة المستخدم في الويب
      scrollBehavior: AppScrollBehavior(),
    );
  }

  /// الحصول على نوع الانتقال الافتراضي بناءً على المنصة
  Transition _getDefaultTransition() {
    // استخدام انتقالات أقل كثافة في الويب لتحسين الأداء
    if (PlatformHelper.isWeb) {
      return Transition.fadeIn;
    }
    // استخدام انتقالات أكثر حيوية في تطبيقات الموبايل
    return Transition.rightToLeftWithFade;
  }
}

/// سلوك التمرير المخصص لتحسين تجربة المستخدم في جميع المنصات
class AppScrollBehavior extends ScrollBehavior {
  @override
  Widget buildOverscrollIndicator(
      BuildContext context, Widget child, ScrollableDetails details) {
    // إزالة تأثير التمرير الزائد في المنصات غير iOS
    return child;
  }

  @override
  ScrollPhysics getScrollPhysics(BuildContext context) {
    // استخدام فيزياء التمرير الخاصة بـ iOS في جميع المنصات للحصول على تجربة أكثر سلاسة
    return const BouncingScrollPhysics();
  }

  @override
  Set<PointerDeviceKind> get dragDevices {
    // السماح بالسحب بالماوس على الويب
    return {
      PointerDeviceKind.touch,
      PointerDeviceKind.mouse,
      PointerDeviceKind.stylus,
      PointerDeviceKind.invertedStylus,
      PointerDeviceKind.trackpad,
    };
  }
}

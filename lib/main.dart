import 'dart:io';

import 'package:almashal/firebase_options.dart';
import 'package:almashal/src/core/utils/platform_helper.dart';
import 'package:almashal/src/data/services/auth_service.dart';
import 'package:almashal/src/data/services/error_service.dart';
import 'package:almashal/src/data/services/network_service.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:get/get.dart';
import 'package:get_storage/get_storage.dart';
import 'src/app.dart';
import 'src/core/di/service_locator.dart';
import 'src/custom_http_overrides.dart';
import 'src/core/theme/app_theme.dart';

Future<void> main() async {
  WidgetsFlutterBinding.ensureInitialized();

  // تهيئة Firebase بناءً على المنصة
  await Firebase.initializeApp(
    options: DefaultFirebaseOptions.currentPlatform,
  );

  // تجاوز مشكلات شهادة HTTP في غير الويب
  if (!PlatformHelper.isWeb) {
    HttpOverrides.global = CustomHttpOverrides();
  }

  // تهيئة التخزين المحلي
  await GetStorage.init();

  // تهيئة الخدمات
  ServiceLocator.init();

  // إعداد FCM للرسائل الخلفية (في غير منصة الويب)
  if (!PlatformHelper.isWeb) {
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
  } else {
    // إعدادات إضافية خاصة بالويب
    await _configureWebSpecificSettings();
  }
  Get.put(await ErrorBag().init());
  Get.put(await NetworkService().init());
  var service = Get.put(AuthService());
  await service.init();
  runApp(App(theme: AppTheme.lightTheme, darkTheme: AppTheme.darkTheme));
}

// إعدادات خاصة بالويب
Future<void> _configureWebSpecificSettings() async {
  if (kIsWeb) {
    // يمكن إضافة إعدادات إضافية خاصة بالويب هنا
    // مثل تكوين firebase messaging للويب، أو تكوين مواضيع خاصة بالويب

    // تحسين سرعة إطارات الرسم على الويب (اختياري)
    // await FlutterWindowManager.instance.setWindowSize(
    //   const Size(1200, 800),
    // );
  }
}

@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  // إذا كنت ستستخدم خدمات Firebase الأخرى في الخلفية، مثل Firestore،
  // تأكد من استدعاء `initializeApp` قبل استخدام خدمات Firebase الأخرى.
  await Firebase.initializeApp();

  if (kDebugMode) {
    print("معالجة رسالة خلفية: ${message.messageId}");
  }
}
